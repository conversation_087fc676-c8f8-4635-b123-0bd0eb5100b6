<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <!-- View -->
        <record id="view_view_form" model="ir.ui.view">
            <field name="model">ir.ui.view</field>
            <field name="arch" type="xml">
                <form string="Views">
                  <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="type"/>
                            <field name="model" invisible="True"/>
                            <field name="model_id"/>
                            <field name="priority"/>
                            <field name="active" widget="boolean_toggle"/>
                        </group>
                        <group groups="base.group_no_one">
                            <field name="inherit_id"/>
                            <field name="mode"/>
                            <field name="model_data_id"/>
                            <field name="xml_id" class="text-break"/>
                        </group>
                    </group>
                    <div class="oe_edit_only alert alert-info" role="alert">
                        Be aware that editing the architecture of a standard view is not advised, since the changes will be overwritten during future module updates.<br/>
                        We recommend applying modifications to standard views through inherited views or customization with Odoo Studio.
                    </div>
                    <notebook>
                        <page string="Architecture" name="architecture">
                            <field name="arch_db" class="oe_edit_only oe_no_translation_content"/>
                            <field name="arch_base" string="View Architecture" widget="code" options="{'mode': 'xml'}" required="1"/>
                        </page>
                        <page string="Access Rights" name="access_rights">
                            <field name="groups_id"/>
                        </page>
                        <page name="inherit_children" string="Inherited Views">
                            <field name="inherit_children_ids" context="{'default_model':model,'default_type':type,'default_inherit_id':id,'default_mode':'extension', 'active_test': False}">
                                <tree default_order="priority,id" decoration-muted="not active">
                                    <field name="active" column_invisible="True"/>
                                    <field name="id"/>
                                    <field name="priority"/>
                                    <field name="name"/>
                                    <field name="xml_id"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                  </sheet>
                </form>
            </field>
        </record>

        <record id="view_view_tree" model="ir.ui.view">
            <field name="model">ir.ui.view</field>
            <field name="arch" type="xml">
                <tree string="Views">
                    <field name="priority" string="Sequence" widget="handle"/>
                    <field name="name"/>
                    <field name="type"/>
                    <field name="model"/>
                    <field name="xml_id" groups="base.group_no_one"/>
                    <field name="inherit_id"/>
                </tree>
            </field>
        </record>

        <record id="view_view_search" model="ir.ui.view">
            <field name="model">ir.ui.view</field>
            <field name="arch" type="xml">
                <search string="Views">
                    <field name="name" filter_domain="['|', '|', ('name','ilike',self), ('model','ilike',self), ('model_data_id','ilike',self)]" string="View"/>
                    <field name="key"/>
                    <field name="model"/>
                    <field name="inherit_id"/>
                    <field name="type"/>
                    <field name="arch_db" string="View Architecture"/>
                    <filter string="Form" name="form" domain="[('type', '=','form')]"/>
                    <filter string="Tree" name="tree" domain="[('type', '=', 'tree')]"/>
                    <filter string="Kanban" name="kanban" domain="[('type', '=', 'kanban')]"/>
                    <filter string="Search" name="search" domain="[('type', '=', 'search')]"/>
                    <filter string="QWeb" name="qweb" domain="[('type', '=', 'qweb')]"/>
                    <separator/>
                    <filter string="Modified Architecture" name="arch_updated" domain="[('arch_updated', '=',True)]"/>
                    <separator/>
                    <filter string="Active" name="active" domain="[('active', '=',True)]"/>
                    <filter string="Inactive" name="inactive" domain="[('active', '=',False)]"/>
                    <group expand="0" string="Group By">
                        <filter string="Model" name="object" domain="[]" context="{'group_by':'model'}"/>
                        <filter string="Type" name="type" domain="[]" context="{'group_by':'type'}"/>
                        <filter string="Inherit" name="inherit" domain="[]" context="{'group_by':'inherit_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="action_ui_view" model="ir.actions.act_window">
            <field name="name">Views</field>
            <field name="res_model">ir.ui.view</field>
            <field name="view_id" ref="view_view_tree"/>
            <field name="context">{'search_default_active': 1}</field>
            <field name="help">Views allows you to personalize each view of Odoo. You can add new fields, move fields, rename them or delete the ones that you do not need.</field>
        </record>

        <menuitem action="action_ui_view" id="menu_action_ui_view" parent="base.next_id_2" sequence="2"/>

        <!-- Reset view wizard -->
        <record id="reset_view_arch_wizard_view" model="ir.ui.view">
            <field name="name">Reset View Architecture</field>
            <field name="model">reset.view.arch.wizard</field>
            <field name="arch" type="xml">
                <form string="Reset View Architecture">
                    <group>
                        <group>
                            <field name="has_diff" invisible="1"/>
                            <field name="view_id" invisible="1"/>
                            <field name="view_name"/>
                            <field name="compare_view_id" invisible="reset_mode != 'other_view'" options="{'no_create': True, 'no_open': True}"/>
                        </group>
                        <group>
                            <field name="reset_mode" widget="radio"/>
                        </group>
                    </group>
                    <field name="arch_diff" invisible="not arch_diff"/>
                    <div class="alert alert-warning my-2" role="alert" invisible="arch_diff">
                        <span invisible="reset_mode != 'soft'">This view has no previous version.</span>
                        <span invisible="reset_mode != 'hard'">This view is not coming from a file.</span>
                        <span invisible="reset_mode != 'other_view'">You need two views to compare.</span>
                    </div>
                    <footer>
                        <button string="Reset View" name="reset_view_button" type="object" class="btn-primary" invisible="not has_diff" data-hotkey="q"/>
                        <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="x" />
                    </footer>
                </form>
            </field>
        </record>

        <record id="reset_view_arch_wizard_action" model="ir.actions.act_window">
            <field name="name">Compare/Reset</field>
            <field name="res_model">reset.view.arch.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="binding_model_id" ref="model_ir_ui_view"/>
            <field name="binding_view_types">form,list</field>
        </record>

        <!-- View customizations -->
        <record id="view_view_custom_form" model="ir.ui.view">
            <field name="model">ir.ui.view.custom</field>
            <field name="arch" type="xml">
                <form string="Customized Views">
                  <sheet>
                    <group col="4">
                        <field name="user_id"/>
                        <field name="ref_id"/>
                        <separator string="View Architecture" colspan="4" />
                        <field name="arch" colspan="4" nolabel="1"/>
                    </group>
                   </sheet>
                </form>
            </field>
        </record>

        <record id="view_view_custom_tree" model="ir.ui.view">
            <field name="model">ir.ui.view.custom</field>
            <field name="arch" type="xml">
                <tree string="Customized Views">
                    <field name="user_id"/>
                    <field name="ref_id"/>
                </tree>
            </field>
        </record>

        <record id="view_view_custom_search" model="ir.ui.view">
            <field name="model">ir.ui.view.custom</field>
            <field name="arch" type="xml">
                <search string="Customized Views">
                    <field name="user_id"/>
                    <field name="ref_id"/>
                </search>
            </field>
        </record>

        <record id="action_ui_view_custom" model="ir.actions.act_window">
            <field name="name">Customized Views</field>
            <field name="res_model">ir.ui.view.custom</field>
            <field name="help" type="html"><p class="o_view_nocontent_smiling_face">Create a customized view</p><p>Customized views are used when users reorganize the content of their dashboard views (via web client)</p></field>
        </record>

        <menuitem id="menu_action_ui_view_custom" action="action_ui_view_custom" parent="base.next_id_2" sequence="3"/>

</odoo>
