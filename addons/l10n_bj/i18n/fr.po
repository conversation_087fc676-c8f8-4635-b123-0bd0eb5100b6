# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_bj
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-30 09:31+0000\n"
"PO-Revision-Date: 2023-11-30 09:31+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_sales_exempt
msgid "1. Exempted turnover"
msgstr "1. Opérations exonérées"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_deductible_additional
msgid "10. Additional deductions"
msgstr "10. Complément de déductions"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_deductible_repayment
msgid "11. Repayment to do"
msgstr "11. Reversement à effectuer"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_deductible_total
msgid "12. Total"
msgstr "12. Total"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_net_gross
msgid "13. Gross VAT (18% x l.4 + l.5)"
msgstr "13. TVA Brute (18% x l.4 + l.5)"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_net_deductible
msgid "14. VAT deductible"
msgstr "14. TVA Déductible"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_net_to_pay
msgid "15. Net VAT to pay"
msgstr "15. TVA Nette à payer"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_credit_to_report
msgid "16. Credit to report"
msgstr "16. Crédit à reporter"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_sales_export
msgid "2. Export of taxable products"
msgstr "2. Exportations des produits taxables"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_sales_export_non_taxable
msgid "3. Export of non-taxable products"
msgstr "3. Exportations de Produits non taxables"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_sales_taxable
msgid "4. Taxable operations"
msgstr "4. Opérations taxables"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_sales_self
msgid "5. Self Deliveries"
msgstr "5. Livraison à soi-même"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_sales_total_turnover
msgid "6. Total turnover without VAT"
msgstr "6. CA Total hors TVA"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_deductible_reported
msgid "7. Credit reported from last month"
msgstr "7. Crédit reporté du mois précédent"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_deductible_goods_services
msgid "8. Deduction on goods and services (without assets)"
msgstr "8. Déduction sur biens ne constituant pas des immobilisations et sur services"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_deductible_assets
msgid "9. Deduction on assets"
msgstr "9. Déduction sur biens constituant des immobilisations"

#. module: l10n_bj
#: model:ir.model,name:l10n_bj.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modèle de Plan Comptable"

#. module: l10n_bj
#: model:account.report.column,name:l10n_bj.account_tax_report_bj_balance
msgid "Balance"
msgstr "Solde"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_turnover
msgid "II. Turnover (Without Tax)"
msgstr "II. Chiffres d'affaires (hors TVA)"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_deductions
msgid "III. Deductions"
msgstr "III. Déductions"

#. module: l10n_bj
#: model:account.report.line,name:l10n_bj.account_tax_report_line_bj_net
msgid "IV. NET VAT"
msgstr "IV. TVA Nette"

#. module: l10n_bj
#. odoo-python
#: code:addons/l10n_bj/models/template_bj_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr "SYSCEBNL pour Associations"

#. module: l10n_bj
#. odoo-python
#: code:addons/l10n_bj/models/template_bj.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr "SYSCOHADA pour Sociétés"


#. module: l10n_bj
#: model:account.report,name:l10n_bj.account_tax_report_bj
msgid "VAT Report"
msgstr "Déclaration TVA"
