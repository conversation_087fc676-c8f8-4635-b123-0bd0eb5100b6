# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_sale
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# Константи<PERSON> <<EMAIL>>, 2023
# Wil Odoo, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "(left:"
msgstr "(слева:"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "(tax incl.)"
msgstr "(с учетом НДС)"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.report_invoice_document
msgid "<span style=\"margin: 0px 5px;\">:</span>"
msgstr "<span style=\"margin: 0px 5px;\">:</span>"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "A new order has been created."
msgstr "Был создан новый заказ."

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Apply a down payment (fixed amount)"
msgstr "Внести первоначальный взнос (фиксированная сумма)"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Apply a down payment (percentage)"
msgstr "Внести первоначальный взнос (в процентах)"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_control_panel/sale_order_management_control_panel.xml:0
#, python-format
msgid "Back"
msgstr "Назад"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.js:0
#, python-format
msgid "Cancelled"
msgstr "Отменен"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "Курс валют"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_list/sale_order_list.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "Customer"
msgstr "Клиент"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Customer loading error"
msgstr "Ошибка загрузки клиента"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_list/sale_order_list.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "Date"
msgstr "Дата"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.message_body
msgid "Delivered from"
msgstr "Доставлено из"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Do you want to load the SN/Lots linked to the Sales Order?"
msgstr "Хотите ли вы загрузить SN/Lots, связанные с заказом на продажу?"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Down Payment"
msgstr "Первоначальный взнос"

#. module: pos_sale
#: model:product.template,name:pos_sale.default_downpayment_product_product_template
msgid "Down Payment (POS)"
msgstr "Первоначальный взнос (POS)"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__down_payment_details
msgid "Down Payment Details"
msgstr "Детали первоначального взноса"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_config__down_payment_product_id
#: model:ir.model.fields,field_description:pos_sale.field_res_config_settings__pos_down_payment_product_id
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "Down Payment Product"
msgstr "Продукт для первоначального взноса"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Due balance: %s"
msgstr "Должный баланс: %s"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_control_panel/sale_order_management_control_panel.xml:0
#, python-format
msgid "E.g. customer: Steward, date: 2020-05-09"
msgstr "Например, заказчик: Стюард, дата: 2020-05-09"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Error amount too high"
msgstr "Ошибка: сумма слишком велика"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/overrides/components/receipt_screen/order_receipt/order_receipt.xml:0
#: model_terms:ir.ui.view,arch_db:pos_sale.report_invoice_document
#, python-format
msgid "From"
msgstr "От"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__invoiced
msgid "Invoiced"
msgstr "Счёт-фактура выставлена"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid ""
"It seems that you didn't configure a down payment product in your point of "
"sale. You can go to your point of sale configuration to choose one."
msgstr ""
"Похоже, что вы не настроили продукт для авансового платежа в вашей торговой "
"точке. Вы можете перейти к настройкам торговой точки, чтобы выбрать один из "
"них."

#. module: pos_sale
#. odoo-python
#: code:addons/pos_sale/models/sale_order.py:0
#, python-format
msgid "Linked POS Orders"
msgstr "Связанные POS-заказы"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__sale_order_origin_id
msgid "Linked Sale Order"
msgstr "Связанный заказ на продажу"

#. module: pos_sale
#. odoo-python
#: code:addons/pos_sale/models/pos_order.py:0
#, python-format
msgid "Linked Sale Orders"
msgstr "Связанные заказы на продажу"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.js:0
#, python-format
msgid "Locked"
msgstr "Заблокировано"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_control_panel/sale_order_management_control_panel.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_control_panel/sale_order_management_control_panel.xml:0
#, python-format
msgid "Next Order List"
msgstr "Список следующих заказов"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "No"
msgstr "Нет"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "No down payment product"
msgstr "Продукт без первоначального взноса"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_sessions_open_count
msgid "Open POS Sessions"
msgstr "Открытые POS-сессии"

#. module: pos_sale
#: model:ir.actions.act_window,name:pos_sale.pos_session_action_from_crm_team
msgid "Open Sessions"
msgstr "Открытые сессии"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_list/sale_order_list.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "Order"
msgstr "Заказ"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__pos_order_line_ids
#: model:ir.model.fields,field_description:pos_sale.field_sale_order_line__pos_order_line_ids
msgid "Order lines Transfered to Point of Sale"
msgstr "Строки заказов переданы в точку продаж"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__order_reference__pos_order
msgid "POS Order"
msgstr "POS-заказ"

#. module: pos_sale
#: model:ir.model.fields.selection,name:pos_sale.selection__sale_report__state__paid
msgid "Paid"
msgstr "Оплачено"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Конфигурация точки продаж"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "Линии заказов в точках продаж"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_order
msgid "Point of Sale Orders"
msgstr "Заказы в торговых точках"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_pos_session
msgid "Point of Sale Session"
msgstr "Сессия в торговой точке"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_config_ids
msgid "Point of Sales"
msgstr "Точка продаж"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__pos_order_count
msgid "Pos Order Count"
msgstr "Pos Количество Заказов"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_control_panel/sale_order_management_control_panel.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_control_panel/sale_order_management_control_panel.xml:0
#, python-format
msgid "Previous Order List"
msgstr "Предыдущий список заказов"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Products not available in POS"
msgstr "Продукты, недоступные в POS"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.js:0
#, python-format
msgid "Quotation"
msgstr "Коммерческое предложение"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.js:0
#, python-format
msgid "Quotation Sent"
msgstr "Коммерческое предложение отправлено"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/set_sale_order_button/set_sale_order_button.xml:0
#, python-format
msgid "Quotation/Order"
msgstr "Предложение/Заказ"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_report__order_reference
msgid "Related Order"
msgstr "Связанный заказ"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "SN/Lots Loading"
msgstr "SN/Lots Loading"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/overrides/components/product_screen/product_screen.xml:0
#: code:addons/pos_sale/static/src/overrides/components/product_screen/product_screen.xml:0
#, python-format
msgid "SO"
msgstr "SO"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__sale_order_count
msgid "Sale Order Count"
msgstr "Счетчик заказов на продажу"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "Sales"
msgstr "Продажи"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Отчет об анализе продаж"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.js:0
#: model:ir.model,name:pos_sale.model_sale_order
#, python-format
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Позиция заказа на продажу"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_crm_team
#: model:ir.model.fields,field_description:pos_sale.field_pos_config__crm_team_id
#: model:ir.model.fields,field_description:pos_sale.field_pos_order__crm_team_id
#: model:ir.model.fields,field_description:pos_sale.field_pos_session__crm_team_id
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr "Команда продаж"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_res_config_settings__pos_crm_team_id
msgid "Sales Team (PoS)"
msgstr "Отдел продаж (PoS)"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "Sales are reported to the following sales team"
msgstr "О продажах сообщается следующей команде продаж"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "Salesman"
msgstr "Продавец"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_list/sale_order_list.xml:0
#, python-format
msgid "Salesperson"
msgstr "Продавец"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_view_kanban_dashboard
msgid "Session Running"
msgstr "Сессия Выполняется"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_crm_team__pos_order_amount_total
msgid "Session Sale Amount"
msgstr "Сумма продажи сессии"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.crm_team_view_kanban_dashboard
msgid "Sessions Running"
msgstr "Сессии Выполняются"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/set_sale_order_button/set_sale_order_button.xml:0
#: code:addons/pos_sale/static/src/app/set_sale_order_button/set_sale_order_button.xml:0
#, python-format
msgid "Set Sale Order"
msgstr "Установить порядок продажи"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Settle the order"
msgstr "Урегулировать заказ"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid ""
"Some of the products in your Sale Order are not available in POS, do you "
"want to import them?"
msgstr ""
"Некоторые товары в вашем заказе на продажу не доступны в POS, хотите ли вы "
"их импортировать?"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_pos_order_line__sale_order_line_id
msgid "Source Sale Order Line"
msgstr "Линия заказов на продажу"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_list/sale_order_list.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "State"
msgstr "Область"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_report__state
msgid "Status"
msgstr "Статус"

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_sale_order__amount_unpaid
msgid "The amount due from the sale order."
msgstr "Сумма к оплате по ордеру на продажу."

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr "Курс валюты к валюте по курсу, действующему на дату заказа"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "There was a problem in loading the %s customer."
msgstr "Возникла проблема при загрузке %s клиента."

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_config__crm_team_id
#: model:ir.model.fields,help:pos_sale.field_pos_session__crm_team_id
#: model:ir.model.fields,help:pos_sale.field_res_config_settings__pos_crm_team_id
msgid "This Point of sale's sales will be related to this Sales Team."
msgstr "Продажи в этой торговой точке будут связаны с отделом продаж."

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.res_config_settings_view_form
msgid "This product will be applied when down payment is made"
msgstr "Этот продукт будет применен при внесении первоначального взноса"

#. module: pos_sale
#: model:ir.model.fields,help:pos_sale.field_pos_config__down_payment_product_id
#: model:ir.model.fields,help:pos_sale.field_res_config_settings__pos_down_payment_product_id
msgid "This product will be used as down payment on a sale order."
msgstr ""
"Этот товар будет использован в качестве первого взноса по заказу на продажу."

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_list/sale_order_list.xml:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_row/sale_order_row.xml:0
#, python-format
msgid "Total"
msgstr "Всего"

#. module: pos_sale
#: model:ir.model,name:pos_sale.model_stock_picking
msgid "Transfer"
msgstr "Перевод"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.view_pos_order_form_inherit_pos_sale
msgid ""
"Transfered<br/>\n"
"                                from Sale"
msgstr ""
"Перенесено<br/>\n"
"                                с продажи"

#. module: pos_sale
#: model_terms:ir.ui.view,arch_db:pos_sale.view_order_form_inherit_pos_sale
msgid ""
"Transfered<br/>\n"
"                                to POS"
msgstr ""
"Переведено<br/>\n"
"                                на POS"

#. module: pos_sale
#: model:ir.model.fields,field_description:pos_sale.field_sale_order__amount_unpaid
msgid "Unpaid Amount"
msgstr "Неоплаченная сумма"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "What do you want to do?"
msgstr "Каковы ваши действия?"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid "Yes"
msgstr "Да"

#. module: pos_sale
#. odoo-javascript
#: code:addons/pos_sale/static/src/app/order_management_screen/sale_order_management_screen/sale_order_management_screen.js:0
#, python-format
msgid ""
"You have tried to charge a down payment of %s but only %s remains to be "
"paid, %s will be applied to the purchase order line."
msgstr ""
"Вы попытались внести авансовый платеж в размере %s, но осталось оплатить "
"только %s, %s будет применено к строке заказа на поставку."
