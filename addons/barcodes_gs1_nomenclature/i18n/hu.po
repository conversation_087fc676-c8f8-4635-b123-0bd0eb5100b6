# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* barcodes_gs1_nomenclature
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# krnkris, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: krnkris, 2023\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__alpha
msgid "Alpha-Numeric Name"
msgstr ""

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_nomenclature__gs1_separator_fnc1
msgid ""
"Alternative regex delimiter for the FNC1. The separator must not match the "
"begin/end of any related rules pattern."
msgstr ""

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__associated_uom_id
msgid "Associated Uom"
msgstr ""

#. module: barcodes_gs1_nomenclature
#: model:ir.model,name:barcodes_gs1_nomenclature.model_barcode_nomenclature
msgid "Barcode Nomenclature"
msgstr "Vonalkód nómenklatúra"

#. module: barcodes_gs1_nomenclature
#: model:ir.model,name:barcodes_gs1_nomenclature.model_barcode_rule
msgid "Barcode Rule"
msgstr "Vonalkód szabály"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__use_date
msgid "Best before Date"
msgstr "Minőség megőrzésének dátuma"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__date
msgid "Date"
msgstr "Dátum"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__gs1_decimal_usage
msgid "Decimal"
msgstr "Decimális"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__location_dest
msgid "Destination location"
msgstr "Cél tárhely"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__encoding
msgid "Encoding"
msgstr "Kódolás"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__expiration_date
msgid "Expiration Date"
msgstr "Lejárat dátuma"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_nomenclature__gs1_separator_fnc1
msgid "FNC1 Separator"
msgstr ""

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__gs1_content_type
msgid "GS1 Content Type"
msgstr ""

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__encoding__gs1-128
msgid "GS1-128"
msgstr ""

#. module: barcodes_gs1_nomenclature
#: model:ir.model,name:barcodes_gs1_nomenclature.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP irányítás"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__gs1_decimal_usage
msgid ""
"If True, use the last digit of AI to determine where the first decimal is"
msgstr ""

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "Invalid barcode: can't be formated as date"
msgstr ""

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "Invalid barcode: the check digit is incorrect"
msgstr ""

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_nomenclature__is_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__is_gs1_nomenclature
msgid "Is GS1 Nomenclature"
msgstr ""

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__location
msgid "Location"
msgstr "Helyszín"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__lot
msgid "Lot"
msgstr "Lot"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__measure
msgid "Measure"
msgstr "Mérés"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__gs1_content_type__identifier
msgid "Numeric Identifier"
msgstr ""

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__pack_date
msgid "Pack Date"
msgstr "Csomag dátuma"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__package
msgid "Package"
msgstr "Csomag"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__package_type
msgid "Package Type"
msgstr "Csomag típus"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields.selection,name:barcodes_gs1_nomenclature.selection__barcode_rule__type__quantity
msgid "Quantity"
msgstr "Mennyiség"

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_nomenclature.py:0
#, python-format
msgid "The FNC1 Separator Alternative is not a valid Regex: "
msgstr ""

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__gs1_content_type
msgid ""
"The GS1 content type defines what kind of data the rule will process the "
"barcode as:        * Date: the barcode will be converted into a Odoo "
"datetime;        * Measure: the barcode's value is related to a specific "
"UoM;        * Numeric Identifier: fixed length barcode following a specific "
"encoding;        * Alpha-Numeric Name: variable length barcode."
msgstr ""

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_rule.py:0
#, python-format
msgid "The rule pattern \"%s\" is not a valid Regex: "
msgstr ""

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_rule.py:0
#, python-format
msgid ""
"The rule pattern \"%s\" is not valid, it needs two groups:\n"
"\t- A first one for the Application Identifier (usually 2 to 4 digits);\n"
"\t- A second one to catch the value."
msgstr ""

#. module: barcodes_gs1_nomenclature
#. odoo-python
#: code:addons/barcodes_gs1_nomenclature/models/barcode_nomenclature.py:0
#, python-format
msgid ""
"There is something wrong with the barcode rule \"%s\" pattern.\n"
"If this rule uses decimal, check it can't get sometime else than a digit as last char for the Application Identifier.\n"
"Check also the possible matched values can only be digits, otherwise the value can't be casted as a measure."
msgstr ""

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_nomenclature__is_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__is_gs1_nomenclature
msgid ""
"This Nomenclature use the GS1 specification, only GS1-128 encoding rules is "
"accepted is this kind of nomenclature."
msgstr ""

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "This barcode can't be parsed by any barcode rules."
msgstr ""

#. module: barcodes_gs1_nomenclature
#. odoo-javascript
#: code:addons/barcodes_gs1_nomenclature/static/src/js/barcode_parser.js:0
#, python-format
msgid "This barcode can't be partially or fully parsed."
msgstr ""

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,help:barcodes_gs1_nomenclature.field_barcode_rule__encoding
msgid ""
"This rule will apply only if the barcode is encoded with the specified "
"encoding"
msgstr ""
"Ezt a szabályt csak egyedi szabállyal létrehozott vonalkódra alkalmazza"

#. module: barcodes_gs1_nomenclature
#: model:ir.model.fields,field_description:barcodes_gs1_nomenclature.field_barcode_rule__type
msgid "Type"
msgstr "Típus"
