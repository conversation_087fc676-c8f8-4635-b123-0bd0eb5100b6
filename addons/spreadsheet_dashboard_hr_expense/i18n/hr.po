# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_hr_expense
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <karol<PERSON>.<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "# Expenses"
msgstr "# Troškovi"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Category"
msgstr "Kategorija"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Employee"
msgstr "Zaposlenik"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expense"
msgstr "Trošak"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_hr_expense.spreadsheet_dashboard_expense
#, python-format
msgid "Expenses"
msgstr "Troškovi"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses Analysis"
msgstr "Analiza troškova"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Expenses Analysis by Customer to Reinvoice"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - Expenses"
msgstr "KPI - Troškovi"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To reimburse"
msgstr "KPI - Za nadoknaditi"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To report"
msgstr "KPI - Za izvještavanje"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "KPI - To validate"
msgstr "KPI - Za odobriti"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Order"
msgstr "Narudžba"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Period"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Product"
msgstr "Proizvod"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To reimburse"
msgstr "Za nadoknaditi"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To report"
msgstr "Za izvještavanje"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "To validate"
msgstr "Za odobriti"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Categories"
msgstr "Top kategorije"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Employees"
msgstr "Top zaposlenici"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Expenses"
msgstr "Najveći troškovi"

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Top Reinvoiced Orders"
msgstr ""

#. module: spreadsheet_dashboard_hr_expense
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_expense/data/files/expense_dashboard.json:0
#, python-format
msgid "Total"
msgstr "Ukupno"
