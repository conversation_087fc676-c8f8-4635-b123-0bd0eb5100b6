# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_at
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-24 13:17+0000\n"
"PO-Revision-Date: 2023-10-24 13:17+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0010
msgid "0010"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0011
msgid "0011"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0019
msgid "0019"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0020
msgid "0020"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0021
msgid "0021"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0029
msgid "0029"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0100
msgid "0100"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0101
msgid "0101"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0109
msgid "0109"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0110
msgid "0110"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0111
msgid "0111"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0112
msgid "0112"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0113
msgid "0113"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0119
msgid "0119"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0120
msgid "0120"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0121
msgid "0121"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0129
msgid "0129"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0130
msgid "0130"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0131
msgid "0131"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0132
msgid "0132"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0133
msgid "0133"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0134
msgid "0134"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0140
msgid "0140"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0141
msgid "0141"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0149
msgid "0149"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0150
msgid "0150"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0151
msgid "0151"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0159
msgid "0159"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0160
msgid "0160"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0169
msgid "0169"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0170
msgid "0170"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0179
msgid "0179"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0180
msgid "0180"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0200
msgid "0200"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0205
msgid "0205"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0210
msgid "0210"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0219
msgid "0219"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0220
msgid "0220"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0229
msgid "0229"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0300
msgid "0300"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0301
msgid "0301"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0310
msgid "0310"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0311
msgid "0311"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0320
msgid "0320"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0321
msgid "0321"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0330
msgid "0330"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0331
msgid "0331"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0340
msgid "0340"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0341
msgid "0341"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0349
msgid "0349"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0350
msgid "0350"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0351
msgid "0351"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0359
msgid "0359"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0360
msgid "0360"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0361
msgid "0361"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0370
msgid "0370"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0371
msgid "0371"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0379
msgid "0379"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0400
msgid "0400"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0401
msgid "0401"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0410
msgid "0410"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0411
msgid "0411"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0420
msgid "0420"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0421
msgid "0421"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0430
msgid "0430"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0431
msgid "0431"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0439
msgid "0439"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0440
msgid "0440"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0441
msgid "0441"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0500
msgid "0500"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0501
msgid "0501"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0510
msgid "0510"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0511
msgid "0511"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0520
msgid "0520"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0521
msgid "0521"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0530
msgid "0530"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0531
msgid "0531"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0540
msgid "0540"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0541
msgid "0541"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0550
msgid "0550"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0551
msgid "0551"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0555
msgid "0555"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0556
msgid "0556"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0559
msgid "0559"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0600
msgid "0600"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0601
msgid "0601"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0605
msgid "0605"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0606
msgid "0606"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0610
msgid "0610"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0611
msgid "0611"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0612
msgid "0612"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0613
msgid "0613"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0620
msgid "0620"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0621
msgid "0621"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0630
msgid "0630"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0631
msgid "0631"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0640
msgid "0640"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0641
msgid "0641"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0650
msgid "0650"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0651
msgid "0651"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0655
msgid "0655"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0659
msgid "0659"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0660
msgid "0660"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0661
msgid "0661"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0670
msgid "0670"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0671
msgid "0671"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0680
msgid "0680"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0681
msgid "0681"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0685
msgid "0685"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0686
msgid "0686"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0689
msgid "0689"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0700
msgid "0700"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0701
msgid "0701"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0702
msgid "0702"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0710
msgid "0710"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0711
msgid "0711"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0780
msgid "0780"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0781
msgid "0781"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0800
msgid "0800"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0810
msgid "0810"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0820
msgid "0820"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0830
msgid "0830"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0840
msgid "0840"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0850
msgid "0850"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0860
msgid "0860"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0862
msgid "0862"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0867
msgid "0867"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0870
msgid "0870"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0880
msgid "0880"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0900
msgid "0900"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0910
msgid "0910"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0920
msgid "0920"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0940
msgid "0940"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0950
msgid "0950"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0959
msgid "0959"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0980
msgid "0980"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0990
msgid "0990"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0995
msgid "0995"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0996
msgid "0996"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_0997
msgid "0997"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1000
msgid "1000"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1001
msgid "1001"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1100
msgid "1100"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1101
msgid "1101"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1200
msgid "1200"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1201
msgid "1201"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1300
msgid "1300"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1301
msgid "1301"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1350
msgid "1350"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1351
msgid "1351"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1360
msgid "1360"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1361
msgid "1361"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1400
msgid "1400"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1401
msgid "1401"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1500
msgid "1500"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1501
msgid "1501"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1600
msgid "1600"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1601
msgid "1601"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1700
msgid "1700"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1800
msgid "1800"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1801
msgid "1801"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1803
msgid "1803"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1910
msgid "1910"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1920
msgid "1920"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1930
msgid "1930"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1940
msgid "1940"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1950
msgid "1950"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1960
msgid "1960"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_1970
msgid "1970"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2000
msgid "2000"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2080
msgid "2080"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2090
msgid "2090"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2100
msgid "2100"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2130
msgid "2130"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2140
msgid "2140"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2150
msgid "2150"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2180
msgid "2180"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2190
msgid "2190"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2200
msgid "2200"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2201
msgid "2201"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2230
msgid "2230"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2240
msgid "2240"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2250
msgid "2250"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2251
msgid "2251"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2280
msgid "2280"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2290
msgid "2290"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2291
msgid "2291"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2292
msgid "2292"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2293
msgid "2293"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2294
msgid "2294"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2295
msgid "2295"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2300
msgid "2300"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2310
msgid "2310"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2320
msgid "2320"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2330
msgid "2330"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2340
msgid "2340"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2350
msgid "2350"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2470
msgid "2470"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2480
msgid "2480"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2490
msgid "2490"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2500
msgid "2500"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2501
msgid "2501"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2502
msgid "2502"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2504
msgid "2504"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2505
msgid "2505"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2506
msgid "2506"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2507
msgid "2507"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2509
msgid "2509"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2510
msgid "2510"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2511
msgid "2511"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2512
msgid "2512"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2513
msgid "2513"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2515
msgid "2515"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2517
msgid "2517"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2518
msgid "2518"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2519
msgid "2519"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2520
msgid "2520"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2530
msgid "2530"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2531
msgid "2531"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2532
msgid "2532"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2533
msgid "2533"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2534
msgid "2534"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2535
msgid "2535"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2536
msgid "2536"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2537
msgid "2537"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2538
msgid "2538"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2540
msgid "2540"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2541
msgid "2541"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2542
msgid "2542"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2543
msgid "2543"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2560
msgid "2560"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2565
msgid "2565"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2570
msgid "2570"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2573
msgid "2573"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2575
msgid "2575"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2577
msgid "2577"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2580
msgid "2580"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2582
msgid "2582"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2584
msgid "2584"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2586
msgid "2586"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2590
msgid "2590"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2600
msgid "2600"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2601
msgid "2601"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2602
msgid "2602"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2610
msgid "2610"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2612
msgid "2612"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2620
msgid "2620"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2630
msgid "2630"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2680
msgid "2680"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2690
msgid "2690"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2700
msgid "2700"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2730
msgid "2730"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2740
msgid "2740"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2750
msgid "2750"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2780
msgid "2780"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2790
msgid "2790"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2800
msgid "2800"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2880
msgid "2880"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2885
msgid "2885"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2890
msgid "2890"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2900
msgid "2900"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2940
msgid "2940"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2950
msgid "2950"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2960
msgid "2960"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2970
msgid "2970"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2980
msgid "2980"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2990
msgid "2990"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_2999
msgid "2999"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3000
msgid "3000"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3010
msgid "3010"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3020
msgid "3020"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3030
msgid "3030"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3035
msgid "3035"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3040
msgid "3040"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3041
msgid "3041"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3042
msgid "3042"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3043
msgid "3043"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3044
msgid "3044"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3050
msgid "3050"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3051
msgid "3051"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3052
msgid "3052"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3060
msgid "3060"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3061
msgid "3061"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3070
msgid "3070"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3071
msgid "3071"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3080
msgid "3080"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3085
msgid "3085"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3090
msgid "3090"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3100
msgid "3100"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3105
msgid "3105"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3110
msgid "3110"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3180
msgid "3180"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3200
msgid "3200"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3201
msgid "3201"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3202
msgid "3202"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3203
msgid "3203"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3204
msgid "3204"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3205
msgid "3205"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3210
msgid "3210"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3300
msgid "3300"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3360
msgid "3360"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3370
msgid "3370"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3380
msgid "3380"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3390
msgid "3390"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3400
msgid "3400"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3401
msgid "3401"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3440
msgid "3440"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3441
msgid "3441"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3450
msgid "3450"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3455
msgid "3455"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3460
msgid "3460"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3470
msgid "3470"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3480
msgid "3480"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3481
msgid "3481"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3485
msgid "3485"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3486
msgid "3486"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3490
msgid "3490"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3491
msgid "3491"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3492
msgid "3492"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3493
msgid "3493"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3495
msgid "3495"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3500
msgid "3500"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3501
msgid "3501"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3502
msgid "3502"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3503
msgid "3503"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3504
msgid "3504"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3505
msgid "3505"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3506
msgid "3506"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3507
msgid "3507"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3509
msgid "3509"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3510
msgid "3510"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3511
msgid "3511"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3516
msgid "3516"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3517
msgid "3517"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3520
msgid "3520"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3530
msgid "3530"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3540
msgid "3540"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3541
msgid "3541"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3542
msgid "3542"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3550
msgid "3550"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3551
msgid "3551"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3554
msgid "3554"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3556
msgid "3556"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3560
msgid "3560"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3590
msgid "3590"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3600
msgid "3600"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3610
msgid "3610"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3620
msgid "3620"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3630
msgid "3630"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3640
msgid "3640"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3650
msgid "3650"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3700
msgid "3700"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3750
msgid "3750"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3760
msgid "3760"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3800
msgid "3800"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3900
msgid "3900"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3990
msgid "3990"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_3999
msgid "3999"
msgstr ""

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_sale_report_title
msgid "4. VAT Computation (U1/U30)"
msgstr "4. Berechnung der Umsatzsteuer (U1/U30)"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_1
msgid ""
"4.1 Total amount of the taxable base for deliveries and other services [000]"
msgstr "4.1 Gesamtbetrag der Bemessungsgrundlage für Lieferungen und sonstige Leistungen [000]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_10
msgid "4.10 Section 6(1)(9)(a) (land sales) [019]"
msgstr "4.10 § 6 Abs. 1 Z 9 lit. a (Grundstücksumsätze) [019]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_11
msgid "4.11 Section 6(1)(27) (small business) [016]"
msgstr "4.11 § 6 Abs. 1 Z 27 (Kleinunternehmer) [016]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_12
msgid ""
"4.12 Section 6(1)(...) (other tax-exempt transactions without deduction of "
"input tax) [020]"
msgstr "4.12 § 6 Abs. 1 Z .. (übrige steuerfreie Umsätze ohne Vorsteuerabzug) [020]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_13
msgid ""
"4.13 Total amount of taxable deliveries, other services and own consumption "
"(including taxable advance payments)"
msgstr "4.13 Gesamtbetrag der steuerpflichtigen Lieferungen, sonstigen Leistungen und Eigenverbrauch (einschließlich steuerpflichtiger Anzahlungen)"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_14_tax
msgid "4.14 20% Standard rate"
msgstr "4.14 20% Normalsteuersatz"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_14_base
msgid "4.14 20% Standard rate [022]"
msgstr "4.14 20% Normalsteuersatz [022]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_15_tax
msgid "4.15 10% Reduced rate"
msgstr "4.15 10% ermäßigter Steuersatz"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_15_base
msgid "4.15 10% Reduced rate [029]"
msgstr "4.15 10% ermäßigter Steuersatz [029]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_16_tax
msgid "4.16 13% Reduced rate"
msgstr "4.16 13% ermäßigter Steuersatz"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_16_base
msgid "4.16 13% Reduced rate [006]"
msgstr "4.16 13% ermäßigter Steuersatz [006]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_17_tax
msgid "4.17 19% for Jungholz and Mittelberg"
msgstr "4.17 19% für Jungholz und Mittelberg"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_17_base
msgid "4.17 19% for Jungholz and Mittelberg [037]"
msgstr "4.17 19% für Jungholz und Mittelberg [037]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_18_tax
msgid ""
"4.18 10% Additional tax for flat-rate agricultural and forestry holdings"
msgstr "4.18 10% Zusatzsteuer für pauschalierte land- und forstwirtschaftliche Betriebe"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_18_base
msgid ""
"4.18 10% Additional tax for flat-rate agricultural and forestry holdings "
"[052]"
msgstr "4.18 10% Zusatzsteuer für pauschalierte land- und forstwirtschaftliche Betriebe [052]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_19_tax
msgid ""
"4.19 7% Additional tax for flat-rate agricultural and forestry holdings"
msgstr "4.19 7% Zusatzsteuer für pauschalierte land- und forstwirtschaftliche Betriebe"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_19_base
msgid ""
"4.19 7% Additional tax for flat-rate agricultural and forestry holdings "
"[007]"
msgstr "4.19 7% Zusatzsteuer für pauschalierte land- und forstwirtschaftliche Betriebe [007]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_2
msgid ""
"4.2 Plus own consumption (Section 1(1)(2), Section 3(2) and Section 3a(1a)) "
"[001]"
msgstr "4.2 zuzüglich Eigenverbrauch (§ 1 Abs. 1 Z 2, § 3 Abs. 2 und § 3a Abs. 1a) [001]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_20
msgid ""
"4.20 Tax liability pursuant to Section 11(12,14), Section 16(2) and pursuant"
" to Art. 7(4) [056]"
msgstr "4.20 Steuerschuld gemäß § 11 Abs. 12 und 14, § 16 Abs. 2 sowie gemäß Art. 7 Abs. 4 [056]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_21
msgid ""
"4.21 Tax liability pursuant to Section 19(1) second sentence, Section "
"19(1c,1e) and pursuant to Art. 25(5) [057]"
msgstr "4.21 Steuerschuld gemäß § 19 Abs. 1 zweiter Satz, § 19 Abs. 1c, 1e sowie gemäß Art. 25 Abs. 5 [057]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_22
msgid ""
"4.22 Tax liability according to Section 19(1a) (construction services) [048]"
msgstr "4.22 Steuerschuld gemäß § 19 Abs. 1a (Bauleistungen) [048]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_23
msgid "4.23 Tax liability under Section 19(1b) (security property, ...) [044]"
msgstr "4.23 Steuerschuld gemäß § 19 Abs. 1b (Sicherungseigentum, ...) [044]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_24
msgid ""
"4.24 Tax liability under Section 19(1d) (scrap and waste materials) [032]"
msgstr "4.24 Steuerschuld gemäß § 19 Abs. 1d (Schrott und Abfallstoffe) [032]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_25
msgid ""
"4.25 Total amount of taxable amounts for intra-Community acquisitions [070]"
msgstr "4.25 Gesamtbetrag der Bemessungsgrundlagen für innergemeinschaftliche Erwerbe [070]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_26
msgid "4.26 Tax-exempt under Art. 6(2) [071]"
msgstr "4.26 Davon steuerfrei gemäß Art. 6 Abs. 2 [071]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_27
msgid "4.27 Total amount of taxable intra-Community acquisitions"
msgstr "4.27 Gesamtbetrag der steuerpflichtigen innergemeinschaftlichen Erwerbe"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_28_tax
msgid "4.28 20% Standard rate"
msgstr "4.28 20% Normalsteuersatz"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_28_base
msgid "4.28 20% Standard rate [072]"
msgstr "4.28 20% Normalsteuersatz [072]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_29_tax
msgid "4.29 10% Reduced rate"
msgstr "4.29 10% ermäßigter Steuersatz"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_29_base
msgid "4.29 10% Reduced rate [073]"
msgstr "4.29 10% ermäßigter Steuersatz [073]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_3
msgid ""
"4.3 Minus revenue for which the tax liability is the beneficiary according "
"to Section 19(1) [021]"
msgstr "4.3 abzüglich Umsätze, für die die Steuerschuld gemäß § 19 Abs. 1 (Leistungsempfänger) [021]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_30_tax
msgid "4.30 13% Reduced rate"
msgstr "4.30 13% ermäßigter Steuersatz"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_30_base
msgid "4.30 13% Reduced rate [008]"
msgstr "4.30 13% ermäßigter Steuersatz [008]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_31_tax
msgid "4.31 19% for Jungholz and Mittelberg"
msgstr "4.31 19% für Jungholz und Mittelberg"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_31_base
msgid "4.31 19% for Jungholz and Mittelberg [088]"
msgstr "4.31 19% für Jungholz und Mittelberg [088]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_32
msgid ""
"4.32 Acquisitions pursuant to Art. 3(8), second sentence, that have been "
"taxed in the Member State of destination [076]"
msgstr "4.32 Erwerbe gemäß Art. 3 Abs. 8 zweiter Satz, die im Mitgliedstaat des Bestimmungslandes besteuert worden sind [076]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_33
msgid ""
"4.33 Acquisitions under Art. 3(8), second sentence, deemed to be taxed "
"domestically under Art. 25(2) [077]"
msgstr "4.33 Erwerbe gemäß Art. 3 Abs. 8 zweiter Satz, die gemäß Art. 25 Abs. 2 im Inland als besteuert gelten [077]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_4
msgid "4.4 Total"
msgstr "4.4 Summe"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_5
msgid ""
"4.5 Section 6(1)(1) in conjunction with Section 7 (export deliveries) [011]"
msgstr "4.5 § 6 Abs. 1 Z 1 iVm § 7 (Ausfuhrlieferungen) [011]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_6
msgid ""
"4.6 Section 6(1)(1) in conjunction with Section 8 (contract processing) "
"[012]"
msgstr "4.6 § 6 Abs. 1 Z 1 iVm § 8 (Lohnveredelungen) [012]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_7
msgid ""
"4.7 Section 6(1)(2)-(6) and Section 23(5) (seafaring, aviation, ...) [015]"
msgstr "4.7 § 6 Abs. 1 Z 2 bis 6 sowie § 23 Abs. 5 (Seeschifffahrt, Luftfahrt, ...) [015]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_8
msgid ""
"4.8 Article 6(1) (Intra-Community deliveries excluding deliveries of "
"vehicles to be specified separately below) [017]"
msgstr "4.8 Art. 6 Abs. 1 (innergemeinschaftliche Lieferungen ohne die nachstehend gesondert anzuführenden Fahrzeuglieferungen) [017]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_4_9
msgid ""
"4.9 Article 6(1), if deliveries of new vehicles were made to customers "
"without a VAT number or by vehicle suppliers pursuant to article. 2 [018]"
msgstr "4.9 Art. 6 Abs. 1, sofern Lieferungen neuer Fahrzeuge an Abnehmer ohne UID-Nummer bzw. durch Fahrzeuglieferer gemäß Art. 2 erfolgten. [018]"

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4000
msgid "4000"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4010
msgid "4010"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4012
msgid "4012"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4014
msgid "4014"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4016
msgid "4016"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4019
msgid "4019"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4030
msgid "4030"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4031
msgid "4031"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4050
msgid "4050"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4052
msgid "4052"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4054
msgid "4054"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4060
msgid "4060"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4062
msgid "4062"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4064
msgid "4064"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4066
msgid "4066"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4068
msgid "4068"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4069
msgid "4069"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4070
msgid "4070"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4080
msgid "4080"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4084
msgid "4084"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4090
msgid "4090"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4092
msgid "4092"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4095
msgid "4095"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4096
msgid "4096"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4098
msgid "4098"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4100
msgid "4100"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4110
msgid "4110"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4111
msgid "4111"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4120
msgid "4120"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4200
msgid "4200"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4210
msgid "4210"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4290
msgid "4290"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4310
msgid "4310"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4350
msgid "4350"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4352
msgid "4352"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4353
msgid "4353"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4354
msgid "4354"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4356
msgid "4356"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4358
msgid "4358"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4360
msgid "4360"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4362
msgid "4362"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4364
msgid "4364"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4400
msgid "4400"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4401
msgid "4401"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4402
msgid "4402"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4403
msgid "4403"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4416
msgid "4416"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4419
msgid "4419"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4420
msgid "4420"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4421
msgid "4421"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4422
msgid "4422"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4423
msgid "4423"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4424
msgid "4424"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4425
msgid "4425"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4430
msgid "4430"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4449
msgid "4449"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4450
msgid "4450"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4451
msgid "4451"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4452
msgid "4452"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4453
msgid "4453"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4454
msgid "4454"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4455
msgid "4455"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4456
msgid "4456"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4457
msgid "4457"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4458
msgid "4458"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4459
msgid "4459"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4460
msgid "4460"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4461
msgid "4461"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4462
msgid "4462"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4466
msgid "4466"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4469
msgid "4469"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4470
msgid "4470"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4480
msgid "4480"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4481
msgid "4481"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4482
msgid "4482"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4486
msgid "4486"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4489
msgid "4489"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4490
msgid "4490"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4500
msgid "4500"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4510
msgid "4510"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4520
msgid "4520"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4580
msgid "4580"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4600
msgid "4600"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4605
msgid "4605"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4610
msgid "4610"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4630
msgid "4630"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4631
msgid "4631"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4660
msgid "4660"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4700
msgid "4700"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4701
msgid "4701"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4702
msgid "4702"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4705
msgid "4705"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4709
msgid "4709"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4800
msgid "4800"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4810
msgid "4810"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4820
msgid "4820"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4826
msgid "4826"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4829
msgid "4829"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4830
msgid "4830"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4831
msgid "4831"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4835
msgid "4835"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4840
msgid "4840"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4850
msgid "4850"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4851
msgid "4851"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4852
msgid "4852"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4855
msgid "4855"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4860
msgid "4860"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4865
msgid "4865"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4866
msgid "4866"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4867
msgid "4867"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4870
msgid "4870"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4871
msgid "4871"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4872
msgid "4872"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4875
msgid "4875"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4880
msgid "4880"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4881
msgid "4881"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4882
msgid "4882"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4885
msgid "4885"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4900
msgid "4900"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4901
msgid "4901"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4902
msgid "4902"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4903
msgid "4903"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4940
msgid "4940"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4941
msgid "4941"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4942
msgid "4942"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4943
msgid "4943"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4980
msgid "4980"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4981
msgid "4981"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4982
msgid "4982"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4983
msgid "4983"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4995
msgid "4995"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_4999
msgid "4999"
msgstr ""

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_purchase_report_title
msgid "5. Deductible input tax computation"
msgstr "5. Berechnung der abziehbaren Vorsteuer"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_5_1
msgid ""
"5.1 Total amount of input taxes (excluding amounts to be shown separately "
"below) [060]"
msgstr "5.1 Gesamtbetrag der Vorsteuern (ohne die nachstehend gesondert anzuführenden Beträge) [060]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_5_10
msgid ""
"5.10 Not deductible under Section 12(3) in conjunction with Subsections 4 "
"and 5 [062]"
msgstr "5.10 Davon nicht abzugsfähig gemäß § 12 Abs. 3 iVm Abs. 4 und 5 [062]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_5_11
msgid "5.11 Correction pursuant to Section 12(10,11) [063]"
msgstr "5.11 Berichtigung gemäß § 12 Abs. 10 und 11 [063]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_5_12
msgid "5.12 Correction pursuant to Section 16 [067]"
msgstr "5.12 Berichtigung gemäß § 16 [067]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_5_13
msgid "5.13 Total amount of deductible input tax"
msgstr "5.13 Gesamtbetrag der abziehbaren Vorsteuer"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_5_2
msgid ""
"5.2 Input tax relating to import turnover tax paid (Section 12(1)(2)(a)) "
"[061]"
msgstr "5.2 Vorsteuern betreffend die entrichtete Einfuhrumsatzsteuer (§ 12 Abs. 1 Z 2 lit. a) [061]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_5_3
msgid ""
"5.3 Input tax concerning the import turnover tax owed and entered in the tax"
" account (Section 12(1)(2)(b)) [083]"
msgstr "5.3 Vorsteuern betreffend die geschuldete, auf dem Abgabenkonto verbuchte Einfuhrumsatzsteuer (§ 12 Abs. 1 Z 2 lit. b) [083]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_5_4
msgid "5.4 Input taxes from intra-Community acquisition [065]"
msgstr "5.4 Vorsteuern aus dem innergemeinschaftlichen Erwerb [065]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_5_5
msgid ""
"5.5 Input taxes concerning the tax liability pursuant to Section 19(1) "
"second sentence, Section 19(1c,1e) and pursuant to Art. 25(5) [066]"
msgstr "5.5 Vorsteuern betreffend die Steuerschuld gemäß § 19 Abs. 1 zweiter Satz, § 19 Abs. 1c, 1e sowie gemäß Art. 25 Abs. 5 [066]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_5_6
msgid ""
"5.6 Input taxes relating to tax liability pursuant to Section 19(1a) "
"(construction services) [082]"
msgstr "5.6 Vorsteuern betreffend die Steuerschuld gemäß § 19 Abs. 1a (Bauleistungen) [082]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_5_7
msgid ""
"5.7 Input taxes relating to tax liability under Section 19(1b) (security "
"property, ...) [087]"
msgstr "5.7 Vorsteuern betreffend die Steuerschuld gemäß § 19 Abs. 1b (Sicherungseigentum, ...) [087]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_5_8
msgid ""
"5.8 Input taxes relating to tax liability under Section 19(1d) (scrap and "
"waste materials) [089]"
msgstr "5.8 Vorsteuern betreffend die Steuerschuld gemäß § 19 Abs. 1d (Schrott und Abfallstoffe) [089]"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_5_9
msgid ""
"5.9 Input taxes for intra-Community deliveries of new vehicles by vehicle "
"suppliers under Art. 2 [064]"
msgstr "5.9 Vorsteuern für innergemeinschaftliche Lieferungen neuer Fahrzeuge von Fahrzeuglieferern gemäß Art. 2 [064]"

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5000
msgid "5000"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5001
msgid "5001"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5002
msgid "5002"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5003
msgid "5003"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5004
msgid "5004"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5005
msgid "5005"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5006
msgid "5006"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5007
msgid "5007"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5080
msgid "5080"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5084
msgid "5084"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5100
msgid "5100"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5200
msgid "5200"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5300
msgid "5300"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5310
msgid "5310"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5320
msgid "5320"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5330
msgid "5330"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5340
msgid "5340"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5350
msgid "5350"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5360
msgid "5360"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5370
msgid "5370"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5400
msgid "5400"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5410
msgid "5410"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5411
msgid "5411"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5412
msgid "5412"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5414
msgid "5414"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5415
msgid "5415"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5416
msgid "5416"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5417
msgid "5417"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5440
msgid "5440"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5441
msgid "5441"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5450
msgid "5450"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5451
msgid "5451"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5460
msgid "5460"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5461
msgid "5461"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5470
msgid "5470"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5471
msgid "5471"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5500
msgid "5500"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5501
msgid "5501"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5600
msgid "5600"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5601
msgid "5601"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5610
msgid "5610"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5615
msgid "5615"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5620
msgid "5620"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5621
msgid "5621"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5700
msgid "5700"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5750
msgid "5750"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5770
msgid "5770"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5772
msgid "5772"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5774
msgid "5774"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5780
msgid "5780"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5800
msgid "5800"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5801
msgid "5801"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5802
msgid "5802"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5803
msgid "5803"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5805
msgid "5805"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5806
msgid "5806"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5810
msgid "5810"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5811
msgid "5811"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5812
msgid "5812"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5820
msgid "5820"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5821
msgid "5821"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5822
msgid "5822"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5830
msgid "5830"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5831
msgid "5831"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5834
msgid "5834"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5835
msgid "5835"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5840
msgid "5840"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5842
msgid "5842"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5844
msgid "5844"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5850
msgid "5850"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5851
msgid "5851"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5852
msgid "5852"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5853
msgid "5853"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5855
msgid "5855"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5856
msgid "5856"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5857
msgid "5857"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5858
msgid "5858"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5860
msgid "5860"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_5900
msgid "5900"
msgstr ""

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_final_report_title
msgid "6. Other corrections"
msgstr "6. Sonstige Berichtigungen"

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6000
msgid "6000"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6005
msgid "6005"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6010
msgid "6010"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6015
msgid "6015"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6020
msgid "6020"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6025
msgid "6025"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6030
msgid "6030"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6040
msgid "6040"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6050
msgid "6050"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6060
msgid "6060"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6070
msgid "6070"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6090
msgid "6090"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6100
msgid "6100"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6200
msgid "6200"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6205
msgid "6205"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6210
msgid "6210"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6220
msgid "6220"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6225
msgid "6225"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6230
msgid "6230"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6240
msgid "6240"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6250
msgid "6250"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6260
msgid "6260"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6270
msgid "6270"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6290
msgid "6290"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6400
msgid "6400"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6401
msgid "6401"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6402
msgid "6402"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6403
msgid "6403"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6404
msgid "6404"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6410
msgid "6410"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6411
msgid "6411"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6420
msgid "6420"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6421
msgid "6421"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6450
msgid "6450"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6451
msgid "6451"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6452
msgid "6452"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6453
msgid "6453"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6454
msgid "6454"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6455
msgid "6455"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6460
msgid "6460"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6461
msgid "6461"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6500
msgid "6500"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6560
msgid "6560"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6580
msgid "6580"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6590
msgid "6590"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6600
msgid "6600"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6601
msgid "6601"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6602
msgid "6602"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6603
msgid "6603"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6660
msgid "6660"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6661
msgid "6661"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6662
msgid "6662"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6663
msgid "6663"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6670
msgid "6670"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6680
msgid "6680"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6685
msgid "6685"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6690
msgid "6690"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6695
msgid "6695"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6700
msgid "6700"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6710
msgid "6710"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6720
msgid "6720"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6730
msgid "6730"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6731
msgid "6731"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6740
msgid "6740"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6750
msgid "6750"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6751
msgid "6751"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6790
msgid "6790"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6791
msgid "6791"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_6800
msgid "6800"
msgstr ""

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_7
msgid "7. Prepayment/debit (-) or credit/surplus (+) [095]"
msgstr "7. Zahllast (-) bzw. Gutschrift/Überschuss (+) [095]"

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7000
msgid "7000"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7005
msgid "7005"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7010
msgid "7010"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7011
msgid "7011"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7020
msgid "7020"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7021
msgid "7021"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7022
msgid "7022"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7030
msgid "7030"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7040
msgid "7040"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7041
msgid "7041"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7042
msgid "7042"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7050
msgid "7050"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7090
msgid "7090"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7100
msgid "7100"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7110
msgid "7110"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7120
msgid "7120"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7130
msgid "7130"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7140
msgid "7140"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7150
msgid "7150"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7160
msgid "7160"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7161
msgid "7161"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7162
msgid "7162"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7170
msgid "7170"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7171
msgid "7171"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7180
msgid "7180"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7200
msgid "7200"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7201
msgid "7201"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7202
msgid "7202"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7203
msgid "7203"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7204
msgid "7204"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7205
msgid "7205"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7206
msgid "7206"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7209
msgid "7209"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7210
msgid "7210"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7215
msgid "7215"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7216
msgid "7216"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7220
msgid "7220"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7225
msgid "7225"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7230
msgid "7230"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7235
msgid "7235"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7300
msgid "7300"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7320
msgid "7320"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7321
msgid "7321"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7322
msgid "7322"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7323
msgid "7323"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7324
msgid "7324"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7325
msgid "7325"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7326
msgid "7326"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7330
msgid "7330"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7332
msgid "7332"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7334
msgid "7334"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7335
msgid "7335"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7336
msgid "7336"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7340
msgid "7340"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7345
msgid "7345"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7350
msgid "7350"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7355
msgid "7355"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7360
msgid "7360"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7370
msgid "7370"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7380
msgid "7380"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7381
msgid "7381"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7382
msgid "7382"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7385
msgid "7385"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7390
msgid "7390"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7400
msgid "7400"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7401
msgid "7401"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7402
msgid "7402"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7410
msgid "7410"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7411
msgid "7411"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7412
msgid "7412"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7440
msgid "7440"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7480
msgid "7480"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7490
msgid "7490"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7500
msgid "7500"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7540
msgid "7540"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7580
msgid "7580"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7585
msgid "7585"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7600
msgid "7600"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7601
msgid "7601"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7610
msgid "7610"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7611
msgid "7611"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7630
msgid "7630"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7631
msgid "7631"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7650
msgid "7650"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7651
msgid "7651"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7652
msgid "7652"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7653
msgid "7653"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7654
msgid "7654"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7660
msgid "7660"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7661
msgid "7661"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7685
msgid "7685"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7690
msgid "7690"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7695
msgid "7695"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7696
msgid "7696"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7700
msgid "7700"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7710
msgid "7710"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7720
msgid "7720"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7740
msgid "7740"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7750
msgid "7750"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7755
msgid "7755"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7758
msgid "7758"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7760
msgid "7760"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7765
msgid "7765"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7770
msgid "7770"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7775
msgid "7775"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7780
msgid "7780"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7782
msgid "7782"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7785
msgid "7785"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7790
msgid "7790"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7800
msgid "7800"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7801
msgid "7801"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7804
msgid "7804"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7805
msgid "7805"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7806
msgid "7806"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7807
msgid "7807"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7808
msgid "7808"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7809
msgid "7809"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7810
msgid "7810"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7811
msgid "7811"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7815
msgid "7815"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7816
msgid "7816"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7820
msgid "7820"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7825
msgid "7825"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7830
msgid "7830"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7840
msgid "7840"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7841
msgid "7841"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7850
msgid "7850"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7860
msgid "7860"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7870
msgid "7870"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7890
msgid "7890"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7900
msgid "7900"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7910
msgid "7910"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7960
msgid "7960"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7970
msgid "7970"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7980
msgid "7980"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7990
msgid "7990"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_7999
msgid "7999"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8000
msgid "8000"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8010
msgid "8010"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8020
msgid "8020"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8030
msgid "8030"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8040
msgid "8040"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8045
msgid "8045"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8050
msgid "8050"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8052
msgid "8052"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8055
msgid "8055"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8060
msgid "8060"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8070
msgid "8070"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8080
msgid "8080"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8100
msgid "8100"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8101
msgid "8101"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8110
msgid "8110"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8120
msgid "8120"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8121
msgid "8121"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8122
msgid "8122"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8125
msgid "8125"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8140
msgid "8140"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8150
msgid "8150"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8160
msgid "8160"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8170
msgid "8170"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8171
msgid "8171"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8180
msgid "8180"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8181
msgid "8181"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8190
msgid "8190"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8191
msgid "8191"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8200
msgid "8200"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8201
msgid "8201"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8205
msgid "8205"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8206
msgid "8206"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8210
msgid "8210"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8211
msgid "8211"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8220
msgid "8220"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8230
msgid "8230"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8231
msgid "8231"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8232
msgid "8232"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8260
msgid "8260"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8261
msgid "8261"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8270
msgid "8270"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8271
msgid "8271"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8280
msgid "8280"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8281
msgid "8281"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8290
msgid "8290"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8300
msgid "8300"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8310
msgid "8310"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8320
msgid "8320"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8340
msgid "8340"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8350
msgid "8350"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8360
msgid "8360"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8400
msgid "8400"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8410
msgid "8410"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8420
msgid "8420"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8440
msgid "8440"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8445
msgid "8445"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8450
msgid "8450"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8460
msgid "8460"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8470
msgid "8470"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8480
msgid "8480"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8490
msgid "8490"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8500
msgid "8500"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8510
msgid "8510"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8520
msgid "8520"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8530
msgid "8530"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8540
msgid "8540"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8550
msgid "8550"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8560
msgid "8560"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8600
msgid "8600"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8610
msgid "8610"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8700
msgid "8700"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8710
msgid "8710"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8720
msgid "8720"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8750
msgid "8750"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8760
msgid "8760"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8770
msgid "8770"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8800
msgid "8800"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8810
msgid "8810"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8900
msgid "8900"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8910
msgid "8910"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8920
msgid "8920"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8990
msgid "8990"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_8991
msgid "8991"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9000
msgid "9000"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9001
msgid "9001"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9009
msgid "9009"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9010
msgid "9010"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9011
msgid "9011"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9030
msgid "9030"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9031
msgid "9031"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9040
msgid "9040"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9050
msgid "9050"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9060
msgid "9060"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9070
msgid "9070"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9080
msgid "9080"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9090
msgid "9090"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9130
msgid "9130"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9140
msgid "9140"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9150
msgid "9150"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9160
msgid "9160"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9161
msgid "9161"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9162
msgid "9162"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9163
msgid "9163"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9190
msgid "9190"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9191
msgid "9191"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9192
msgid "9192"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9200
msgid "9200"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9210
msgid "9210"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9220
msgid "9220"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9240
msgid "9240"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9300
msgid "9300"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9310
msgid "9310"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9320
msgid "9320"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9330
msgid "9330"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9340
msgid "9340"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9345
msgid "9345"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9350
msgid "9350"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9351
msgid "9351"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9360
msgid "9360"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9361
msgid "9361"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9370
msgid "9370"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9371
msgid "9371"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9380
msgid "9380"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9381
msgid "9381"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9385
msgid "9385"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9389
msgid "9389"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9390
msgid "9390"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9392
msgid "9392"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9393
msgid "9393"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9396
msgid "9396"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9397
msgid "9397"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9400
msgid "9400"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9410
msgid "9410"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9420
msgid "9420"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9430
msgid "9430"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9450
msgid "9450"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9501
msgid "9501"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9502
msgid "9502"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9550
msgid "9550"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9570
msgid "9570"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9580
msgid "9580"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9581
msgid "9581"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9600
msgid "9600"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9601
msgid "9601"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9610
msgid "9610"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9611
msgid "9611"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9612
msgid "9612"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9613
msgid "9613"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9614
msgid "9614"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9618
msgid "9618"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9620
msgid "9620"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9630
msgid "9630"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9640
msgid "9640"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9641
msgid "9641"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9644
msgid "9644"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9700
msgid "9700"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9710
msgid "9710"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9720
msgid "9720"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9730
msgid "9730"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9800
msgid "9800"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9810
msgid "9810"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9850
msgid "9850"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9880
msgid "9880"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9881
msgid "9881"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9882
msgid "9882"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9883
msgid "9883"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9884
msgid "9884"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9890
msgid "9890"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9891
msgid "9891"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9990
msgid "9990"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9991
msgid "9991"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9992
msgid "9992"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9993
msgid "9993"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_external_code_9994
msgid "9994"
msgstr ""

#. module: l10n_at
#: model:ir.model,name:l10n_at.model_account_chart_template
msgid "Account Chart Template"
msgstr "Kontenplanvorlage"

#. module: l10n_at
#: model:account.report.column,name:l10n_at.tax_report_balance
msgid "Balance"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAI1
msgid "Bilanz AAI1"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAI2
msgid "Bilanz AAI2"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAI3
msgid "Bilanz AAI3"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAII1
msgid "Bilanz AAII1"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAII2
msgid "Bilanz AAII2"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAII3
msgid "Bilanz AAII3"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAII4
msgid "Bilanz AAII4"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAIII
msgid "Bilanz AAIII"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAIII1
msgid "Bilanz AAIII1"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAIII2
msgid "Bilanz AAIII2"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAIII3
msgid "Bilanz AAIII3"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAIII4
msgid "Bilanz AAIII4"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAIII5
msgid "Bilanz AAIII5"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AAIII6
msgid "Bilanz AAIII6"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_ABI1
msgid "Bilanz ABI1"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_ABI2
msgid "Bilanz ABI2"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_ABI3
msgid "Bilanz ABI3"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_ABI4
msgid "Bilanz ABI4"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_ABI5
msgid "Bilanz ABI5"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_ABII1
msgid "Bilanz ABII1"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_ABII2
msgid "Bilanz ABII2"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_ABII3
msgid "Bilanz ABII3"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_ABII4
msgid "Bilanz ABII4"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_ABIII
msgid "Bilanz ABIII"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_ABIII1
msgid "Bilanz ABIII1"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_ABIII2
msgid "Bilanz ABIII2"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_ABIV
msgid "Bilanz ABIV"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AC
msgid "Bilanz AC"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_AD
msgid "Bilanz AD"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PAI
msgid "Bilanz PAI"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PAII
msgid "Bilanz PAII"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PAII1
msgid "Bilanz PAII1"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PAII2
msgid "Bilanz PAII2"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PAIII
msgid "Bilanz PAIII"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PAIII1
msgid "Bilanz PAIII1"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PAIII2
msgid "Bilanz PAIII2"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PAIII3
msgid "Bilanz PAIII3"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PAIV
msgid "Bilanz PAIV"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PBI
msgid "Bilanz PBI"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PBII
msgid "Bilanz PBII"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PBIII
msgid "Bilanz PBIII"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PBIV
msgid "Bilanz PBIV"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PCI
msgid "Bilanz PCI"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PCII
msgid "Bilanz PCII"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PCIII
msgid "Bilanz PCIII"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PCIV
msgid "Bilanz PCIV"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PCV
msgid "Bilanz PCV"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PCVI
msgid "Bilanz PCVI"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PCVII
msgid "Bilanz PCVII"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PCVIII
msgid "Bilanz PCVIII"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PCVIII1
msgid "Bilanz PCVIII1"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PCVIII2
msgid "Bilanz PCVIII2"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_PD
msgid "Bilanz PD"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT1
msgid "GuV EBIT1"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT2
msgid "GuV EBIT2"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT3
msgid "GuV EBIT3"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT4
msgid "GuV EBIT4"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT4I
msgid "GuV EBIT4I"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT4II
msgid "GuV EBIT4II"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT4III
msgid "GuV EBIT4III"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT5I
msgid "GuV EBIT5I"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT5II
msgid "GuV EBIT5II"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT6I
msgid "GuV EBIT6I"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT6II
msgid "GuV EBIT6II"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT7I
msgid "GuV EBIT7I"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT7II
msgid "GuV EBIT7II"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_EBIT8
msgid "GuV EBIT8"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_FIN10
msgid "GuV FIN10"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_FIN11
msgid "GuV FIN11"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_FIN12
msgid "GuV FIN12"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_FIN13
msgid "GuV FIN13"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_FIN14
msgid "GuV FIN14"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_FIN15
msgid "GuV FIN15"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_MTAX
msgid "GuV MTAX"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_RL
msgid "GuV RL"
msgstr ""

#. module: l10n_at
#: model:account.account.tag,name:l10n_at.account_tag_l10n_at_TAX
msgid "GuV TAX"
msgstr ""

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_sale_03_report_title
msgid "Intra-Community acquisition"
msgstr "Innergemeinschaftliche Erwerb"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_sale_05_report_title
msgid "Non-taxable acquisitions"
msgstr "Nicht zu versteuernde Erwerbe"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_sale_04_report_title
msgid "Of which taxable with"
msgstr "Davon sind zu versteuern mit"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_line_6
msgid "Other corrections [090]"
msgstr "Sonstige Berichtigungen [090]"

#. module: l10n_at
#: model:account.report,name:l10n_at.tax_report
msgid "Tax Report"
msgstr "Steuerbericht"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_sale_01_report_title
msgid "Tax-exempt WITH input tax deduction according to"
msgstr "Davon steuerfrei MIT Vorsteuerabzug gemäß"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_l10n_at_tva_sale_02_report_title
msgid "Tax-exempt WITHOUT input tax deduction according to"
msgstr "Davon steuerfrei OHNE Vorsteuerabzug gemäß"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_at_base_title_umsatz_base_4_14_19
#: model:account.report.line,name:l10n_at.tax_report_line_at_base_title_umsatz_base_4_28_31
msgid "Taxable base"
msgstr "Bemessungsgrundlage"

#. module: l10n_at
#: model:account.report.line,name:l10n_at.tax_report_line_at_tax_title_4_14_19
#: model:account.report.line,name:l10n_at.tax_report_line_at_tax_title_4_28_31
msgid "VAT"
msgstr "Umsatzsteuer"
