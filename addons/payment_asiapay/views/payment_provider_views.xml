<?xml version='1.0' encoding='utf-8' ?>
<odoo>

    <record id="payment_provider_form" model="ir.ui.view">
        <field name="name">AsiaPay Provider Form</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment.payment_provider_form"/>
        <field name="arch" type="xml">
            <group name="provider_credentials" position='inside'>
                <group invisible="code != 'asiapay'">
                    <field name="asiapay_brand"
                           string="Brand"
                           required="code == 'asiapay' and state != 'disabled'"/>
                    <field name="asiapay_merchant_id"
                           string="Merchant ID"
                           required="code == 'asiapay' and state != 'disabled'"/>
                    <field name="asiapay_secure_hash_secret"
                           string="Secure Hash Secret"
                           required="code == 'asiapay' and state != 'disabled'"
                           password="True"/>
                    <field name="asiapay_secure_hash_function"
                           string="Secure Hash Function"
                           required="code == 'asiapay' and state != 'disabled'"
                           groups="base.group_no_one"/>
                </group>
            </group>
        </field>
    </record>

</odoo>
