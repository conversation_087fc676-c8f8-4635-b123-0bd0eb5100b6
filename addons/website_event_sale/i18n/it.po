# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_sale
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 12:32+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid ""
"<i class=\"fa fa-ban me-2\"/>\n"
"                                            Unpublished"
msgstr ""
"<i class=\"fa fa-ban me-2\"/>\n"
"                                            Non pubblicato"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid ""
"<i class=\"fa fa-check me-2\"/>\n"
"                                            Registered"
msgstr ""
"<i class=\"fa fa-check me-2\"/>\n"
"                                            Registrato"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_attendee_details
msgid ""
"<span>Sign In</span>\n"
"                <span class=\"fa fa-sign-in\"/>"
msgstr ""
"<span>Registrati</span>\n"
"                <span class=\"fa fa-sign-in\"/>"

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity cannot be applied to this "
"event tickets product."
msgstr ""
"Impossibile applicare al prodotto biglietti evento una voce di listino "
"prezzi con una quantità min. positiva."

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid ""
"A pricelist item with a positive min. quantity will not be applied to the "
"event tickets products."
msgstr ""
"Impossibile applicare ai prodotti biglietti evento una voce di listino "
"prezzi con una quantità min. positiva."

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_attendee_details
msgid "Confirm Registration"
msgstr "Conferma registrazione"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "Download Tickets <i class=\"ms-1 fa fa-download\"/>"
msgstr "Scarica biglietti <i class=\"ms-1 fa fa-download\"/>"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_event_sale_report
msgid "Event Sales Report"
msgstr "Resoconto vendite evento"

#. module: website_event_sale
#: model:ir.model.fields,field_description:website_event_sale.field_product_product__event_ticket_ids
msgid "Event Tickets"
msgstr "Biglietti evento"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.modal_ticket_registration
msgid "Free"
msgstr "Gratuito"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.modal_ticket_registration
msgid "From"
msgstr "Da"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "Go to Event"
msgstr "Vai all'evento"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.registration_attendee_details
msgid "Go to Payment"
msgstr "Vai al pagamento"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_pricelist_item
msgid "Pricelist Rule"
msgstr "Regola listino prezzi"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_template
msgid "Product"
msgstr "Prodotto"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_product_product
msgid "Product Variant"
msgstr "Variante prodotto"

#. module: website_event_sale
#: model:ir.model.fields,field_description:website_event_sale.field_event_sale_report__is_published
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_sale_report_view_search
msgid "Published Events"
msgstr "Eventi pubblicati"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order
msgid "Sales Order"
msgstr "Ordine di vendita"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Riga ordine di vendita"

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "Sorry, The %(ticket)s tickets for the %(event)s event are sold out."
msgstr "I biglietti %(event)s per l'evento %(ticket)s sono esauriti."

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid ""
"Sorry, only %(remaining_seats)d seats are still available for the %(ticket)s"
" ticket for the %(event)s event."
msgstr ""
"Per i biglietti %(ticket)sdell'evento %(event)s sono ancora disponibili solo"
" %(remaining_seats)dposti."

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "The provided ticket doesn't exist"
msgstr "Il biglietto fornito non esiste"

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "The ticket doesn't match with this product."
msgstr "Il biglietto non corrisponde al prodotto."

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/product_pricelist.py:0
#, python-format
msgid "Warning"
msgstr "Avviso"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "We are looking forward to meeting you at the following"
msgstr "Non vediamo l'ora di incontrarti al prossimo"

#. module: website_event_sale
#: model:ir.model,name:website_event_sale.model_website
msgid "Website"
msgstr "Sito web"

#. module: website_event_sale
#. odoo-python
#: code:addons/website_event_sale/models/sale_order.py:0
#, python-format
msgid "You cannot raise manually the event ticket quantity in your cart"
msgstr ""
"Non è possibile aumentare manualmente la quantità dei biglietti per l'evento"
" nel carrello"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "event"
msgstr "evento"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.event_confirmation
msgid "events"
msgstr "eventi"

#. module: website_event_sale
#: model_terms:ir.ui.view,arch_db:website_event_sale.modal_ticket_registration
msgid "to"
msgstr "a"
