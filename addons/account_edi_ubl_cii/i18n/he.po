# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi_ubl_cii
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# NoaFarkash, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# or balmas, 2025
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-06 18:37+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid ""
"%s should have a KVK or OIN number set in Company ID field or as Peppol "
"e-address (EAS code 0106 or 0190)."
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0002
msgid ""
"0002 - System Information et Repertoire des Entreprise et des "
"Etablissements: SIRENE"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0007
msgid "0007 - Organisationsnummer (Swedish legal entities)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0009
msgid "0009 - SIRET-CODE"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0037
msgid "0037 - LY-tunnus"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0060
msgid "0060 - Data Universal Numbering System (D-U-N-S Number)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0088
msgid "0088 - EAN Location Code"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0096
msgid "0096 - DANISH CHAMBER OF COMMERCE Scheme (EDIRA compliant)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0097
msgid "0097 - FTI - Ediforum Italia, (EDIRA compliant)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0106
msgid ""
"0106 - Association of Chambers of Commerce and Industry in the Netherlands, "
"(EDIRA compliant)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0130
msgid "0130 - Directorates of the European Commission"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0135
msgid "0135 - SIA Object Identifiers"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0142
msgid "0142 - SECETI Object Identifiers"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0151
msgid "0151 - Australian Business Number (ABN) Scheme"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0183
msgid "0183 - Swiss Unique Business Identification Number (UIDB)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0184
msgid "0184 - DIGSTORG"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0188
msgid "0188 - Corporate Number of The Social Security and Tax Number System"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0190
msgid "0190 - Dutch Originator's Identification Number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0191
msgid ""
"0191 - Centre of Registers and Information Systems of the Ministry of "
"Justice"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0192
msgid "0192 - Enhetsregisteret ved Bronnoysundregisterne"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0193
msgid "0193 - UBL.BE party identifier"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0195
msgid "0195 - Singapore UEN identifier"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0196
msgid "0196 - Kennitala - Iceland legal id for individuals and legal entities"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0198
msgid "0198 - ERSTORG"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0199
msgid "0199 - Legal Entity Identifier (LEI)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0200
msgid "0200 - Legal entity code (Lithuania)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0201
msgid "0201 - Codice Univoco Unità Organizzativa iPA"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0202
msgid "0202 - Indirizzo di Posta Elettronica Certificata"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0204
msgid "0204 - Leitweg-ID"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0208
msgid "0208 - Numero d'entreprise / ondernemingsnummer / Unternehmensnummer"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0209
msgid "0209 - GS1 identification keys"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0210
msgid "0210 - CODICE FISCALE"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0211
msgid "0211 - PARTITA IVA"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0212
msgid "0212 - Finnish Organization Identifier"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0213
msgid "0213 - Finnish Organization Value Add Tax Identifier"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0215
msgid "0215 - Net service ID"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0216
msgid "0216 - OVTcode"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0221
msgid "0221 - The registered number of the qualified invoice issuer (Japan)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__0230
msgid "0230 - National e-Invoicing Framework (Malaysia)"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "1.0"
msgstr "1.0"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_facturx_export_22
msgid "42"
msgstr "42"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9901
msgid "9901 - Danish Ministry of the Interior and Health"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9910
msgid "9910 - Hungary VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9913
msgid "9913 - Business Registers Network"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9914
msgid "9914 - Österreichische Umsatzsteuer-Identifikationsnummer"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9915
msgid "9915 - Österreichisches Verwaltungs bzw. Organisationskennzeichen"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9918
msgid ""
"9918 - SOCIETY FOR WORLDWIDE INTERBANK FINANCIAL, TELECOMMUNICATION "
"S.W.I.F.T"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9919
msgid "9919 - Kennziffer des Unternehmensregisters"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9920
msgid "9920 - Agencia Española de Administración Tributaria"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9922
msgid "9922 - Andorra VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9923
msgid "9923 - Albania VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9924
msgid "9924 - Bosnia and Herzegovina VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9925
msgid "9925 - Belgium VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9926
msgid "9926 - Bulgaria VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9927
msgid "9927 - Switzerland VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9928
msgid "9928 - Cyprus VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9929
msgid "9929 - Czech Republic VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9930
msgid "9930 - Germany VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9931
msgid "9931 - Estonia VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9932
msgid "9932 - United Kingdom VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9933
msgid "9933 - Greece VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9934
msgid "9934 - Croatia VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9935
msgid "9935 - Ireland VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9936
msgid "9936 - Liechtenstein VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9937
msgid "9937 - Lithuania VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9938
msgid "9938 - Luxemburg VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9939
msgid "9939 - Latvia VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9940
msgid "9940 - Monaco VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9941
msgid "9941 - Montenegro VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9942
msgid "9942 - Macedonia, the former Yugoslav Republic of VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9943
msgid "9943 - Malta VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9944
msgid "9944 - Netherlands VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9945
msgid "9945 - Poland VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9946
msgid "9946 - Portugal VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9947
msgid "9947 - Romania VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9948
msgid "9948 - Serbia VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9949
msgid "9949 - Slovenia VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9950
msgid "9950 - Slovakia VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9951
msgid "9951 - San Marino VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9952
msgid "9952 - Türkiye VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9953
msgid "9953 - Holy See (Vatican City State) VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9955
msgid "9955 - Swedish VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9957
msgid "9957 - French VAT number"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__9959
msgid "9959 - Employer Identification Number (EIN, USA)"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "A payment of %s was detected."
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_a_nz
msgid "A-NZ BIS Billing 3.0"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__an
msgid "AN - O.F.T.P. (ODETTE File Transfer Protocol)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__aq
msgid "AQ - X.400 address for mail text"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__as
msgid "AS - AS2 exchange"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__au
msgid "AU - File Transfer Protocol"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_move_send
msgid "Account Move Send"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Articles 226 items 11 to 15 Directive 2006/112/EN"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "At least one of the following fields %s is required on %s."
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_bank_statement_line__ubl_cii_xml_id
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move__ubl_cii_xml_id
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_payment__ubl_cii_xml_id
msgid "Attachment"
msgstr "קובץ מצורף"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__ubl_cii_format__ubl_bis3
msgid "BIS Billing 3.0"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__ubl_cii_format__ubl_a_nz
msgid "BIS Billing 3.0 A-NZ"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__ubl_cii_format__ubl_sg
msgid "BIS Billing 3.0 SG"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_de
msgid "BIS3 DE (XRechnung)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move_send__checkbox_ubl_cii_label
msgid "Checkbox Ubl Cii Label"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move_send__checkbox_ubl_cii_xml
msgid "Checkbox Ubl Cii Xml"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_partner__peppol_eas
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_users__peppol_eas
msgid ""
"Code used to identify the Endpoint for BIS Billing 3.0 and its derivatives.\n"
"             List available at https://docs.peppol.eu/poacc/billing/3.0/codelist/eas/"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_common
msgid ""
"Common functions for EDI documents: generate the data, the constraints, etc"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move_send__show_ubl_company_warning
msgid "Company warning"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#, python-format
msgid "Conditional cash/payment discount"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_res_config_settings
msgid "Config Settings"
msgstr "הגדר הגדרות"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_res_partner
msgid "Contact"
msgstr "איש קשר"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#, python-format
msgid ""
"Could not retrieve currency: %s. Did you enable the multicurrency option and"
" activate the currency?"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Could not retrieve the tax: %s %% for line '%s'."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Could not retrieve the unit of measure for line with label '%s'."
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_efff
msgid "E-FFF (BE)"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.res_partner_view_search
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.res_partner_view_tree
msgid "EDI Format"
msgstr "פורמט EDI"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__peppol_eas__em
msgid "EM - Electronic mail"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "EN 16931"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "Each invoice line shall have one and only one tax."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "Each invoice line should have a product or a label."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Each invoice line should have at least one tax."
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.view_partner_property_form
msgid "Electronic Invoicing"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move_send__enable_ubl_cii_xml
msgid "Enable Ubl Cii Xml"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/wizard/account_move_send.py:0
#, python-format
msgid "Errors occurred while creating the EDI document (format: %s):"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Export outside the EU"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__ubl_cii_format__facturx
msgid "Factur-X (CII)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_cii
msgid "Factur-x/XRechnung CII 2.2.0"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid ""
"For intracommunity supply, the actual delivery date or the invoicing period "
"should be included."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "For intracommunity supply, the delivery address should be included."
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__ubl_cii_format
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__ubl_cii_format
msgid "Format"
msgstr "תבנית"

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Format used to import the invoice: %s"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_company__invoice_is_ubl_cii
msgid "Generate Peppol format by default"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__hide_peppol_fields
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__hide_peppol_fields
msgid "Hide Peppol Fields"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Intra-Community supply"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "Invoice generated by Odoo"
msgstr "חשבונית שנוצרה על-ידי Odoo"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_move
msgid "Journal Entry"
msgstr "פקודת יומן"

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__ubl_cii_format__nlcius
msgid "NLCIUS"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid ""
"No gross price, net price nor line subtotal amount found for line in xml"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "Odoo"
msgstr "Odoo"

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move_send__ubl_partner_warning
msgid "Partner warning"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
#, python-format
msgid ""
"Peppol EAS code 9901 is deprecated. Please use a different Danish EAS code "
"instead."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
#, python-format
msgid "Peppol EAS code 9955 is deprecated. Please use 0007 instead."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
#, python-format
msgid ""
"Peppol EAS codes 0037, 0212, 0213, 0215 are deprecated. Please use 0216 "
"instead."
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__peppol_endpoint
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__peppol_endpoint
msgid "Peppol Endpoint"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_partner__peppol_eas
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_users__peppol_eas
msgid "Peppol e-address (EAS)"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_res_config_settings__invoice_is_ubl_cii
msgid "Peppol format"
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_move_send_form
msgid ""
"Please fill in Peppol EAS and Peppol Endpoint in your company form to "
"generate a complete file."
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_ir_actions_report
msgid "Report Action"
msgstr "פעולת דוח"

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_sg
msgid "SG BIS Billing 3.0"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_nl
msgid "SI-UBL 2.0 (NLCIUS)"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "Tax '%s' is invalid: %s"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
#, python-format
msgid ""
"The Peppol endpoint is not valid. It should contain exactly 10 digits "
"(Company Registry number).The expected format is: **********"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
#, python-format
msgid "The Peppol endpoint is not valid. The expected format is: **********"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/res_partner.py:0
#, python-format
msgid ""
"The Peppol endpoint is not valid. The expected format is: **************"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid ""
"The VAT number of the supplier does not seem to be valid. It should be of "
"the form: NO179728982MVA."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "The VAT of the %s should be prefixed with its country code."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_bis3.py:0
#, python-format
msgid "The country is required for the %s."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#, python-format
msgid "The currency '%s' is not active."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "The element %s is required on %s."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_common.py:0
#, python-format
msgid "The field %s is required on %s."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#, python-format
msgid ""
"The field 'Sanitized Account Number' is required on the Recipient Bank."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/wizard/account_move_send.py:0
#, python-format
msgid ""
"The following partners are missing Peppol EAS or Peppol Endpoint field: %s. "
"Please check those in their Accounting tab. Otherwise, the generated files "
"will be incomplete."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_ubl_20.py:0
#, python-format
msgid ""
"The invoice has been converted into a credit note and the quantities have "
"been reverted."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/wizard/account_move_send.py:0
#, python-format
msgid ""
"This partner is missing Peppol EAS or Peppol Endpoint field. Please check "
"those in its Accounting tab or the generated file will be incomplete."
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_20
msgid "UBL 2.0"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_21
msgid "UBL 2.1"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model,name:account_edi_ubl_cii.model_account_edi_xml_ubl_bis3
msgid "UBL BIS Billing 3.0.12"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_bank_statement_line__ubl_cii_xml_file
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_move__ubl_cii_xml_file
#: model:ir.model.fields,field_description:account_edi_ubl_cii.field_account_payment__ubl_cii_xml_file
msgid "UBL/CII File"
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_partner__peppol_endpoint
#: model:ir.model.fields,help:account_edi_ubl_cii.field_res_users__peppol_endpoint
msgid ""
"Unique identifier used by the BIS Billing 3.0 and its derivatives, also "
"known as 'Endpoint ID'."
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#, python-format
msgid ""
"When the Canary Island General Indirect Tax (IGIC) applies, the tax rate on "
"each invoice line should be greater than 0."
msgstr ""

#. module: account_edi_ubl_cii
#: model:ir.model.fields.selection,name:account_edi_ubl_cii.selection__res_partner__ubl_cii_format__xrechnung
msgid "XRechnung CIUS"
msgstr ""

#. module: account_edi_ubl_cii
#. odoo-python
#: code:addons/account_edi_ubl_cii/models/account_edi_xml_cii_facturx.py:0
#, python-format
msgid ""
"You should include at least one tax per invoice line. [BR-CO-04]-Each "
"Invoice line (BG-25) shall be categorized with an Invoiced item VAT category"
" code (BT-151)."
msgstr ""

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "factur-x.xml"
msgstr "factur-x.xml"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "fx"
msgstr "fx"

#. module: account_edi_ubl_cii
#: model_terms:ir.ui.view,arch_db:account_edi_ubl_cii.account_invoice_pdfa_3_facturx_metadata
msgid "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
msgstr "urn:factur-x:pdfa:CrossIndustryDocument:invoice:1p0#"
