# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_booth_sale
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2023
# Wil <PERSON>, 2023
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Manon <PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_form_from_event
msgid "<span class=\"o_stat_text\">Sale Order</span>"
msgstr "<span class=\"o_stat_text\">Commande client</span>"

#. module: event_booth_sale
#: model:ir.model.fields,help:event_booth_sale.field_product_product__detailed_type
#: model:ir.model.fields,help:event_booth_sale.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Un produit stockable est un produit dont on gère le stock. L'application \"Inventaire\" doit être installée. \n"
"Un produit consommable est un produit pour lequel le stock n'est pas géré.\n"
"Un service est un produit immatériel que vous fournissez."

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_booth_ids
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__event_booth_id
msgid "Booth"
msgstr "Stand"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_booth_category_id
msgid "Booth Category"
msgstr "Catégorie de stand"

#. module: event_booth_sale
#: model:ir.model.fields,help:event_booth_sale.field_event_booth_configurator__event_booth_category_available_ids
msgid "Booth Category for which booths are still available. Used in frontend"
msgstr ""
"Catégorie de stand pour laquelle des stands sont encore disponibles. Utilisé"
" en frontend"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order__event_booth_count
msgid "Booth Count"
msgstr "Nombre de stands"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_category_view_form
msgid "Booth Details"
msgstr "Détails du stand"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_form
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_tree
msgid "Booth Registration"
msgstr "Réservation de stand"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order__event_booth_ids
#: model_terms:ir.ui.view,arch_db:event_booth_sale.sale_order_view_form
msgid "Booths"
msgstr "Stands"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_category_id
msgid "Booths Category"
msgstr "Catégorie des stands"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_configurator_view_form
msgid "Cancel"
msgstr "Annuler"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_ids
msgid "Confirmed Booths"
msgstr "Stands confirmés"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_registration_ids
msgid "Confirmed Registration"
msgstr "Inscription confirmée"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_form
msgid "Contact"
msgstr "Contact"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_email
msgid "Contact Email"
msgstr "E-mail du contact"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_name
msgid "Contact Name"
msgstr "Nom du contact"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__contact_phone
msgid "Contact Phone"
msgstr "Téléphone du contact"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__create_uid
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__create_date
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__create_date
msgid "Created on"
msgstr "Créé le"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__currency_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__currency_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_type_booth__currency_id
msgid "Currency"
msgstr "Devise"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__partner_id
msgid "Customer"
msgstr "Client"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_registration_view_form
msgid "Details"
msgstr "Détails"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__display_name
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_id
msgid "Event"
msgstr "Événement"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth
#: model:ir.model.fields.selection,name:event_booth_sale.selection__product_template__detailed_type__event_booth
msgid "Event Booth"
msgstr "Stand d'événement"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth_category
msgid "Event Booth Category"
msgstr "Catégorie de stand d'événement"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__event_booth_category_available_ids
msgid "Event Booth Category Available"
msgstr "Catégorie de stand d'événement disponible"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth_configurator
msgid "Event Booth Configurator"
msgstr "Configurateur de stand d'événement"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_booth_registration
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__event_booth_registration_ids
msgid "Event Booth Registration"
msgstr "Réservation d'un stand d'événement"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_event_type_booth
msgid "Event Booth Template"
msgstr "Modèle de stand d'événement"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__sale_order_line_id
msgid "Final Sale Order Line"
msgstr "Ligne finale de la commande"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__id
msgid "ID"
msgstr "ID"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__image_1920
msgid "Image"
msgstr "Image"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__is_event_booth
msgid "Is Event Booth"
msgstr "Est un stand d'événement"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__is_paid
msgid "Is Paid"
msgstr "Est payé"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__write_uid
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__write_date
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_configurator_view_form
msgid "Ok"
msgstr "Ok"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__sale_order_id
msgid "Order Reference"
msgstr "Référence de commande"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_form_from_event
msgid "Paid"
msgstr "Payé"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_sale_order_line__event_booth_pending_ids
msgid "Pending Booths"
msgstr "Stands en attente"

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/models/sale_order.py:0
#, python-format
msgid ""
"Please make sure all your event-booth related lines are configured before "
"confirming this order:%s"
msgstr ""
"Assurez-vous que toutes les lignes relatives au stand d'événement sont "
"configurées avant de confirmer cette commande :%s"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__price
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__price
#: model:ir.model.fields,field_description:event_booth_sale.field_event_type_booth__price
msgid "Price"
msgstr "Prix"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__price_reduce
msgid "Price Reduce"
msgstr "Réduction de prix"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__price_reduce_taxinc
msgid "Price Reduce Tax inc"
msgstr "Remise toutes charges comprises"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__price_incl
msgid "Price incl"
msgstr "Prix toutes taxes comprises"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_product_template
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_category__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__product_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_type_booth__product_id
msgid "Product"
msgstr "Produit"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_product_product__detailed_type
#: model:ir.model.fields,field_description:event_booth_sale.field_product_template__detailed_type
msgid "Product Type"
msgstr "Type de produit"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_product_product
msgid "Product Variant"
msgstr "Variante de produit"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_form_from_event
msgid "Registrations"
msgstr "Inscriptions"

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/models/sale_order_line.py:0
#, python-format
msgid "Registrations from the same Order Line must belong to a single event."
msgstr ""
"Les inscriptions d'une même ligne de commande doivent appartenir à un seul "
"événement."

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth__sale_order_line_registration_ids
msgid "SO Lines with reservations"
msgstr "Lignes de commande qui contiennent des réservations"

#. module: event_booth_sale
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_configurator__sale_order_line_id
#: model:ir.model.fields,field_description:event_booth_sale.field_event_booth_registration__sale_order_line_id
msgid "Sale Order Line"
msgstr "Ligne de commande"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_sale_order
msgid "Sales Order"
msgstr "Commande client"

#. module: event_booth_sale
#: model:ir.model,name:event_booth_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Ligne de commande"

#. module: event_booth_sale
#: model:ir.actions.act_window,name:event_booth_sale.event_booth_configurator_action
msgid "Select an event booth"
msgstr "Sélectionnez un stand d'événement"

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/models/sale_order_line.py:0
#, python-format
msgid ""
"The following booths are unavailable, please remove them to continue : "
"%(booth_names)s"
msgstr ""
"Les stands suivants sont indisponibles, veuillez les retirer pour continuer "
":%(booth_names)s"

#. module: event_booth_sale
#: model:ir.model.constraint,message:event_booth_sale.constraint_event_booth_registration_unique_registration
msgid "There can be only one registration for a booth by sale order line"
msgstr ""
"Il ne peut y avoir qu'une seule inscription pour un stand par ligne de "
"commande"

#. module: event_booth_sale
#: model_terms:ir.ui.view,arch_db:event_booth_sale.event_booth_view_tree_from_event
msgid "Total"
msgstr "Total"

#. module: event_booth_sale
#: model:ir.model.fields,help:event_booth_sale.field_sale_order_line__event_booth_pending_ids
msgid "Used to create registration when providing the desired event booth."
msgstr ""
"Utilisé pour créer une inscription lors de la fourniture du stand "
"d'événement souhaité."

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/models/event_booth.py:0
#, python-format
msgid ""
"You can't delete the following booths as they are linked to sales orders: "
"%(booths)s"
msgstr ""
"Vous ne pouvez pas supprimer les stands suivants, car ils sont liés à des "
"commandes clients : %(booths)s"

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/wizard/event_booth_configurator.py:0
#, python-format
msgid "You have to select at least one booth."
msgstr "Vous devez sélectionner au moins un stand."

#. module: event_booth_sale
#. odoo-python
#: code:addons/event_booth_sale/models/event_booth_registration.py:0
#, python-format
msgid ""
"Your order has been cancelled because the following booths have been "
"reserved"
msgstr "Votre commande a été annulée car les stands suivants ont été réservés"
