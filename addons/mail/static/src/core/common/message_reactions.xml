<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">
<t t-name="mail.MessageReactions">
    <div class="position-relative d-flex flex-wrap"
        t-att-class="{
            'flex-row-reverse me-3': env.inChatWindow and env.alignedRight,
            'ms-3': !(env.inChatWindow and env.alignedRight) and (props.message.is_discussion),
        }"
        t-attf-class="{{ props.message.is_discussion ? 'mt-n2' : 'mt-1' }}">
        <button t-foreach="props.message.reactions" t-as="reaction" t-key="reaction.content" class="o-mail-MessageReaction btn d-flex p-0 border rounded-1 mb-1"
            t-on-click="() => this.onClickReaction(reaction)"
            t-on-contextmenu="onContextMenu"
            t-att-class="{
                'o-selfReacted border-primary text-primary fw-bolder': hasSelfReacted(reaction),
                'bg-view': !hasSelfReacted(reaction),
                'ms-1': env.inChatWindow and env.alignedRight,
                'me-1': !(env.inChatWindow and env.alignedRight),
         }" t-att-title="getReactionSummary(reaction)">
            <span class="mx-1" t-esc="reaction.content"/>
            <span class="mx-1" t-esc="reaction.count"/>
        </button>
    </div>
</t>
</templates>
