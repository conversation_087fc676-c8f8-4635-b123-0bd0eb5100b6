<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="discuss.CallMenu">
        <div class="dropdown" t-attf-class="{{ className }}" t-ref="root">
            <button t-if="rtc.state.channel" class="px-3 user-select-none dropdown-toggle o-no-caret o-dropdown--narrow" t-att-title="buttonTitle" role="button" t-on-click="() => this.threadService.open(this.rtc.state.channel)">
                <div class="o-discuss-CallMenu-buttonContent d-flex align-items-center">
                    <span class="position-relative me-2">
                        <i class="fa me-2" t-att-class="{
                            'fa-microphone': !rtc.state.sendCamera and !rtc.state.sendScreen,
                            'fa-video-camera': rtc.state.sendCamera,
                            'fa-desktop': rtc.state.sendScreen,
                        }"/>
                        <small class="position-absolute top-0 end-0 bottom-0 mt-n3 pt-1">
                            <i class="o-discuss-CallMenu-dot fa fa-circle text-warning small"/>
                        </small>
                    </span>
                    <em class="text-truncate" t-esc="rtc.state.channel.displayName"/>
                </div>
            </button>
        </div>
    </t>

</templates>
