# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* point_of_sale
# 
# Translators:
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON>, 2025
# Wil <PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 20:36+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Sarah <PERSON>, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid ""
"\n"
"This issue occurs because the quantity becomes zero after rounding during the conversion. To fix this, adjust the conversion factors or rounding method to ensure that even the smallest quantity in the original unit does not round down to zero in the target unit."
msgstr ""
"\n"
"이 문제는 변환 과정에서 수량이 0으로 반올림될 때 발생합니다. 이 문제를 해결하려면 변환 계수 또는 반올림 방법을 수정하여 원래 단위의 가장 작은 수량도 대상 단위에서 0 이상으로 유지되도록 합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid " - From \"%s\" to \"%s\""
msgstr " - \"%s\" 부터 \"%s\" 까지"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid " - closing"
msgstr " - 종료"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid " - opening"
msgstr " - 시작"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid " REFUND"
msgstr "환불"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_printer__printer_type__iot
msgid " Use a printer connected to the IoT Box"
msgstr "IoT Box에 연결된 프린터 사용"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "% Disc"
msgstr "% 할인"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "%(pos_name)s (not used)"
msgstr "%(pos_name)s (사용하지 않음)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.js:0
#, python-format
msgid "%(vatLabel)s: %(vatId)s"
msgstr "%(vatLabel)s: %(vatId)s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s POS payment of %s in %s"
msgstr "%s의 %s POS 결제액 %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.js:0
#, python-format
msgid "%s customer(s) found for \"%s\"."
msgstr "\"%s\" 관련 %s 고객이 검색되었습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "%s fiscal position(s) added to the configuration."
msgstr "%s 재정 위치가 설정에 추가되었습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"%s has a total amount of %s, are you sure you want to delete this order?"
msgstr "%s의 총액은 %s입니다. 이 주문을 삭제하시겠습니까?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid "%s product(s) found for \"%s\"."
msgstr "\"%s\"에 대해 %s 품목이 검색되었습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s untaxed"
msgstr "%s 비과세"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "%s with %s"
msgstr "%s 약속, 고객명 %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "(RESCUE FOR %(session)s)"
msgstr "(%(session)s에 대한 복구)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "(as of opening)"
msgstr "(시작 기준)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "+ New Shop"
msgstr "+ 새 상점 추가"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "/pos/ticket and use the code below to request an invoice online"
msgstr "/pos/ticket 이며 온라인 청구서를 요청하려면 아래의 코드를 사용하시기 바랍니다"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "0.00"
msgstr "0.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "00014-001-0001"
msgstr "00014-001-0001"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "10"
msgstr "10"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "100.00"
msgstr "100.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "1000"
msgstr "1000"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "10000"
msgstr "10000"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "10000.00"
msgstr "10000.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "123.45"
msgstr "123.45"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "1234567890"
msgstr "1234567890"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "2-03-2000 9:00 AM"
msgstr "2-03-2000 9:00 AM"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "45"
msgstr "45"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__ticket_code
msgid ""
"5 digits alphanumeric code to be used by portal user to request an invoice"
msgstr "포털 사용자가 청구서를 요청할 때 사용하는 5자리 영숫자 코드"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "5.00"
msgstr "5.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "50.00"
msgstr "50.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "567789"
msgstr "567789"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "95.00"
msgstr "95.00"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "987657"
msgstr "987657"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "99.99"
msgstr "99.99"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_pos_kanban
msgid ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-shopping-bag\" role=\"img\" aria-label=\"Shopping "
"cart\" title=\"Shopping cart\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all "
"PoS.\" pos-data-toggle=\"tooltip\"/>"
msgstr ""
"<i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all "
"PoS.\" pos-data-toggle=\"tooltip\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "<i class=\"fa fa-pencil\"/> Edit"
msgstr "<i class=\"fa fa-pencil\"/> 편집"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>How to manage tax-included prices"
msgstr "<i class=\"oi oi-fw oi-arrow-right\"/>세금 포함 가격 관리 방법"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"<p>Dear %(client_name)s,<br/>Here is your electronic ticket for the "
"%(pos_name)s. </p>"
msgstr "<p>안녕하세요 %(client_name)s님,<br/>%(pos_name)s에 대한 전자 티켓입니다. </p>"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#, python-format
msgid ""
"<p>Ready to have a look at the <b>POS Interface</b>? Let's start our first "
"session.</p>"
msgstr "<p><b>POS 인터페이스</b>를 살펴보실 준비가 되셨나요? 첫번째 세션을 시작합니다.</p>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Barcodes</span>\n"
"                                <i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all PoS.\" pos-data-toggle=\"tooltip\"/>"
msgstr ""
"<span class=\"o_form_label\">바코드</span>\n"
"                                <i class=\"fa fa-info-circle me-1\" title=\"This setting is common to all PoS.\" pos-data-toggle=\"tooltip\"/>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Cash Register</span>"
msgstr "<span class=\"o_stat_text\">금전등록기</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "<span class=\"o_stat_text\">Journal Items</span>"
msgstr "<span class=\"o_stat_text\">전표 항목</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "<span class=\"oe_inline\"><b>Skip Preview Screen</b></span>"
msgstr "<span class=\"oe_inline\"><b>미리보기 화면 통과</b></span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "<span invisible=\"is_total_cost_computed\">TBD</span>"
msgstr "<span invisible=\"is_total_cost_computed\">미정</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Balance</span>"
msgstr "<span>잔액</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Closing</span>"
msgstr "<span>마감</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>Reporting</span>"
msgstr "<span>보고</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "<span>View</span>"
msgstr "<span>보기</span>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid ""
"<strong> &gt; Payment Terminals</strong>\n"
"                                    in order to install a Payment Terminal and make a fully integrated payment method."
msgstr ""
"<strong> &gt; 결제용 단말기</strong>\n"
"                                    결제용 단말기를 설치하고 결제 방법을 하나로 통합하기 위한 것입니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Amount of discounts</strong>:"
msgstr "<strong>할인 금액</strong>:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "<strong>Amounting to:</strong>"
msgstr "<strong>금액:</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Config names</strong>"
msgstr "<strong>설정명</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>End of session note:</strong>"
msgstr "<strong>세션 종료 메모:</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Number of discounts</strong>:"
msgstr "<strong>할인 횟수</strong>:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Opening of session note:</strong>"
msgstr "<strong>세션 시작 메모:</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "<strong>Source Invoice:</strong>"
msgstr "<strong>원본 청구서:</strong>"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "<strong>Total</strong>"
msgstr "<strong>합계</strong>"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "? Clicking \"Confirm\" will validate the payment."
msgstr "? \"확인\"을 클릭하면 결제가 승인됩니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "A Customer Name Is Required"
msgstr "고객 이름이 필요합니다"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__uuid
msgid ""
"A globally unique identifier for this pos configuration, used to prevent "
"conflicts in client-generated data."
msgstr "이 POS 환경 설정의 전역 고유 식별 기호로 클라이언트 생성 데이터의 충돌을 방지하는 데 사용됩니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__login_number
msgid ""
"A sequence number that is incremented each time a user resumes the pos "
"session"
msgstr "사용자가 점포판매시스템 세션을 재개할 때마다 증가되는 순서 번호"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__sequence_number
msgid "A sequence number that is incremented with each order"
msgstr "각 주문에 따라 증가되는 순서 번호"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
msgid ""
"A session is a period of time, usually one day, during which you sell "
"through the Point of Sale."
msgstr "세션은 일반적으로 하루 동안 POS를 통해 판매하는 기간입니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"A session is currently opened for this PoS. Some settings can only be "
"changed after the session is closed."
msgstr "현재 이 POS에 열려 있는 세션이 있습니다. 일부 설정 항목의 경우 세션을 종료한 경우에만 변경할 수 있습니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sequence_number
msgid "A session-unique sequence number for the order"
msgstr "주문에 대한 세션-고유 순서 번호"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_footer
msgid "A short text that will be inserted as a footer in the printed receipt."
msgstr "인쇄된 영수증에 꼬리말로 삽입되는 짧은 글."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__receipt_header
msgid "A short text that will be inserted as a header in the printed receipt."
msgstr "인쇄된 영수증에 머리말로 삽입되는 짧은글."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__detailed_type
#: model:ir.model.fields,help:point_of_sale.field_product_template__detailed_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"저장 가능 품목이란 재고 관리가 가능한 품목을 의미하며, 사용을 위해서는 재고 관리 앱을 설치하셔야 합니다.\n"
"소모품은 재고가 따로 관리되지 않는 품목입니다.\n"
"서비스는 회사 측에서 제공하는 비물질적 재화입니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid ""
"A valid product already exists for Point of Sale. Therefore, demonstration "
"products cannot be loaded."
msgstr "POS에 유효한 품목이 이미 존재하여 데모 품목을 불러올 수 없습니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "ALL POS"
msgstr "전체 POS"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
#, python-format
msgid "AMOUNT"
msgstr "금액"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept customer tips or convert their change to a tip"
msgstr "고객 팁 수락 또는 변경 사항을 팁으로 변환"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a PayTM payment terminal"
msgstr "PayTM 결제 단말기로 결제 수락"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Six payment terminal"
msgstr "Six 결제용 단말기로 결제 승인"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Stripe payment terminal"
msgstr "Stripe 결제용 단말기로 결제 승인"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with a Vantiv payment terminal"
msgstr "Vantiv 결제 단말기로 결제 수락"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Accept payments with an Adyen payment terminal"
msgstr "Adyen 결제 단말기로 결제 수락"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_warning
msgid "Access warning"
msgstr "사용 권한 경고"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Account"
msgstr "계정"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_cash_rounding
msgid "Account Cash Rounding"
msgstr "계정 현금 반올림"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_chart_template
msgid "Account Chart Template"
msgstr "계정과목 일람표 서식"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__account_move_id
msgid "Account Move"
msgstr "회계 이동"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__bank_payment_ids
msgid "Account payments representing aggregated and bank split payments."
msgstr "전체 계정 결제 및 은행 분할 납부"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Accounting"
msgstr "회계"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__invoice_journal_id
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_invoice_journal_id
msgid "Accounting journal used to create invoices."
msgstr "청구서 생성에 사용되는 회계 전표"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,help:point_of_sale.field_pos_order__sale_journal
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_journal_id
msgid ""
"Accounting journal used to post POS session journal entries and POS invoice "
"payments."
msgstr "POS 세션 전표 입력 및 POS 청구서 결제를 진행하기 위한 회계 전표입니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__active
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__active
msgid "Active"
msgstr "활성화"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_ids
msgid "Activities"
msgstr "활동"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "활동 예외 장식"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_state
msgid "Activity State"
msgstr "활동 상태"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_icon
msgid "Activity Type Icon"
msgstr "활동 유형 아이콘"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/textarea_popup.js:0
#, python-format
msgid "Add"
msgstr "추가"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_note_button/customer_note_button.js:0
#, python-format
msgid "Add Customer Note"
msgstr "고객 메모 추가"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Add Tip"
msgstr "팁 추가"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_ticket_unique_code
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__point_of_sale_ticket_unique_code
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Add a 5-digit code on the receipt to allow the user to request the invoice "
"for an order on the portal."
msgstr "사용자가 포털에서 주문과 관련된 청구서를 요청할 수 있도록 영수증에 5자리 코드를 추가합니다. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_use_ticket_qr_code
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__point_of_sale_use_ticket_qr_code
msgid ""
"Add a QR code on the ticket, which the user can scan to request the invoice "
"linked to its order."
msgstr "티켓에 QR 코드를 추가하면 사용자가 이를 스캔하여 주문과 관련된 청구서를 요청할 수 있습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Add a closing note..."
msgstr "종료 메모 추가"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Add a custom message to header and footer"
msgstr "머리말과 꼬리말에 사용자 정의 메시지 추가"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Add a customer"
msgstr "고객 추가"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid "Add a new payment method"
msgstr "새로운 결제 수단 추가"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid "Add a new restaurant order printer"
msgstr "새로운 식당 주문 프린터 추가"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/input_popups/textarea_popup.xml:0
#, python-format
msgid "Add a note..."
msgstr "메모 추가"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Add an opening note..."
msgstr "시작 메모 추가"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
#, python-format
msgid "Add to order"
msgstr "주문 추가"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required information:"
msgstr "필수 추가 정보:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required invoicing information:"
msgstr "청구서 관련 필수 추가 정보:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Additional required user information:"
msgstr "사용자 관련 필수 추가 정보:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Address"
msgstr "주소"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adds a button to set a global discount"
msgstr "글로벌 할인 설정 버튼 추가"

#. module: point_of_sale
#: model:res.groups,name:point_of_sale.group_pos_manager
msgid "Administrator"
msgstr "관리자"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_control
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_cash_control
msgid "Advanced Cash Control"
msgstr "선급금 관리"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Adyen"
msgstr "Adyen"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_adyen
msgid "Adyen Payment Terminal"
msgstr "Adyen 결제 터미널"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "All active orders"
msgstr "모든 활성화 상태 주문"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All available pricelists must be in the same currency as the company or as "
"the Sales Journal set on this point of sale if you use the Accounting "
"application."
msgstr ""
"회계 응용 프로그램을 사용하는 경우 사용 가능한 모든 가격리스트는 회사와 동일한 통화로 또는 이 POS에 설정된 판매 전표와 같아야 "
"합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"All payment methods must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr "모든 결제 방법은 판매 전표와 동일하거나 회사 통화로 설정해야 합니다."

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_all_sales_lines
msgid "All sales lines"
msgstr "모든 판매 항목"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow Ship Later"
msgstr "지연 배송 허용"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow cashiers to set a discount per line"
msgstr "담당 캐서가 항목별로 할인을 설정하도록 허용"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow to access each other's active orders"
msgstr "각자의 활성화된 주문에 접근하는 것을 허용"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allow to log and switch between selected Employees"
msgstr "선택한 직원 간에 로그인 및 전환 허용"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Allowed"
msgstr "허용함"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__is_total_cost_computed
msgid ""
"Allows to know if all the total cost of the order lines have already been "
"computed"
msgstr "주문 내역의 총액 계산이 이미 완료되었는지 확인하도록 허용"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Allows to know if the total cost has already been computed or not"
msgstr "총액 계산이 이미 완료되었는지 여부를 확인하도록 허용"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__amount
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__amount
#, python-format
msgid "Amount"
msgstr "금액"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__amount_authorized_diff
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_amount_authorized_diff
msgid "Amount Authorized Difference"
msgstr "금액 승인 차이"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__amount_to_balance
msgid "Amount to balance"
msgstr "잔액"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "Amount total"
msgstr "총액"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid ""
"An error has occurred when trying to close the session.\n"
"You will be redirected to the back-end to manually close the session."
msgstr ""
"세션을 종료하는 중에 오류가 발생했습니다.\n"
"수기로 세션을 종료하기 위해 백엔드로 이동합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"An error occurred when loading product prices. Make sure all pricelists are "
"available in the POS."
msgstr "제품 가격을 불러오는 중 오류가 발생했습니다. POS에서 모든 가격표를 사용할 수 있는지 확인해 주세요."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/main.js:0
#, python-format
msgid "An error occurred while loading the Point of Sale: \n"
msgstr "POS를 불러오는 중 오류가 발생했습니다.: \n"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr "점포판매시스템에 대한 내부 ID"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_printer__name
msgid "An internal identification of the printer"
msgstr "프린터에 대한 내부 ID"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Another session is already opened for this point of sale."
msgstr "이 POS에 다른 세션이 이미 열려 있습니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Archived"
msgstr "보관됨"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Are you sure that the customer wants to  pay"
msgstr "고객이 지불하기를 원하십니까"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__direct
msgid "As soon as possible"
msgstr "가능한 빨리"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__closing
msgid "At the session closing (faster)"
msgstr "세션 종료 시 (더 빠름)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__update_stock_quantities
msgid ""
"At the session closing: A picking is created for the entire session when it's closed\n"
" In real time: Each order sent to the server create its own picking"
msgstr ""
"세션 종료 시: 세션 종료 시 전체 세션에 대해 픽업 항목을 생성합니다.\n"
"실시간  진행 시: 주문이 서버로 전송되면 자체 픽업 항목을 생성합니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_attribute_action
#, python-format
msgid "Attributes"
msgstr "속성"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Authorized Difference"
msgstr "승인된 차이"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__auto_validate_terminal_payment
msgid "Auto Validate Terminal Payment"
msgstr "단말기 결제 자동 승인"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__rescue
msgid "Auto-generated session for orphan orders, ignored in constraints"
msgstr "제약 조건에서 무시되는 고아 주문에 대한 자동 생성 세션"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_auto
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_auto
msgid "Automatic Receipt Printing"
msgstr "자동 영수증 인쇄"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_cashdrawer
msgid "Automatically open the cashdrawer."
msgstr "자동으로 금전등록기 열기"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Automatically validate order"
msgstr "자동으로 주문 승인"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_auto_validate_terminal_payment
#: model:ir.model.fields,help:point_of_sale.field_pos_config__auto_validate_terminal_payment
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_auto_validate_terminal_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Automatically validates orders paid with a payment terminal."
msgstr "결제용 단말기로 결제된 주문 자동 승인"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Available"
msgstr "사용 가능"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_available_categ_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_available_categ_ids
msgid "Available PoS Product Categories"
msgstr "사용 가능한 PoS 상품 범주"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__available_pricelist_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_available_pricelist_ids
msgid "Available Pricelists"
msgstr "사용 가능한 가격표"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__available_in_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
msgid "Available in POS"
msgstr "POS에서 사용 가능"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__average_price
msgid "Average Price"
msgstr "평균가"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/back_button/back_button.xml:0
#, python-format
msgid "BACK"
msgstr "뒤로 가기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/control_buttons_popup.js:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/reprint_receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Back"
msgstr "뒤로"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Backend"
msgstr "백엔드"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_background_image_1920
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_background_image_1920
msgid "Background Image"
msgstr "배경 이미지"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "Badge ID"
msgstr "배지 ID"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Balance"
msgstr "잔액"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__bank
#, python-format
msgid "Bank"
msgstr "은행"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__bank_payment_ids
msgid "Bank Payments"
msgstr "은행 결제"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "은행거래명세서 내역"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Barcode"
msgstr "바코드"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "바코드 명칭"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_barcode_rule
msgid "Barcode Rule"
msgstr "바코드 규칙"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Barcode Scanner"
msgstr "바코드 스캐너"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Barcode Scanner/Card Reader"
msgstr "바코드 스캐너/카드 리더"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Base"
msgstr "기준액"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Base Amount"
msgstr "기준 금액"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_difference
msgid "Before Closing Difference"
msgstr "차액 마감 전"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Billing address:"
msgstr "청구지 주소:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_bill_tree
msgid "Bills"
msgstr "공급업체 청구서"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Bills & Receipts"
msgstr "청구서 및 영수증"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"쿠폰, 프로모션, 기프트 카드, 적립 제도 등 다양한 종류의 프로그램으로 매출 상승효과를 기대할 수 있습니다. 제품, 고객, 최소 구매 "
"금액, 기간 등에 특정 조건을 설정하거나, 할인 또는 사은품 증정을 할 수 있습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Buffer:"
msgstr "버퍼:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr "브라우저 인쇄를 무시하고 하드웨어 프록시를 통해 인쇄합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "CANCELLED"
msgstr "취소됨"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
#, python-format
msgid "CASH"
msgstr "현금"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "CHANGE"
msgstr "변경"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Can't change customer"
msgstr "고객을 변경할 수 없습니다"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/product_configurator_popup/product_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/confirm_popup/confirm_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/selection_popup.js:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
#, python-format
msgid "Cancel"
msgstr "취소"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Cancel Payment Request"
msgstr "결제 요청 취소"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__cancel
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__cancel
msgid "Cancelled"
msgstr "취소됨"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Cannot modify a tip"
msgstr "팁 항목을 수정할 수 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Cannot return change without a cash payment method"
msgstr "현금 결제 방법 없이 변경을 반환할 수 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__cardholder_name
#, python-format
msgid "Cardholder Name"
msgstr "신용 카드 고객 영문명"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: code:addons/point_of_sale/models/report_sale_details.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__is_cash_count
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__cash
#: model:pos.payment.method,name:point_of_sale.payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Cash"
msgstr "현금"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Cash %s"
msgstr "현금 %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Cash In"
msgstr "시재 입금"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Cash In/Out"
msgstr "현금 입출금"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_journal_id
msgid "Cash Journal"
msgstr "현금 전표"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__statement_line_ids
msgid "Cash Lines"
msgstr "시재 내역"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Cash Move 1"
msgstr "현금 작업 1"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
#: code:addons/point_of_sale/models/report_sale_details.py:0
#, python-format
msgid "Cash Opening"
msgstr "시작 시재"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Cash Out"
msgstr "시재 출금"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__cash_rounding
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Rounding"
msgstr "현금 올림"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_cash_rounding
msgid "Cash Rounding (PoS)"
msgstr "시재 반올림 처리 (POS)"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cash Roundings"
msgstr "현금 올림"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "Cash control - closing"
msgstr "현금 관리 - 마감"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.js:0
#, python-format
msgid "Cash control - opening"
msgstr "현금 관리 - 오픈"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash difference observed during the counting (Loss)"
msgstr "계산 중 발견된 현금 차이(손실)"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash difference observed during the counting (Profit)"
msgstr "계산 중 발견된 현금 차이(이익)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.js:0
#, python-format
msgid "Cash in / out"
msgstr "현금 입/출금"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
#, python-format
msgid "Cash in/out of %s is ignored."
msgstr "%s의 시재 입출금을 반영하지 않습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Cash register"
msgstr "금전등록기"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__rounding_method
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_rounding_method
msgid "Cash rounding"
msgstr "현금 반올림"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_cashdrawer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_cashdrawer
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Cashdrawer"
msgstr "금전등록기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__cashier
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
#, python-format
msgid "Cashier"
msgstr "계산원"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""
"범주는 터치스크린 인터페이스를 통해 \n"
"                  제품을 검색하는 데 사용됩니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/category_selector/category_selector.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_category_kanban
#, python-format
msgid "Category"
msgstr "카테고리"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__name
msgid "Category Name"
msgstr "범주명"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__pos_categ_ids
#: model:ir.model.fields,help:point_of_sale.field_product_template__pos_categ_ids
msgid "Category used in the Point of Sale."
msgstr "POS에서 사용하는 카테고리를 설정합니다."

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_chairs
msgid "Chairs"
msgstr "의자"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Change"
msgstr "변경"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Change Tip"
msgstr "팁 변경"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Change:"
msgstr "변경 :"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,help:point_of_sale.field_product_template__to_weight
msgid ""
"Check if the product should be weighted using the hardware scale "
"integration."
msgstr "통합 연결된 저울을 사용하여 상품의 무게를 측정할지 확인합니다. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_product_product__available_in_pos
#: model:ir.model.fields,help:point_of_sale.field_product_template__available_in_pos
msgid "Check if you want this product to appear in the Point of Sale."
msgstr "이 품목을 POS에 표시할지를 확인합니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,help:point_of_sale.field_uom_uom__is_pos_groupable
msgid ""
"Check if you want to group products of this category in point of sale orders"
msgstr "POS로 이 범주 제품을 그룹화할지 확인하십시오."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__cash_control
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_cash_control
msgid "Check the amount of the cashbox at opening and closing."
msgstr "개폐시 현금출납기 금액을 확인하시기 바랍니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid ""
"Check the internet connection then try to sync again by clicking on the red "
"wifi button (upper right of the screen)."
msgstr "인터넷 연결을 확인하신 후 빨간색 와이파이 버튼 (화면 우측 상단)을 클릭하여 동기화를 다시 시도합니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__child_id
msgid "Children Categories"
msgstr "하위 범주"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Choose a specific fiscal position at the order depending on the kind of "
"customer (tax exempt, onsite vs. takeaway, etc.)."
msgstr "주문할 때 고객 분류 (면세, 현장 또는 테이크아웃 등)에 따라 특정 재정 위치를 선택하세요."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "City"
msgstr "시/군/구"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Click here to close the session"
msgstr "세션을 종료하려면 여기를 클릭하십시오"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__client
msgid "Client"
msgstr "클라이언트"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Close"
msgstr "닫기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Close Session"
msgstr "세션 종료"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Close Session & Post Entries"
msgstr "세션 종료 및 입력 내용 발행"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_close_session_wizard
msgid "Close Session Wizard"
msgstr "세션 마법사 종료"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closed
msgid "Closed & Posted"
msgstr "폐장 & 게시"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__closing_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Closing Control"
msgstr "마감 관리"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__stop_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Closing Date"
msgstr "마감 날짜"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__closing_notes
msgid "Closing Notes"
msgstr "종료 메모"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Closing Session"
msgstr "세션 마감"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Closing difference in %s (%s)"
msgstr "%s 차액 마감 (%s)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Closing note"
msgstr "종료 메모"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "Closing session error"
msgstr "세션 마감 오류"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__value
msgid "Coin/Bill Value"
msgstr "주화/지폐 가치"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_bill
#: model:ir.model,name:point_of_sale.model_pos_bill
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_bill_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_default_bill_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_bill
#, python-format
msgid "Coins/Bills"
msgstr "동전 및 지폐"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__combo_ids
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__combo_ids
msgid "Combinations"
msgstr "혼합"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Combine %s POS payments from %s"
msgstr "%s에서 POS 결제 %s 결합"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__combo_id
#: model:ir.model.fields.selection,name:point_of_sale.selection__product_template__detailed_type__combo
#: model:ir.model.fields.selection,name:point_of_sale.selection__product_template__type__combo
msgid "Combo"
msgstr "콤보"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_combo
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
msgid "Combo Choices"
msgstr "콤보 선택 사항"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__combo_line_ids
msgid "Combo Lines"
msgstr "콤보 내역"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_form
msgid "Combo Name"
msgstr "콤보 이름"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__combo_parent_id
msgid "Combo Parent"
msgstr "상위 콤보"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid "Combo products cannot contains variants or attributes"
msgstr "묶음 상품에는  품목 세부사항이나 속성을 포함할 수 없습니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_form
msgid "Combos"
msgstr "콤보"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_company
msgid "Companies"
msgstr "회사"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__company_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__company_id
msgid "Company"
msgstr "회사"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__company_has_template
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_company_has_template
msgid "Company has chart of accounts"
msgstr "회사에 계정과목 일람표가 있습니다"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
#, python-format
msgid "Complete the selection to proceed"
msgstr "진행하려면 선택을 완료하세요."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_res_config_settings
msgid "Config Settings"
msgstr "환경 설정"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_config_product
msgid "Configuration"
msgstr "설정"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Configurations &gt; Settings"
msgstr "환경 설정"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_tree
msgid "Configure at least one Point of Sale."
msgstr "POS를 하나 이상 구성합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/number_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/text_input_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#, python-format
msgid "Confirm"
msgstr "승인"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/confirm_popup/confirm_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/number_popup.js:0
#, python-format
msgid "Confirm?"
msgstr "승인하시겠습니까?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connect device to your PoS without an IoT Box"
msgstr "IoT 박스 없이 장치를 POS로 연결합니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__other_devices
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_other_devices
msgid "Connect devices to your PoS without an IoT Box."
msgstr "IoT Box없이 장치를 PoS에 연결하십시오."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connect devices using an IoT Box"
msgstr "IoT 박스를 이용하여 장치를 연결합니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Connected Devices"
msgstr "연결된 장치"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.js:0
#, python-format
msgid "Connected, Not Owned"
msgstr "연결됨. 소유하지 않음"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Connecting to Proxy"
msgstr "프록시 연결"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Connection error"
msgstr "연결 오류"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Connection to IoT Box failed"
msgstr "IoT Box에 연결하지 못했습니다"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Connection to the printer failed"
msgstr "프린터 연결에 실패함"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.model,name:point_of_sale.model_res_partner
#, python-format
msgid "Contact"
msgstr "연락처"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Continue"
msgstr "계속"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
msgid "Continue Selling"
msgstr "계속 판매하기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/offline_error_popup.js:0
#, python-format
msgid "Continue with limited functionalities"
msgstr "제한된 기능으로 계속 사용"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid ""
"Conversion Error: The following unit of measure conversions result in a zero"
" quantity due to rounding:"
msgstr "변환 오류: 다음 측정 단위 변환에서 반올림으로 인해 수량이 0이 됩니다:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion Rate"
msgstr "전환율"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__currency_rate
msgid "Conversion rate from company currency to order currency."
msgstr "회사 통화에서 주문 통화로의 전환 비율입니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Cost:"
msgstr "비용:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Counted"
msgstr "계산됨"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Country"
msgstr "국가"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__country_code
msgid "Country Code"
msgstr "국가 코드"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Create"
msgstr "작성"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "Create a new POS order"
msgstr "새로운 POS 주문 생성"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_kanban
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_config_tree
msgid "Create a new PoS"
msgstr "새로운 POS 생성"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid "Create a new product variant"
msgstr "새로운 세부선택 품목 생성"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__create_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_uid
msgid "Created by"
msgstr "작성자"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__create_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__create_date
msgid "Created on"
msgstr "작성일자"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__currency_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__currency_id
msgid "Currency"
msgstr "통화"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__currency_rate
msgid "Currency Rate"
msgstr "환율"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_id
msgid "Current Session"
msgstr "현재 세션"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_user_id
msgid "Current Session Responsible"
msgstr "현재 세션 담당자"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__current_session_state
msgid "Current Session State"
msgstr "현재 세션 상태"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_custom
msgid "Custom"
msgstr "사용자 정의"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_header_or_footer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_header_or_footer
msgid "Custom Header & Footer"
msgstr "사용자 지정 머릿글 및 바닥글"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__custom_attribute_value_ids
msgid "Custom Values"
msgstr "맞춤 값"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_button/customer_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_button/customer_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_button/customer_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__partner_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__partner_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#, python-format
msgid "Customer"
msgstr "고객"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_payment_method__type__pay_later
#, python-format
msgid "Customer Account"
msgstr "고객 계정"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Customer Display"
msgstr "고객용 화면"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_via_proxy
msgid "Customer Facing Display"
msgstr "고객용 디스플레이"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Customer Invoice"
msgstr "고객 청구서"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/customer_note_button/customer_note_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__customer_note
#, python-format
msgid "Customer Note"
msgstr "고객 관련 메모"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__access_url
msgid "Customer Portal URL"
msgstr "고객 포털 URL"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Customer Required"
msgstr "고객 필수"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.xml:0
#, python-format
msgid "Customer Screen"
msgstr "고객 화면"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.xml:0
#, python-format
msgid "Customer Screen Connected"
msgstr "고객 화면이 연결되었습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.js:0
#, python-format
msgid "Customer Screen Unsupported. Please upgrade the IoT Box"
msgstr "고객 화면이 지원되지 않습니다. IoT 박스를 업그레이드하십시오."

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#, python-format
msgid "Customer is required for %s payment method."
msgstr "고객에게 %s 결제 방법이 필수입니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Customer tips, cannot be modified directly"
msgstr "고객이 결제한 팁입니다. 직접 수정할 수 없습니다."

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_customer
msgid "Customers"
msgstr "고객"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "DEMO_PRODUCT_NAME"
msgstr "DEMO_PRODUCT_NAME"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "DEMO_REF"
msgstr "DEMO_REF"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
#, python-format
msgid "DETAILS"
msgstr "세부 내용"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Daily Sale"
msgstr "일일 판매"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Daily Sales Report"
msgstr "일일 판매 보고서"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session_filtered
msgid "Daily sessions hold sales from your Point of Sale."
msgstr "일일 세션에 POS에서의 판매 내용이 있습니다."

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_dashboard
msgid "Dashboard"
msgstr "현황판"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__date_order
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_date
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
#, python-format
msgid "Date"
msgstr "날짜"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
#, python-format
msgid "DatePicker"
msgstr "날짜 선택기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Days"
msgstr "일"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Debug Window"
msgstr "디버그 창"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default"
msgstr "기본"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__account_default_pos_receivable_account_id
msgid "Default Account Receivable (PoS)"
msgstr "기본 미수금 (POS)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__default_fiscal_position_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_default_fiscal_position_id
msgid "Default Fiscal Position"
msgstr "기본 재정 위치"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Journals"
msgstr "기본 전표"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.js:0
#, python-format
msgid "Default Price"
msgstr "기본 가격"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_pricelist_id
msgid "Default Pricelist"
msgstr "기본 가격표"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__sale_tax_id
msgid "Default Sale Tax"
msgstr "기본 판매세"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Sales Tax"
msgstr "기본 판매세"

#. module: point_of_sale
#: model:account.tax,name:point_of_sale.pos_taxes_0
msgid "Default Tax for PoS"
msgstr "POS 세금 기본값"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default Temporary Account"
msgstr "기본 임시 계정"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default journals for orders and invoices"
msgstr "주문 및 청구서용 기본 전표"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Default sales tax for products"
msgstr "제품의 기본 판매 세금"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "모든 재고 운용에 사용되는 기본 단위입니다."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_pos_category_action
msgid "Define a new category"
msgstr "새 범주 정의"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Define the smallest coinage of the currency used to pay by cash"
msgstr "현금 결제 최소 금액 단위를 지정하세요."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__name
msgid ""
"Defines the name of the payment method that will be displayed in the Point "
"of Sale when the payments are selected."
msgstr "결제 선택 시 POS에 표시될 결제 방법 이름을 설정합니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__delay_validation
msgid "Delay Validation"
msgstr "지연 검증"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Delete"
msgstr "삭제"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Delete Paid Orders"
msgstr "결제한 주문 삭제하기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid "Delete Paid Orders?"
msgstr "결제가 완료된 주문을 삭제하시겠습니까?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Delete Unpaid Orders"
msgstr "미결제 주문 삭제하기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid "Delete Unpaid Orders?"
msgstr "결제가 완료되지 않은 주문을 삭제하시겠습니까?"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Demo 3-03-2000 5:00 PM"
msgstr "Demo 3-03-2000 5:00 PM"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Demo Name"
msgstr "데모 이름"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid "Demo products are no longer available"
msgstr "데모 상품을 더 이상 사용할 수 없습니다."

#. module: point_of_sale
#: model:product.template,name:point_of_sale.desk_organizer_product_template
msgid "Desk Organizer"
msgstr "책상 정리함"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.desk_pad_product_template
msgid "Desk Pad"
msgstr "책상 패드"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_desks
msgid "Desks"
msgstr "책상"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_id
msgid "Destination account"
msgstr "대상 계정"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__account_readonly
msgid "Destination account is readonly"
msgstr "대상 계정은 읽기 전용입니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Difference"
msgstr "차액"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Difference at closing PoS session"
msgstr "POS 세션 종료 시의 차액"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_difference
msgid ""
"Difference between the theoretical closing balance and the real closing "
"balance."
msgstr "이론적 결산 잔액과 실질 결산 잔액의 차이."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_digest_digest
msgid "Digest"
msgstr "요약"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Disc.%"
msgstr "할인율"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Disc:"
msgstr "할인율 :"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/store/combo_configurator_popup/combo_configurator_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/date_picker_popup/date_picker_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/number_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/text_input_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/input_popups/textarea_popup.js:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Discard"
msgstr "취소"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/customer_facing_display_button/customer_facing_display_button.js:0
#, python-format
msgid "Disconnected"
msgstr "연결 해제됨"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: model:product.template,name:point_of_sale.product_product_consumable_product_template
#, python-format
msgid "Discount"
msgstr "할인"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__discount
msgid "Discount (%)"
msgstr "할인 (%)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__notice
msgid "Discount Notice"
msgstr "할인 알림"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "Discount:"
msgstr "할인 :"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__discount
msgid "Discounted Product"
msgstr "할인 품목"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Discounts"
msgstr "할인"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Discounts:"
msgstr "할인:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Dismiss"
msgstr "해산"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__display_name
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__display_name
msgid "Display Name"
msgstr "표시명"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Display orders on the preparation display"
msgstr "준비 화면에서 주문 표시"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr "사용 권한 없음, 이 데이터는 사용자 요약 이메일에서 무시됩니다"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid ""
"Do you want to accept payments difference and post a profit/loss journal "
"entry?"
msgstr "결제 차액 승인 후 손익 전표입력을 발행하시겠습니까?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Do you want to open the customer list to select customer?"
msgstr "고객 목록을 열어서 고객을 선택하시겠습니까?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "Do you want to print using the web printer? "
msgstr "웹 프린터로 프린트할까요?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Download Paid Orders"
msgstr "결제한 주문 다운로드"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Download Unpaid Orders"
msgstr "미결제 주문 다운로드"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Download a report with all the sales of the current PoS Session"
msgstr "현재 POS 세션의 전체 판매 내역을 포함하여 보고서 다운로드"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#, python-format
msgid "Download error traceback"
msgstr "오류 추적 다운로드"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid ""
"Each Order Printer has an IP Address that defines the IoT Box/Hardware\n"
"            Proxy where the printer can be found, and a list of product categories.\n"
"            An Order Printer will only print updates for products belonging to one of\n"
"            its categories."
msgstr ""
"각각의 주문 프린터에는 프린터를 인식할 수 있도록 IoT 박스 또는 \n"
"            하드웨어 프록시를 설정하는 IP 주소와 함께 품목 카테고리 목록이 있습니다.\n"
"            주문 프린터는 해당 카테고리에 속하는 품목에 대한 업데이트만을 출력합니다. \n"
"            "

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Edit"
msgstr "편집"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_electronic_scale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Electronic Scale"
msgstr "전자 저울"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Email"
msgstr "이메일"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Email sent."
msgstr "이메일을 전송하였습니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Employees can scan their badge or enter a PIN to log in to a PoS session. "
"These credentials are configurable in the *HR Settings* tab of the employee "
"form."
msgstr ""
"직원은 배지를 스캔하거나 PIN을 입력하여 PoS 세션에 로그인할 수 있습니다. 이 자격 증명은 직원 양식의 *HR 설정* 탭에서 구성할"
" 수 있습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Empty Order"
msgstr "빈 주문"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_scan_via_proxy
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""
"원격으로 연결되어 있는 바코드 스캐너로 바코드 스캔 기능을 활성화하고 Vantiv 카드 리더기로는 카드 인식 기능을 활성화시킬 수 "
"있습니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr "전자 저울 통합을 가능하게 합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Encountered error when loading image. Please try again."
msgstr "이미지를 불러오는 중 오류가 발생했습니다. 다시 시도해주십시오."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__end_date
msgid "End Date"
msgstr "종료일"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end_real
msgid "Ending Balance"
msgstr "기말 잔액"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_popup.js:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Error"
msgstr "오류"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#, python-format
msgid "Error with Traceback"
msgstr "추적 오류"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid "Error! You cannot create recursive categories."
msgstr "오류가 발생했습니다! 순환 참조되는 카테고리는 생성할 수 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Error: no internet connection."
msgstr "오류: 인터넷에 연결되어 있지 않습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Existing orderlines"
msgstr "기존 주문 내역"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#, python-format
msgid "Exit Pos"
msgstr "POS 종료"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Expected"
msgstr "예상됨"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Expected delivery:"
msgstr "예상 배송:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Export Paid Orders"
msgstr "결제한 주문 내보내기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Export Unpaid Orders"
msgstr "미결제 주문 내보내기"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Extra Info"
msgstr "추가 정보"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.fabric_attribute
msgid "Fabric"
msgstr "구조"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__failed_pickings
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__failed_pickings
msgid "Failed Pickings"
msgstr "픽업 실패"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Failed in printing the changes in the order"
msgstr "주문 변경 사항을 인쇄하지 못했습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Financials"
msgstr "재무"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "Finished Importing Orders"
msgstr "주문 가져오기 완료됨"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "재정 위치"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Fiscal Position not found"
msgstr "재정 위치를 찾을 수 없습니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__fiscal_position_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_fiscal_position_ids
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Fiscal Positions"
msgstr "재정 위치"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Flexible Pricelists"
msgstr "변경 가능한 가격표"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Flexible Taxes"
msgstr "변경 가능한 세금"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (협력사)"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "멋진 아이콘 폰트 예: fa-tasks"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Footer"
msgstr "바닥글"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_big_scrollbars
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_big_scrollbars
msgid "For imprecise industrial touchscreens."
msgstr "부정확한 산업용 터치 스크린"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_form_pos_close_session_wizard
#, python-format
msgid "Force Close Session"
msgstr "세션 강제 종료"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Force Done"
msgstr "강제 완료"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Force done"
msgstr "강제 완료"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__force_outstanding_account_id
msgid "Forced Outstanding Account"
msgstr "강제 미결제 계정 "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__split_transactions
msgid ""
"Forces to set a customer when using this payment method and splits the "
"journal entries for each customer. It could slow down the closing process."
msgstr "이 결제 수단을 사용할 경우 고객을 강제 설정하고 고객별로 전표를 입력합니다. 종료 단계의 과정이 느려질 수 있습니다. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Free"
msgstr "무료"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "From invoice payments"
msgstr "청구서 결제에서"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__full_product_name
msgid "Full Product Name"
msgstr "상세 품목명"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_ticket_unique_code
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__point_of_sale_ticket_unique_code
msgid "Generate a code on ticket"
msgstr "티켓에 코드 생성하기"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Generation of your order references"
msgstr "주문 참조 생성"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Get my invoice"
msgstr "내 청구서 받기"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_category__sequence
msgid "Gives the sequence order when displaying a list of product categories."
msgstr "품목 분류 목록을 표시할 때 주문 순서를 제공합니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_discount
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_discount
msgid "Global Discounts"
msgstr "전체 할인"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/back_button/back_button.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/back_button/back_button.xml:0
#, python-format
msgid "Go Back"
msgstr "돌아가기"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Go to"
msgstr "이동하기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Greater than allowed"
msgstr "허용되는 숫자보다 큽니다"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Group By"
msgstr "그룹별"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_uom_category__is_pos_groupable
#: model:ir.model.fields,field_description:point_of_sale.field_uom_uom__is_pos_groupable
msgid "Group Products in POS"
msgstr "POS 그룹 제품"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "HTTPS connection to IoT Box failed"
msgstr "IoT Box에 대한 HTTPS 연결 실패"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Hardware Events"
msgstr "하드웨어 이벤트"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Hardware Status"
msgstr "하드웨어 상태"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__has_active_session
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_has_active_session
msgid "Has Active Session"
msgstr "활성화된 세션 있음"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_control
msgid "Has Cash Control"
msgstr "현금 제어됨"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__has_image
msgid "Has Image"
msgstr "이미지 있음"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__has_refundable_lines
msgid "Has Refundable Lines"
msgstr "환불 가능 내역 있음"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Header"
msgstr "머리글"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Hide Category Images"
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Hide Product Images"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__hide_use_payment_terminal
msgid "Hide Use Payment Terminal"
msgstr "사용 결제 터미널 숨기기"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__id
msgid "ID"
msgstr "ID"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#, python-format
msgid "IMPORTANT: Bug Report From Odoo Point Of Sale"
msgstr "중요 : Odoo POS의 버그 보고서"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__proxy_ip
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_proxy_ip
msgid "IP Address"
msgstr "IP 주소"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon"
msgstr "아이콘"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "예외 활동을 표시하기 위한 아이콘"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__split_transactions
msgid "Identify Customer"
msgstr "고객 확인"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid ""
"If this orderline is a refund, then the refunded orderline is specified in "
"this field."
msgstr "이 주문 내역이 환불에 대한 것인 경우에는 환불된 내역이 이 필드에 지정됩니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__picking_policy
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_picking_policy
msgid ""
"If you deliver all products at once, the delivery order will be scheduled "
"based on the greatest product lead time. Otherwise, it will be based on the "
"shortest."
msgstr ""
"모든 품목을 한 번에 배송하는 경우 최대 제품 리드타임을 기준으로 배송 주문이 예약됩니다. 그렇지 않으면 가장 짧은 시간을 기준으로 "
"합니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display
msgid "Iface Customer Facing Display"
msgstr "iface 고객용 디스플레이"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__image_128
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__image
msgid "Image"
msgstr "이미지"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Import Orders"
msgstr "주문 가져오기"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Improve navigation for imprecise industrial touchscreens"
msgstr "부정확한 산업용 터치 스크린을 위한 탐색 기능 향상"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opened
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "In Progress"
msgstr "진행 중"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "In order to delete a sale, it must be new or cancelled."
msgstr "판매를 삭제하기 위해서는 새로 만들거나 취소해야 합니다."

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__res_company__point_of_sale_update_stock_quantities__real
msgid "In real time (accurate but slower)"
msgstr "실시간 (정확하지만 느림)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Incorrect address for shipping"
msgstr "잘못된 배송지 주소입니다"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Incorrect rounding"
msgstr "잘못된 반올림입니다"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__message
msgid "Information message"
msgstr "정보 메시지"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_start_categ_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_start_categ_id
msgid "Initial Category"
msgstr "초기 범주"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_method_form
msgid ""
"Installing chart of accounts from the General Settings of\n"
"                Invocing/Accounting app will create Bank and Cash payment\n"
"                methods automatically."
msgstr ""
"송장/계정 앱의 일반 설정에서 계정과목 일람표를 설치하면 \n"
"                 은행 및 현금 결제 방법이 \n"
"                 자동으로 생성됩니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_mercury
msgid "Integrated Card Payments"
msgstr "통합 카드 결제"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__receivable_account_id
msgid "Intermediary Account"
msgstr "중개 계정"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Intermediary account used for unidentified customers."
msgstr "확인되지 않은 고객용으로 사용하는 임시 계정"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_category_action
msgid "Internal Categories"
msgstr "내부 범주"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__note
msgid "Internal Notes"
msgstr "내부 메모"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Invalid action"
msgstr "잘못된 작업입니다"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Invalid email."
msgstr "잘못된 이메일입니다"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#, python-format
msgid "Inventory"
msgstr "재고 관리"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Inventory Management"
msgstr "재고 관리"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__account_move
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Invoice"
msgstr "청구서"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__invoice_journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_invoice_journal_id
msgid "Invoice Journal"
msgstr "청구서 전표"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Invoice Name"
msgstr "청구서 이름"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Invoice Request"
msgstr "청구서 요청"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid "Invoice payment for %s (%s) using %s"
msgstr "%s을(를) 사용하여 %s (%s)에 대한 청구서 결제"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__invoiced
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__invoiced
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Invoiced"
msgstr "발행 완료된 청구서"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Invoices"
msgstr "청구서"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Invoicing confirmation"
msgstr "청구서 승인"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "IoT Box"
msgstr "IoT 박스"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "IoT Box IP Address"
msgstr "IoT Box IP 주소"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_invoiced
msgid "Is Invoiced"
msgstr "청구서 발행 완료되었습니다"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__is_kiosk_mode
msgid "Is Kiosk Mode"
msgstr "키오스크 모드입니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_refunded
msgid "Is Refunded"
msgstr "환불되었습니다"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_total_cost_computed
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__is_total_cost_computed
msgid "Is Total Cost Computed"
msgstr "총 비용이 계산되었습니까?"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__is_in_company_currency
msgid "Is Using Company Currency"
msgstr "회사 통화 사용 여부"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_restaurant
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_restaurant
msgid "Is a Bar/Restaurant"
msgstr "바/레스토랑 유무"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_installed_account_accountant
msgid "Is the Full Accounting Installed"
msgstr "전체 회계가 설치 유무"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__is_tipped
msgid "Is this already tipped?"
msgstr "팁이 이미 지불되었나요?"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__is_change
msgid "Is this payment change?"
msgstr "결제 변경인가요?"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_tax.py:0
#, python-format
msgid ""
"It is forbidden to modify a tax used in a POS order not posted. You must "
"close the POS sessions before modifying the tax."
msgstr ""
"게시되지 않은 POS 주문에 사용된 세금을 수정하는 것은 금지되어 있습니다. 세금을 수정하기 전에 POS 세션을 닫아야 합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "It is not allowed to mix refunds and sales"
msgstr "환불과 판매는 혼합할 수 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "It is possible to print your tickets by making use of an IoT Box."
msgstr "IoT 박스를 사용하여 티켓을 인쇄할 수 있습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/debug_manager.js:0
#, python-format
msgid "JS Tests"
msgstr "JS 시험"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_journal
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__journal_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Journal"
msgstr "전표"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__move_id
msgid "Journal Entry"
msgstr "전표 입력"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_account_move_line
msgid "Journal Item"
msgstr "전표 항목"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Journal Items"
msgstr "전표 항목"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total_value
msgid "Kpi Pos Total Value"
msgstr "Kpi Pos 총합"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.led_lamp_product_template
msgid "LED Lamp"
msgstr "LED 램프"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__name
msgid "Label"
msgstr "라벨"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Language"
msgstr "사용 언어"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Laptop model x"
msgstr "노트북 모델 x"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_big_scrollbars
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_big_scrollbars
msgid "Large Scrollbars"
msgstr "대형 스크롤바"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_cash
msgid "Last Session Closing Cash"
msgstr "최근 세션 마감 시재"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__last_session_closing_date
msgid "Last Session Closing Date"
msgstr "최종 세션 마감일"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__write_uid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_close_session_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__write_date
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__last_order_preparation_change
msgid "Last preparation change"
msgstr "최근 준비 변경"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__last_order_preparation_change
msgid "Last printed state of the order"
msgstr "주문 관련 최근 인쇄 상태"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_leather
msgid "Leather"
msgstr "가죽"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Leave a reason here"
msgstr "여기에 이유를 남겨주세요."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the default account from the company setting"
msgstr "회사 설정에서 기본 계정을 사용하시려면 공란으로 두십시오"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Account used as outstanding account when creating accounting payment records for bank payments."
msgstr ""
"공란으로 두시면 회사 설정에서 기본 계정을 사용하실 수 있습니다.\n"
"은행 결제 관련 회계 결제 기록을 생성할 때 미결제 계정으로 사용되는 계정입니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__receivable_account_id
msgid ""
"Leave empty to use the default account from the company setting.\n"
"Overrides the company's receivable account (for Point of Sale) used in the journal entries."
msgstr ""
"공란으로 두시면 회사 설정에서 기본 계정을 사용하실 수 있습니다.\n"
"전표입력되어 있는 미수금 계정 (POS) 항목보다 우선적으로 사용됩니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Leave empty to use the receivable account of customer"
msgstr "고객용 미수금 계정을 사용하시려면 공란으로 두십시오"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__journal_id
msgid ""
"Leave empty to use the receivable account of customer.\n"
"Defines the journal where to book the accumulated payments (or individual payment if Identify Customer is true) after closing the session.\n"
"For cash journal, we directly write to the default account in the journal via statement lines.\n"
"For bank journal, we write to the outstanding account specified in this payment method.\n"
"Only cash and bank journals are allowed."
msgstr ""
"고객의 미수금 계정을 사용하려면 공란으로 두십시오.\n"
"세션 종료 후 누적 결제 (또는 확인된 고객이 해당하는 경우 개별 결제)를 기장할 장부를 설정합니다.\n"
"현금 분개장의 경우, 명세서 내역에 따라 분개장의 기본 계정에 바로 작성됩니다.\n"
"은행 분개장의 경우, 해당 결제 방법에 지정되어 있는 미결제 계좌로 작성됩니다.\n"
"현금 및 은행 분개장만 사용이 허용됩니다."

#. module: point_of_sale
#: model:product.template,name:point_of_sale.letter_tray_product_template
msgid "Letter Tray"
msgstr "편지꽂이"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__manual_discount
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_manual_discount
msgid "Line Discounts"
msgstr "항목 할인"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__name
msgid "Line No"
msgstr "명세 번호"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Load Order"
msgstr "로딩 순서"

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_product_menu
msgid "Load Product Menu"
msgstr "품목 메뉴 불러오기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Loading Image Error"
msgstr "이미지를 불러오는 중 오류 발생"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "Loading..."
msgstr "불러오는 중..."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_customer_facing_display_local
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_local
msgid "Local Customer Facing Display"
msgstr "현지 고객용 디스플레이"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__login_number
msgid "Login Sequence Number"
msgstr "로그인 일련번호"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
#, python-format
msgid "Logo"
msgstr "로고"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__lot_name
msgid "Lot Name"
msgstr "LOT명"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Lot Number"
msgstr "로트 번호"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Lot/Serial Number(s) Required"
msgstr "LOT/일련번호 필요"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__pack_lot_ids
msgid "Lot/serial Number"
msgstr "LOT/일련번호"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.magnetic_board_product_template
msgid "Magnetic Board"
msgstr "마그네틱 보드"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Make Payment"
msgstr "결제하기"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__available_pricelist_ids
msgid ""
"Make several pricelists available in the Point of Sale. You can also apply a"
" pricelist to specific customers from their contact form (in Sales tab). To "
"be valid, this pricelist must be listed here as an available pricelist. "
"Otherwise the default pricelist will apply."
msgstr ""
"POS에서 여러 가격 목록을 사용할 수 있도록 합니다. 연락처 탭(판매 탭)에서 특정 고객에게 가격표를 적용할 수도 있습니다. 유효하려면"
" 이 가격 목록이 사용 가능한 가격 목록으로 여기에 나열되어 있어야 합니다. 그렇지 않으면 기본 가격표가 적용됩니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"Make sure you are using IoT Box v18.12 or higher. Navigate to %s to accept "
"the certificate of your IoT Box."
msgstr "IoT Box v18.12 이상을 사용하고 있는지 확인합니다. %s로 이동하여 IoT 박스의 인증서를 수락합니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Manage promotion that will grant customers discounts or gifts"
msgstr "고객에게 할인 혜택이나 사은품을 증정하는 프로모션 관리"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__margin
msgid "Margin"
msgstr "이윤"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__margin_percent
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__margin_percent
msgid "Margin (%)"
msgstr "마진 (%)"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Margin:"
msgstr "마진:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_margins_costs_accessible_to_every_user
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_margins_costs_accessible_to_every_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Margins & Costs"
msgstr "마진 및 미용"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Maximum Exceeded"
msgstr "최대 초과"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Maximum value reached"
msgstr "최대값 도달"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/offline_error_popup.js:0
#, python-format
msgid ""
"Meanwhile connection is back, Odoo Point of Sale will operate limited "
"operations. Check your connection or continue with limited functionalities"
msgstr ""
"연결이 복구되는 동안 Odoo POS는 제한적으로 운영됩니다. 연결 상태를 확인하거나 제한된 기능을 계속 사용할 수 있습니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_ids
msgid "Messages"
msgstr "메시지"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__name
msgid "Method"
msgstr "방법"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
msgid "Method Name"
msgstr "방법명"

#. module: point_of_sale
#: model:pos.category,name:point_of_sale.pos_category_miscellaneous
msgid "Misc"
msgstr "기타"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Mobile"
msgstr "휴대폰 번호"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__module_pos_hr
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_module_pos_hr
msgid "Module Pos Hr"
msgstr "POS HR 모듈"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.monitor_stand_product_template
msgid "Monitor Stand"
msgstr "모니터 스탠드"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "More info"
msgstr "추가 정보"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "More settings:"
msgstr "추가 설정:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#, python-format
msgid "More..."
msgstr "더 보기"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Multi Employees per Session"
msgstr "세션 당 여러 명의 직원"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Multiple Invoiced Orders Selected"
msgstr ""

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "내 활동 마감일"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "My Sessions"
msgstr "내 세션"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NEW"
msgstr "신규"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#: code:addons/point_of_sale/static/src/app/store/order_change_receipt_template.xml:0
#, python-format
msgid "NOTE"
msgstr "노트"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__name
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_userlabel
#, python-format
msgid "Name"
msgstr "이름"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Need customer to invoice"
msgstr "청구서를 발행할 고객"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need loss account for the following journals to post the lost amount: %s\n"
msgstr "손실 금액에 대한 전표 발행용 손실 계정이 있어야 합니다: %s\n"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Need profit account for the following journals to post the gained amount: %s"
msgstr "수익 금액에 대한 전표 발행용 수익 계정이 있어야 합니다: %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Network Error"
msgstr "네트워크 에러"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__draft
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__draft
msgid "New"
msgstr "신규"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "New Order"
msgstr "새 주문"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "New Session"
msgstr "신규 세션 생성"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.js:0
#, python-format
msgid "New amount"
msgstr "신규 금액"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.newspaper_rack_product_template
msgid "Newspaper Rack"
msgstr "신문걸이"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "다음 활동 캘린더 행사"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "다음 활동 마감일"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_summary
msgid "Next Activity Summary"
msgstr "다음 활동 요약"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_type_id
msgid "Next Activity Type"
msgstr "다음 활동 유형"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Next Order List"
msgstr "다음 주문 목록"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "No"
msgstr "아니오"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "No Point of Sale selected"
msgstr "POS가 선택되지 않았습니다"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
#, python-format
msgid "No Taxes"
msgstr "비과세"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/barcode_reader_service.js:0
#, python-format
msgid ""
"No barcode nomenclature has been configured. This can be changed in the "
"configuration settings."
msgstr "바코드 이름 규칙이 구성되어 있지 않습니다. 구성 설정 메뉴에서 변경하실 수 있습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"No cash statement found for this session. Unable to record returned cash."
msgstr "이 세션에서 발견된 현금 명세서가 없습니다. 현금 반환 기록은 사용할 수 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"No chart of account configured, go to the \"configuration / settings\" menu,"
" and install one from the Invoicing tab."
msgstr "계정과목표가 구성되어 있지 않습니다. \"구성 / 설정\" 메뉴로 이동하여 청구서 탭에서 계정과목표를 설치하시기 바랍니다."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_sale_graph
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all
#: model_terms:ir.actions.act_window,help:point_of_sale.action_report_pos_order_all_filtered
msgid "No data yet!"
msgstr "데이터가 아직 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/report/pos_invoice.py:0
#, python-format
msgid "No link to an invoice for %s."
msgstr "%s의 청구서 링크가 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.js:0
#, python-format
msgid "No more customer found for \"%s\"."
msgstr "\"%s\"에 대해 고객을 찾을 수 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid "No more product found for \"%s\"."
msgstr "\"%s\"에 대해 품목을 찾을 수 없습니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__num_of_products
msgid "No of Products"
msgstr "품목 번호"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "No orders found"
msgstr "주문이 없습니다"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "No products available. Explore"
msgstr "사용할 수 있는 품목이 없습니다. 다음을 확인하세요."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "No products found for"
msgstr "다음에 대한 품목을 찾을 수 없습니다"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "No sale order found."
msgstr "판매 주문 내역이 없습니다"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_session_filtered
msgid "No sessions found"
msgstr "세션을 찾을 수 없습니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "명명법"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.js:0
#, python-format
msgid "None"
msgstr "없음"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/report_sale_details.py:0
#, python-format
msgid "Not Categorized"
msgstr "분류되지 않음"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Not Invoiced"
msgstr "청구서 미발행"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#, python-format
msgid "Note"
msgstr "노트"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Notes"
msgstr "메모"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__nb_print
msgid "Number of Print"
msgstr "인쇄 번호"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refund_orders_count
msgid "Number of Refund Orders"
msgstr "환불 주문 수"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__number_of_rescue_session
msgid "Number of Rescue Session"
msgstr "헬프 세션 수"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refunded_qty
msgid "Number of items refunded in this orderline."
msgstr "이 주문 내역에서 환불된 항목의 수입니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류가 발생한 메시지 수입니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Number of transactions:"
msgstr "거래 수:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "OK"
msgstr "확인"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.office_combo_product_template
msgid "Office combo"
msgstr "사무용 콤보"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Offline Orders"
msgstr "오프라인 주문"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.xml:0
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_popup.js:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.js:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/select_lot_popup.xml:0
#: code:addons/point_of_sale/static/src/app/utils/confirm_popup/confirm_popup.js:0
#, python-format
msgid "Ok"
msgstr "확인"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Ongoing"
msgstr "진행중"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"Only a negative quantity is allowed for this refund line. Click on +/- to "
"modify the quantity to be refunded."
msgstr "이 환불 내역의 수량에는 음수만 입력할 수 있습니다. +/- 로 환불 수량을 수정하십시오."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Only administrators can edit receipt headers and footers"
msgstr "관리자만 영수증 머리글과 바닥글을 편집할 수 있습니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__only_round_cash_method
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_only_round_cash_method
msgid "Only apply rounding on cash"
msgstr "현금에만 반올림 적용"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Only journals of type 'Cash' or 'Bank' could be used with payment methods."
msgstr "'현금' 또는 '은행 계좌' 유형의 전표만 결제 수단으로 사용할 수 있습니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Only on cash methods"
msgstr "현금 방식만 가능"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__restrict_price_control
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_restrict_price_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Only users with Manager access rights for PoS app can modify the product "
"prices on orders."
msgstr "PoS 앱에 대한 관리자 액세스 권한이 있는 사용자만 주문시 제품 가격을 수정할 수 있습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Only web-compatible Image formats such as .png or .jpeg are supported."
msgstr ".png 나 .jpe 형식과 같은 웹 호환 이미지 형식만 지원됩니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Open Cashbox"
msgstr "현금보관함 열기"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Open PoS sessions that are using this payment method."
msgstr "이 결제 방법을 사용하는 PoS 세션을 엽니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Open Session"
msgstr "세션 열기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Open session"
msgstr "세션 열기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Open the money details popup"
msgstr "자금 세부 정보 팝업 열기"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__user_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opened By"
msgstr "개설자"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opened by"
msgstr "개설자"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Opening"
msgstr "시작"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Opening Cash Control"
msgstr "현금 관리 열기"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_session__state__opening_control
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Opening Control"
msgstr "개시 관리"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__start_at
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
msgid "Opening Date"
msgstr "개시일"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__opening_notes
msgid "Opening Notes"
msgstr "시작 메모"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Opening balance summed to all cash transactions."
msgstr "전체 현금 거래에 대한 기초 잔액 합계액입니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Opening cash"
msgstr "시작 금액"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/store/cash_opening_popup/cash_opening_popup.xml:0
#, python-format
msgid "Opening note"
msgstr "시작 메모"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_type_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_picking_type_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Operation Type"
msgstr "작업 유형"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Operation types show up in the Inventory dashboard."
msgstr "작업 유형이 목록 현황판에 나타납니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/scale_screen/scale_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__pos_order_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__order_id
#, python-format
msgid "Order"
msgstr "주문"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Order %s"
msgstr "%s 주문"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Order %s is not fully paid."
msgstr "%s 주문이 완전히 지불되지 않았습니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_count
msgid "Order Count"
msgstr "주문 수"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__date
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Order Date"
msgstr "주문일"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_sequence_id
msgid "Order IDs Sequence"
msgstr "주문 ID 순서"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__sequence_line_id
msgid "Order Line IDs Sequence"
msgstr "주문 내역 ID 순서"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__lines
msgid "Order Lines"
msgstr "주문 명세"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tracking_number
#, python-format
msgid "Order Number"
msgstr "주문 번호"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_order_printer
msgid "Order Printer"
msgstr "주문용 프린터"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__printer_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_printer_ids
msgid "Order Printers"
msgstr "주문용 프린터"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_printer_form
msgid ""
"Order Printers are used by restaurants and bars to print the\n"
"            order updates in the kitchen/bar when the waiter updates the order."
msgstr ""
"주문용 프린터는 웨이터가 레스토랑과 바에서 받은 주문을 \n"
"          변경하는 경우 키친이나 바에서 주문 업데이트 내용을 인쇄하는데 사용합니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__order_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__order_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Order Ref"
msgstr "주문 참조"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Order Reference"
msgstr "주문 참조"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__sequence_number
msgid "Order Sequence Number"
msgstr "주문 순서 번호"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Order lines"
msgstr "주문 명세"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Order number"
msgstr "주문 번호"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Order reference"
msgstr "주문 참조"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.js:0
#, python-format
msgid "Order saved for later"
msgstr "나중에 사용하기 위해 저장된 주문"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Orderlines in this field are the lines that refunded this orderline."
msgstr "이 필드의 주문 내역은 해당 주문을 환불한 내용입니다."

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_filtered
#: model:ir.actions.act_window,name:point_of_sale.action_pos_pos_form
#: model:ir.actions.act_window,name:point_of_sale.action_pos_sale_graph
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__order_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_ofsale
#: model:ir.ui.menu,name:point_of_sale.menu_report_pos_order_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Orders"
msgstr "주문"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_order_all_filtered
msgid "Orders Analysis"
msgstr "주문 분석"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__lst_price
msgid "Original Price"
msgstr "원래 가격"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__other_devices
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_other_devices
msgid "Other Devices"
msgstr "기타 디바이스"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Other Information"
msgstr "기타 정보"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Others"
msgstr "기타"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__outstanding_account_id
msgid "Outstanding Account"
msgstr "미결제 계정"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_procurement_group__pos_order_id
msgid "POS Order"
msgstr "POS 주문"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS Order %s"
msgstr "POS %s 주문"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line_form
msgid "POS Order line"
msgstr "점포판매시스템 주문 명세"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "POS Order lines"
msgstr "점포판매시스템 주문 명세"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree
msgid "POS Orders"
msgstr "점포판매시스템 주문"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_tree_all_sales_lines
msgid "POS Orders lines"
msgstr "점포판매시스템 주문 명세"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_method_id
msgid "POS Payment Method"
msgstr "POS 결제 방법"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_printer_form
msgid "POS Printer"
msgstr "POS 프린터"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_product_tree_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_search_view_pos
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_tree_view
msgid "POS Product Category"
msgstr "POS 품목 카테고리"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_digest_digest__kpi_pos_total
msgid "POS Sales"
msgstr "POS 판매"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_session_id
msgid "POS Session"
msgstr "POS 세션"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "POS order line %s"
msgstr "POS 주문 명세 %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__paid
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__paid
#, python-format
msgid "Paid"
msgstr "지불됨"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__parent_id
msgid "Parent Category"
msgstr "상위 카테고리"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Partner"
msgstr "협력사"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/action_pad/action_pad.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Pay"
msgstr "지불"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment
msgid "Pay Order"
msgstr "지불 주문"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PayTM"
msgstr "PayTM"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_paytm
msgid "PayTM Payment Terminal"
msgstr "PayTM 결제 단말기"

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/wizard/pos_payment.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Payment"
msgstr "결제"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_date
msgid "Payment Date"
msgstr "지불일"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_method_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_method_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Payment Method"
msgstr "결제 방법"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_method_form
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__payment_method_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_payment_method_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment_method
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Methods"
msgstr "지급 방법"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Payment Name Demo"
msgstr "결제 이름 데모"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__ticket
msgid "Payment Receipt Info"
msgstr "결제 영수증 정보"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__payment_name
msgid "Payment Reference"
msgstr "결제 참조 정보"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__payment_status
msgid "Payment Status"
msgstr "결제 상태"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Payment Successful"
msgstr "결제 성공"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment Terminals"
msgstr "결제 단말기"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__transaction_id
msgid "Payment Transaction ID"
msgstr "결제 거래 ID"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Payment method"
msgstr "결제 방법"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Payment methods available"
msgstr "사용 가능한 결제 방법"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Payment request pending"
msgstr "결제 요청 보류"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Payment reversed"
msgstr "결제 취소됨"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_payment_form
#: model:ir.model,name:point_of_sale.model_account_payment
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__payment_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_payment
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Payments"
msgstr "결제"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid "Payments Difference"
msgstr "지불 차액"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_payment_methods_tree
msgid "Payments Methods"
msgstr "결제 수단"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "Payments in"
msgstr "결제"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "Payments:"
msgstr "결제 :"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__user_id
msgid ""
"Person who uses the cash register. It can be a reliever, a student or an "
"interim employee."
msgstr "금전 등록기를 사용하는 사람. 구제자, 학생 또는 임시 직원 일 수 있습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Phone"
msgstr "전화번호"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pick which product categories are available"
msgstr "사용 가능한 품목 카테고리 선택"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_ids
msgid "Picking"
msgstr "선별"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__picking_count
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__picking_count
msgid "Picking Count"
msgstr "선별 숫자"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#, python-format
msgid "Picking POS"
msgstr "POS 선별"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking_type
msgid "Picking Type"
msgstr "선별 유형"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#, python-format
msgid "Pickings"
msgstr "선별"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Picture"
msgstr "사진"

#. module: point_of_sale
#: model:product.attribute.value,name:point_of_sale.fabric_attribute_plastic
msgid "Plastic"
msgstr "플라스틱"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Please Confirm Large Amount"
msgstr "큰 금액을 확인하십시오"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_combo.py:0
#, python-format
msgid "Please add products in combo."
msgstr "콤보로 품목을 추가하세요."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid "Please check if the IoT Box is still connected."
msgstr "IoT Box가 여전히 연결되어 있는지 확인하십시오."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/base_printer.js:0
#, python-format
msgid ""
"Please check if the printer is still connected. \n"
"Some browsers don't allow HTTP calls from websites to devices in the network (for security reasons). If it is the case, you will need to follow Odoo's documentation for 'Self-signed certificate for ePOS printers' and 'Secure connection (HTTPS)' to solve the issue. "
msgstr ""
"프린터 연결을 확인하세요.\n"
"일부 브라우저에서는 웹사이트에서 네트워크 장치로의 HTTP 호출이 허용되지 않습니다 (보안 상의 이유). 이러한 경우 문제를 해결하려면 'ePOS 프린터용 자체 인증서' 및 '보안 연결 (HTTP)' 관련 Odoo의 문서를 참고하여 실행하세요."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/res_company.py:0
#, python-format
msgid ""
"Please close all the point of sale sessions in this period before closing "
"it. Open sessions are: %s "
msgstr "이 기간 동안 모든 POS 세션을 닫은 후 종료하십시오. 열려 있는 세션은 다음과 같습니다 : %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment_method.py:0
#, python-format
msgid ""
"Please close and validate the following open PoS Sessions before modifying this payment method.\n"
"Open sessions: %s"
msgstr ""
"이 결제 방법을 변경하시려면 열려 있는 다음 POS 세션을 종료 후 승인하시기 바랍니다.\n"
"열려 있는 세션: %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Please configure a payment method in your POS."
msgstr "POS에서 결제 방법을 구성하십시오."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Please create/select a Point of Sale above to show the configuration "
"options."
msgstr "구성 옵션을 표시하려면 위의 POS를 생성/선택하시기 바랍니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "Please define income account for this product: \"%s\" (id:%d)."
msgstr "해당 상품의 손익 계정을 정의하세요 : \"%s\" (id:%d)."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please define income account for this product: '%s' (id:%d)."
msgstr "이 품목에 대한 수입 계정을 정의하십시오:'%s' (id:%d)."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid ""
"Please enter your billing information <small class=\"text-muted\">or</small>"
msgstr "결제 정보를 입력하시거나 <small class=\"text-muted\">또는</small>"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "Please fill all the required fields."
msgstr "필수 필드 전체를 작성해주시기 바랍니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Please go on the %s journal and define a Loss Account. This account will be "
"used to record cash difference."
msgstr "%s 전표로 이동하여 손실 계정을 설정하십시오. 이 계정은 현금 차액을 기록하는데 사용합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Please go on the %s journal and define a Profit Account. This account will "
"be used to record cash difference."
msgstr "%s 전표로 이동하여 수익 계정을 설정하십시오. 이 계정은 현금 차액을 기록하는데 사용합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Please print the invoice from the backend"
msgstr "백엔드에서 청구서를 인쇄하십시오"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Please provide a partner for the sale."
msgstr "판매 협력사를 입력하십시오."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Please select a payment method."
msgstr "결제 방법을 선택하십시오."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Please select the Customer"
msgstr "고객을 선택하십시오"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PoS Interface"
msgstr "PoS 인터페이스"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_attribute_custom_value__pos_order_line_id
msgid "PoS Order Line"
msgstr "POS 주문 명세"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_warehouse.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_pivot
#, python-format
msgid "PoS Orders"
msgstr "POS 주문"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_pos_category_action
#: model:ir.ui.menu,name:point_of_sale.menu_products_pos_category
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "PoS Product Categories"
msgstr "PoS 상품 범주"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "PoS Product Category"
msgstr "PoS 상품 범주"

#. module: point_of_sale
#: model:account.tax.group,name:point_of_sale.pos_taxe_group_0
msgid "PoS Taxes"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "PoS order %s can not be processed"
msgstr ""

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_partner_property_form
msgid "Point Of Sale"
msgstr "POS"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_kanban
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__name
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__config_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_config_id
#: model:ir.ui.menu,name:point_of_sale.menu_point_root
#: model_terms:ir.ui.view,arch_db:point_of_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_account_journal_pos_user_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Point of Sale"
msgstr "POS"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_pos_order_view_tree
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_graph
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_pivot
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Point of Sale Analysis"
msgstr "점포판매시스템 분석"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_category
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__pos_categ_ids
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__pos_categ_ids
msgid "Point of Sale Category"
msgstr "점포판매시스템 분류"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_search
msgid "Point of Sale Config"
msgstr "점포판매시스템 환경설정"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_config
#: model:ir.model.fields,field_description:point_of_sale.field_pos_make_payment__config_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_tree
msgid "Point of Sale Configuration"
msgstr "POS 환경 설정"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_daily_sales_reports_wizard
msgid "Point of Sale Daily Report"
msgstr "POS 일일 보고서"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_saledetails
msgid "Point of Sale Details"
msgstr "점포판매시스템 세부사항"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_details_wizard
msgid "Point of Sale Details Report"
msgstr "점포판매시스템 세부사항 보고서"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_point_of_sale_report_invoice
msgid "Point of Sale Invoice Report"
msgstr "POS 청구서 리포트"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__journal_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_journal_id
msgid "Point of Sale Journal"
msgstr "POS 전표"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_config_tree
msgid "Point of Sale List"
msgstr "POS 목록"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_make_payment
msgid "Point of Sale Make Payment Wizard"
msgstr "점포판매시스템 결제 마법사"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_manager_id
msgid "Point of Sale Manager Group"
msgstr "POS 관리자 그룹"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_stock_warehouse__pos_type_id
msgid "Point of Sale Operation Type"
msgstr "POS 운영 유형"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order_line
msgid "Point of Sale Order Lines"
msgstr "점포판매시스템 주문 명세"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_order
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Point of Sale Orders"
msgstr "점포판매시스템 주문"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_report_pos_order
msgid "Point of Sale Orders Report"
msgstr "점포판매시스템 주문 보고서"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment_method
#: model:ir.model.fields,field_description:point_of_sale.field_account_journal__pos_payment_method_ids
msgid "Point of Sale Payment Methods"
msgstr "POS 결제 수단"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_payment
msgid "Point of Sale Payments"
msgstr "점포판매시스템 결제"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_printer
msgid "Point of Sale Printer"
msgstr "POS 프린터"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_session
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_tree
msgid "Point of Sale Session"
msgstr "점포판매시스템 기간"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.qunit_suite
msgid "Point of Sale Tests"
msgstr "POS 테스트"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__group_pos_user_id
msgid "Point of Sale User Group"
msgstr "POS 사용자 그룹"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_bill__pos_config_ids
#: model:ir.ui.menu,name:point_of_sale.menu_point_of_sale_list
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Point of Sales"
msgstr "점포판매시스템"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_url
msgid "Portal Access URL"
msgstr "포털 접근 URL"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_allowed_pricelist_ids
msgid "Pos Allowed Pricelist"
msgstr "POS 허용 가격표"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__pos_config_ids
msgid "Pos Config"
msgstr "POS 구성"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_order_printer
msgid "Pos Is Order Printer"
msgstr "POS 주문 프린터"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_ids
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_order_id
msgid "Pos Order"
msgstr "Pos 주문"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,field_description:point_of_sale.field_res_users__pos_order_count
msgid "Pos Order Count"
msgstr "POS 주문 수량"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__pos_order_line_id
msgid "Pos Order Line"
msgstr "POS 주문 명세"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_payment_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_payment_ids
msgid "Pos Payment"
msgstr "POS 결제"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "Pos Product Categories"
msgstr "점포판매시스템 제품 분류"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_refunded_invoice_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_move__pos_refunded_invoice_ids
#: model:ir.model.fields,field_description:point_of_sale.field_account_payment__pos_refunded_invoice_ids
msgid "Pos Refunded Invoice"
msgstr "Pos 환불 청구서"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_selectable_categ_ids
msgid "Pos Selectable Categ"
msgstr "POS 선택 가능한 카테고리"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_daily_sales_reports_wizard__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_stock_picking__pos_session_id
msgid "Pos Session"
msgstr "POS 세션"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_duration
msgid "Pos Session Duration"
msgstr "POS 세션 지속시간"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_state
msgid "Pos Session State"
msgstr "POS 세션 상태"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__pos_session_username
msgid "Pos Session Username"
msgstr "POS 세션 사용자 이름"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__open_session_ids
msgid "Pos Sessions"
msgstr "POS 세션"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
msgid "Pos session"
msgstr "POS 세션"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__is_posbox
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_is_posbox
msgid "PosBox"
msgstr "Pos박스"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Positive quantity not allowed"
msgstr "양수 수량 허용되지 않음"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_order__state__done
#: model:ir.model.fields.selection,name:point_of_sale.selection__report_pos_order__state__done
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Posted"
msgstr "발행 완료"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Powered by"
msgstr "저작권자"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Powered by Odoo"
msgstr "Powered by Odoo"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_preparation_display
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Preparation Display"
msgstr "준비 표시"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_printer_form
#: model:ir.ui.menu,name:point_of_sale.menu_pos_preparation_printer
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_printer
msgid "Preparation Printers"
msgstr "준비 프린터"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Previous Order List"
msgstr "이전 주문 목록"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Price"
msgstr "가격"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Price Control"
msgstr "가격 관리"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__combo_price
msgid "Price Extra"
msgstr "추가 금액"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Price discount from %s -> %s"
msgstr "가격 할인 (%s ->%s) "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Price excl. Tax:"
msgstr "세금 제외 가격:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_extra
msgid "Price extra"
msgstr "추가 금액"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.xml:0
#, python-format
msgid "Price list"
msgstr "가격표"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__price
msgid "Priced Product"
msgstr "가격이 책정된 제품"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.js:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pricelist_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__pricelist_id
#, python-format
msgid "Pricelist"
msgstr "가격표"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_pricelist
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "가격표"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Pricing"
msgstr "가격 책정"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sale_details_button.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
#, python-format
msgid "Print"
msgstr "인쇄"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/reprint_receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/reprint_receipt_button/reprint_receipt_button.xml:0
#, python-format
msgid "Print Receipt"
msgstr "영수증 인쇄"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Print a QR code on the receipt to allow the user to easily request the "
"invoice for an order."
msgstr "사용자가 주문과 관련된 청구서를 쉽게 요청할 수 있도록 영수증에 QR 코드를 인쇄합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sale_details_button.xml:0
#, python-format
msgid "Print a report with all the sales of the current PoS Session"
msgstr "현재 POS 세션의 모든 매출이 포함된 보고서 인쇄"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print orders at the kitchen, at the bar, etc."
msgstr "부엌, 바 등에서 주문을 인쇄하십시오."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Print receipts automatically once the payment is registered"
msgstr "결제가 등록되면 영수증 자동 인쇄"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_via_proxy
msgid "Print via Proxy"
msgstr "프록시를 통해 인쇄"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__product_categories_ids
msgid "Printed Product Categories"
msgstr "인쇄된 품목 분류"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
#, python-format
msgid "Printer"
msgstr "프린터"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__name
msgid "Printer Name"
msgstr "프린터 이름"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__printer_type
msgid "Printer Type"
msgstr "프린터 유형"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Printers"
msgstr "프린트"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "Printing error"
msgstr "인쇄 오류"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid "Printing failed"
msgstr "인쇄 실패"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/printer/pos_printer_service.js:0
#, python-format
msgid "Printing is not supported on some browsers"
msgstr "일부 브라우저에서는 인쇄 기능이 지원되지 않습니다."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_procurement_group
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__procurement_group_id
msgid "Procurement Group"
msgstr "조달 그룹"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: model:ir.model,name:point_of_sale.model_product_template
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_pack_operation_lot__product_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
#, python-format
msgid "Product"
msgstr "품목"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "품목 속성 사용자 정의 값"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_categ_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "Product Category"
msgstr "품목 카테고리"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_combo_line
msgid "Product Combo Items"
msgstr "품목 콤보 항목"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_pos_combo
msgid "Product Combos"
msgstr "품목 콤보"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/product_card/product_card.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/product_card/product_card.xml:0
#, python-format
msgid "Product Information"
msgstr "제품 정보"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__base_price
msgid "Product Price"
msgstr "품목 가격"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Product Prices"
msgstr "품목 가격"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_tree_view
msgid "Product Product Categories"
msgstr "생산 품목 분류"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_qty
msgid "Product Quantity"
msgstr "품목 수량"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__product_tmpl_id
msgid "Product Template"
msgstr "품목 양식"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__detailed_type
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__detailed_type
msgid "Product Type"
msgstr "품목 유형"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_uom
msgid "Product Unit of Measure"
msgstr "품목 단위"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__product_uom_id
msgid "Product UoM"
msgstr "품목 단위"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_uom_category
msgid "Product UoM Categories"
msgstr "상품 단위 범주"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_product_product
msgid "Product Variant"
msgstr "품목 세부선택"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_product_action
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_action_product_product
msgid "Product Variants"
msgstr "품목 세부사항"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_combo
msgid "Product combo choices"
msgstr "품목 콤보 선택"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Product information"
msgstr "품목 정보"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.js:0
#, python-format
msgid ""
"Product is not loaded. Tried loading the product from the server but there "
"is a network error."
msgstr "품목을 불러올 수 없습니다. 서버에서 품목 불러오기를 시도하였으나 네트워크 에러가 발생했습니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Product prices on receipts"
msgstr "영수증의 상품 가격"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tipproduct
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_tipproduct
msgid "Product tips"
msgstr "품목 팁"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.product_template_action_pos_product
#: model:ir.ui.menu,name:point_of_sale.menu_pos_products
#: model:ir.ui.menu,name:point_of_sale.pos_config_menu_catalog
#: model:ir.ui.menu,name:point_of_sale.pos_menu_products_configuration
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Products"
msgstr "품목"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__combo_line_ids
msgid "Products in Combo"
msgstr "콤보 품목"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Products:"
msgstr "품목:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Promotions, Coupons, Gift Card & Loyalty Program"
msgstr "프로모션, 쿠폰, 기프트 카드 및 적립 프로그램"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Proxy Connected"
msgstr "프록시 연결됨"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Proxy Disconnected"
msgstr "프록시 연결이 끊어졌습니다"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "프록시 IP 주소"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.xml:0
#, python-format
msgid "Proxy Warning"
msgstr "프록시 경고"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Qty"
msgstr "수량"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__qty
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Quantity"
msgstr "수량"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_receipt/cash_move_receipt.xml:0
#, python-format
msgid "REASON"
msgstr "사유"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "REFUNDED:"
msgstr "환불 완료:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rating_ids
msgid "Ratings"
msgstr "평가"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Read Weighing Scale"
msgstr "저울 눈금 읽기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#: code:addons/point_of_sale/static/src/backend/tours/point_of_sale.js:0
#, python-format
msgid "Ready to launch your <b>point of sale</b>?"
msgstr "귀하의 <b>점포판매시스템</b>을 시작할까요?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.xml:0
#, python-format
msgid "Reason"
msgstr "사유"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Receipt"
msgstr "입고"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Receipt %s"
msgstr "%s 영수증"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_footer
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_receipt_footer
msgid "Receipt Footer"
msgstr "영수증 꼬리말"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__receipt_header
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_receipt_header
msgid "Receipt Header"
msgstr "영수증 머리글"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__pos_reference
#, python-format
msgid "Receipt Number"
msgstr "영수증 번호"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Receipt Printer"
msgstr "영수증 프린터"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Record payments with a terminal on this journal."
msgstr "이 전표에 터미널로 결제를 기록하십시오."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__rescue
msgid "Recovery Session"
msgstr "복구 세션"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Ref 876787"
msgstr "참조 번호 876787"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Refresh Display"
msgstr "화면 새로 고침"

#. module: point_of_sale
#. odoo-javascript
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/refund_button/refund_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/refund_button/refund_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/refund_button/refund_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Refund"
msgstr "환불"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refund_orderline_ids
msgid "Refund Order Lines"
msgstr "환불 주문 내역"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Refund Orders"
msgstr "환불 주문"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Refund and Sales not allowed"
msgstr "환불 및 판매 불가"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Refunded"
msgstr "환불 완료"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_order_ids
msgid "Refunded Order"
msgstr "환불된 주문"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_orderline_id
msgid "Refunded Order Line"
msgstr "환불된 주문 내역"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Refunded Orders"
msgstr "환불된 주문"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__refunded_orders_count
msgid "Refunded Orders Count"
msgstr "환불된 주문 수"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__refunded_qty
msgid "Refunded Quantity"
msgstr "환불된 수량"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Refunding"
msgstr "환불 중"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Refunds"
msgstr "환불"

#. module: point_of_sale
#: model:ir.actions.client,name:point_of_sale.action_client_pos_menu
msgid "Reload POS Menu"
msgstr "POS 메뉴 새로고침"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Remaining"
msgstr "잔여"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Remaining unsynced orders"
msgstr "동기화되지 않은 나머지 주문"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
#, python-format
msgid "Remove"
msgstr "제거"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Replenishment"
msgstr "재보충"

#. module: point_of_sale
#: model:ir.ui.menu,name:point_of_sale.menu_point_rep
msgid "Reporting"
msgstr "보고"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Reprint Invoice"
msgstr "청구서 재인쇄"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Request invoice"
msgstr "청구서 요청"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Request sent"
msgstr "요청 전송"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Reset"
msgstr "초기화"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__user_id
msgid "Responsible"
msgstr "담당자"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__activity_user_id
msgid "Responsible User"
msgstr "담당 사용자"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Restaurant Mode"
msgstr "레스토랑 모드"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__limit_categories
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_limit_categories
msgid "Restrict Categories"
msgstr "카테고리 제한"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__restrict_price_control
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_restrict_price_control
msgid "Restrict Price Modifications to Managers"
msgstr "관리자의 가격 수정 제한"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Restrict price modification to managers"
msgstr "관리자에 대한 가격 수정 제한"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.xml:0
#, python-format
msgid "Resume Order"
msgstr "주문 재개"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Retry"
msgstr "재시도"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#, python-format
msgid "Return Products"
msgstr "반품"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_return
msgid "Returned"
msgstr "반납함"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of POS closing entry %s for order %s from session %s"
msgstr "세션 %s의 %s 주문에 대한 POS 마감 항목 %s 취소"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Reversal of: %s"
msgstr "역분개 대상: %s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Reversal request sent to terminal"
msgstr "터미널로 환입 요청 전송"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Reverse"
msgstr "역분개"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Reverse Payment"
msgstr "역분개 결제"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Review"
msgstr "검토"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Rounding"
msgstr "반올림"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Rounding Method"
msgstr "반올림 방법"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Rounding error in payment lines"
msgstr "결제 내역의 반올림 오류"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/backend/debug_manager.js:0
#, python-format
msgid "Run Point of Sale JS Tests"
msgstr "POS JS 테스트 실행"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 에러"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "SN"
msgstr "일련번호"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "SOLD:"
msgstr "판매 완료:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__nbr_lines
msgid "Sale Line Count"
msgstr "판매 명세수"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_day
#: model:ir.actions.act_window,name:point_of_sale.action_pos_order_line_form
msgid "Sale line"
msgstr "판매 명세"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Sales"
msgstr "판매"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_details
#: model:ir.actions.report,name:point_of_sale.sale_details_report
#: model:ir.ui.menu,name:point_of_sale.menu_report_order_details
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_daily_sales_reports_wizard
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_details_wizard
msgid "Sales Details"
msgstr "판매 세부 사항"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sale_journal
msgid "Sales Journal"
msgstr "매출 전표"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Sample Closing Note"
msgstr "마감 메모 예시"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Sample Config Name"
msgstr "설정명 예시"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Sample Opening Note"
msgstr "시작 메모 예시"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#, python-format
msgid "Save"
msgstr "저장"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Save this page and come back here to set up the feature."
msgstr "이 페이지를 저장하고 돌아와서 기능을 설정합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
#, python-format
msgid "Scale"
msgstr "크기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Scan"
msgstr "스캔"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Scan EAN-13"
msgstr "EAN-13 스캔"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Scan me to request an invoice for your purchase."
msgstr "구매 관련 청구서를 요청하시려면 여기를 스캔하세요."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_scan_via_proxy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "프록시를 통해 스캔"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/proxy_status/proxy_status.js:0
#, python-format
msgid "Scanner"
msgstr "스캐너"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#, python-format
msgid "Search Customers..."
msgstr "고객 검색"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid "Search Orders..."
msgstr "주문 검색"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
msgid "Search Sales Order"
msgstr "판매 주문 검색"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_list.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "Search more"
msgstr "더 검색하기"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__access_token
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__access_token
msgid "Security Token"
msgstr "보안 토큰"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/input_popups/selection_popup.js:0
#, python-format
msgid "Select"
msgstr "선택"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.js:0
#, python-format
msgid "Select Fiscal Position"
msgstr "재정 위치 선택"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Select PoS to start sharing orders"
msgstr "주문 공유를 시작하려면 POS를 선택하세요."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Select a payment method to validate the order."
msgstr "주문을 승인하려면 결제 방법을 선택하세요."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/pricelist_button/pricelist_button.js:0
#, python-format
msgid "Select the pricelist"
msgstr "가격표를 선택하세요."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "Select the product(s) to refund and set the quantity"
msgstr "환불받으실 품목을 선택하신 후 수량을 설정하세요."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Select the shipping date"
msgstr "배송일을 선택하세요."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__attribute_value_ids
msgid "Selected Attributes"
msgstr "선택 속성"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Sell products and deliver them later."
msgstr "품목을 판매하고 추후 배송합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Send"
msgstr "보내기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Send Payment Request"
msgstr "결제 요청 보내기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#: code:addons/point_of_sale/static/src/app/errors/popups/error_traceback_popup.xml:0
#, python-format
msgid "Send by email"
msgstr "이메일로 전송"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Sending email failed. Please try again."
msgstr "이메일 전송에 실패했습니다. 다시 시도해주세요."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Sending in progress."
msgstr "전송 중입니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_category__sequence
#: model:ir.model.fields,field_description:point_of_sale.field_pos_combo__sequence
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__sequence
msgid "Sequence"
msgstr "순서"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__sequence_number
msgid "Sequence Number"
msgstr "일련번호"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/select_lot_popup/edit_list_input/edit_list_input.xml:0
#, python-format
msgid "Serial/Lot Number"
msgstr "일련번호/LOT 번호"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
#, python-format
msgid "Served by"
msgstr "제공"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Server Error"
msgstr "서버 오류"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#: model:ir.model.fields,field_description:point_of_sale.field_account_bank_statement_line__pos_session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__session_id
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__session_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_payment_search
#, python-format
msgid "Session"
msgstr "세션"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Session Control"
msgstr "세션 제어"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__name
msgid "Session ID"
msgstr "세션 ID"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Session ID:"
msgstr "세션 ID:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__session_move_id
msgid "Session Journal Entry"
msgstr "세션 전표입력"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_report_pos_daily_sales_reports
#: model:ir.ui.menu,name:point_of_sale.menu_report_daily_details
msgid "Session Report"
msgstr "세션 보고서"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "Session ids:"
msgstr "세션 ID :"

#. module: point_of_sale
#: model:mail.activity.type,name:point_of_sale.mail_activity_old_session
msgid "Session open over 7 days"
msgstr "세션이 열린 지 7일 경과"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session
#: model:ir.actions.act_window,name:point_of_sale.action_pos_session_filtered
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__session_ids
#: model:ir.ui.menu,name:point_of_sale.menu_pos_session_all
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "Sessions"
msgstr "세션"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__set_maximum_difference
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_set_maximum_difference
msgid "Set Maximum Difference"
msgstr "최대 차액 설정"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Set Weight"
msgstr "무게 설정"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session"
msgstr "세션을 종료할 때 예상 금액과 계산 금액 간의 최대 허용 차액 설정"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__set_maximum_difference
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_set_maximum_difference
msgid ""
"Set a maximum difference allowed between the expected and counted money "
"during the closing of the session."
msgstr "세션을 종료할 때 예상 금액과 계산 금액 간의 최대 허용 차액 설정"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.xml:0
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.xml:0
#, python-format
msgid "Set fiscal position"
msgstr "재정 위치 설정"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "품목당 복합단가 및 자동 할인 등을 설정할 수 있습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Set the new discount"
msgstr "새로운 할인 설정"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "Set the new quantity"
msgstr "새로운 수량 설정"

#. module: point_of_sale
#: model:ir.actions.act_window,name:point_of_sale.action_pos_configuration
#: model:ir.ui.menu,name:point_of_sale.menu_pos_global_settings
msgid "Settings"
msgstr "설정"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Settings on this page will apply to this point of sale."
msgstr "이 페이지 설정 내용은 이 POS에 적용됩니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Share Open Orders"
msgstr "진행 중인 주문 공유"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__ship_later
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_ship_later
#, python-format
msgid "Ship Later"
msgstr "추후 배송"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__shipping_date
msgid "Shipping Date"
msgstr "배송일"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__picking_policy
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_picking_policy
msgid "Shipping Policy"
msgstr "배송 정책"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Shop"
msgstr "쇼핑"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Shopping cart"
msgstr "쇼핑한 장바구니"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Show Category Images"
msgstr "카테고리 이미지 표시"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Show Product Images"
msgstr "제품 이미지 표시"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show checkout to customers through a second display"
msgstr "두번째 디스플레이에서 고객에게 결제 표시"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_via_proxy
msgid "Show checkout to customers with a remotely-connected screen."
msgstr "원격으로 연결된 화면으로 고객에게 체크 아웃을 표시합니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_customer_facing_display_local
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_customer_facing_display_local
msgid "Show checkout to customers."
msgstr "고객에게 결제 화면을 표시합니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__module_pos_hr
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_module_pos_hr
msgid "Show employee login screen"
msgstr "직원 로그인 화면 표시"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Show margins & costs on product information"
msgstr "품목 정보에 마진 및 비용 표시"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_preparation_display
msgid "Show orders on the preparation display screen."
msgstr "준비 표시 화면에 주문을 표시"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "Sign in"
msgstr "로그인"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Six"
msgstr "Six"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_six
msgid "Six Payment Terminal"
msgstr "Six 결제 단말기"

#. module: point_of_sale
#: model:product.attribute,name:point_of_sale.size_attribute
msgid "Size"
msgstr "사이즈"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_print_skip_screen
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_print_skip_screen
msgid "Skip Preview Screen"
msgstr "미리보기 화면 통과"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__skip_change
msgid "Skip line when sending ticket to kitchen printers."
msgstr "주방 프린터로 티켓을 보낼 때 내역을 건너 뜁니다."

#. module: point_of_sale
#: model:product.template,name:point_of_sale.small_shelf_product_template
msgid "Small Shelf"
msgstr "작은 선반"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Some Serial/Lot Numbers are missing"
msgstr "일부 일련번호/LOT 번호가 누락되었습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to configuration "
"errors. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"환경 설정 오류로 인해 일부 주문을 서버에 제출할 수 없습니다. POS (Point of Sale)를 종료 할 수 있지만 문제가 해결되기"
" 전에 세션을 닫지 마십시오."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"Some orders could not be submitted to the server due to internet connection "
"issues. You can exit the Point of Sale, but do not close the session before "
"the issue has been resolved."
msgstr ""
"인터넷 연결 문제로 인해 일부 주문을 서버에 제출할 수 없습니다. POS (Point of Sale)를 종료 할 수 있지만 문제가 "
"해결되기 전에 세션을 닫지 마십시오."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Some, if not all, post-processing after syncing order failed."
msgstr "주문 동기화 후 일부 사후 처리가 실패했습니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Specific route"
msgstr "특정 경로"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_pos_pack_operation_lot
msgid "Specify product lot/serial number in pos order line"
msgstr "POS 주문 내역에 제품 LOT/일련번호 지정"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__route_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_route_id
msgid "Spefic route for products delivered later."
msgstr "추후 배송 품목에 대한 특정 경로입니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__start_category
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_start_category
msgid "Start Category"
msgstr "시작 카테고리"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_details_wizard__start_date
msgid "Start Date"
msgstr "시작일"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.js:0
#, python-format
msgid "Start adding products"
msgstr "제품 추가하기"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "Start category should belong in the available categories."
msgstr "시작 카테고리는 사용할 수 있는 카테고리에 속해 있어야 합니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Start selling from a default product category"
msgstr "기본 상품 범주에서 판매 시작"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_start
msgid "Starting Balance"
msgstr "개시 잔액"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "State"
msgstr "시/도"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__state
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__state
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__state
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_session_search
#, python-format
msgid "Status"
msgstr "상태"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"활동 기준 상태\n"
"기한 초과: 기한이 이미 지났습니다.\n"
"오늘: 활동 날짜가 오늘입니다.\n"
"예정: 향후 계획된 활동입니다."

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_move
msgid "Stock Move"
msgstr "재고 이동"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_rule
msgid "Stock Rule"
msgstr "재고 규칙"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Stock input for %s"
msgstr "%s 관련 재고 입고"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Stock output for %s"
msgstr "%s 관련 재고 출고"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__update_stock_at_closing
msgid "Stock should be updated at closing"
msgstr "마감 시 재고 내용을 업데이트해야 합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Street"
msgstr "도로명 주소"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Stripe"
msgstr "Stripe"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_stripe
msgid "Stripe Payment Terminal"
msgstr "Stripe 결제 단말기"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal_incl
msgid "Subtotal"
msgstr "소계"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_subtotal
msgid "Subtotal w/o Tax"
msgstr "세금 없는 소계"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_sub_total
msgid "Subtotal w/o discount"
msgstr "할인없는 소계"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "Successfully imported"
msgstr "가져오기에 성공했습니다"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/cash_move_popup/cash_move_popup.js:0
#, python-format
msgid "Successfully made a cash %s of %s."
msgstr "%s 관련 %s 항목을 성공적으로 현금화하였습니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Sum of subtotals"
msgstr "소계 합계"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Summary"
msgstr "요약"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/navbar.xml:0
#, python-format
msgid "Switch Product View"
msgstr "품목 보기 전환"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Connected"
msgstr "동기화 연결"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Connecting"
msgstr "동기화 연결"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Disconnected"
msgstr "동기화 연결이 끊어졌습니다"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sync_notification/sync_notification.xml:0
#, python-format
msgid "Synchronisation Error"
msgstr "동기화 오류"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "TOTAL"
msgstr "합계"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/fiscal_position_button/fiscal_position_button.js:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: model:ir.model,name:point_of_sale.model_account_tax
#, python-format
msgid "Tax"
msgstr "세금"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Amount"
msgstr "세액"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__iface_tax_included
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_iface_tax_included
msgid "Tax Display"
msgstr "세금 표시"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.xml:0
#, python-format
msgid "Tax ID"
msgstr "사업자등록번호"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.js:0
#, python-format
msgid "Tax ID: %(vatId)s"
msgstr "세금 ID: %(vatId)s"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Tax Name"
msgstr "세금명"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tax_regime_selection
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_tax_regime_selection
msgid "Tax Regime Selection value"
msgstr "세금 제도 선택 값"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__subtotal
msgid "Tax-Excluded Price"
msgstr "세금 별도 금액"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__iface_tax_included__total
msgid "Tax-Included Price"
msgstr "세금 포함 가격"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_tax
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids
#: model:ir.ui.menu,name:point_of_sale.menu_action_tax_form_open
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Taxes"
msgstr "세무"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Taxes on refunds"
msgstr "환불액에 대한 세금"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Taxes on sales"
msgstr "매출에 대한 세금"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__tax_ids_after_fiscal_position
msgid "Taxes to Apply"
msgstr "적용할 세금"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#, python-format
msgid "Taxes:"
msgstr "세금 :"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Technical Stuff"
msgstr "기술 관련"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Technical Stuffs"
msgstr "기술 관련"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/receipt_header/receipt_header.xml:0
#, python-format
msgid "Tel:"
msgstr "전화 :"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Thank you for your purchase!"
msgstr "구매해 주셔서 감사합니다!"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "The %s must be filled in your details."
msgstr "귀하의 세부 정보에 %s 항목을 입력해야 합니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "프린터의 하드웨어 프록시 호스트 이름 또는 IP 주소"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"두 개의 문자로 된 ISO 국가 코드.\n"
"이 필드는 빠른 검색을 위해 사용할 수 있습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.js:0
#, python-format
msgid ""
"The Point of Sale could not find any product, customer, employee or action "
"associated with the scanned barcode."
msgstr "스캔한 바코드 항목과 관련된 품목이나 고객, 직원이나 작업을 POS에서 찾을 수 없습니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_rounding_form_view_inherited
msgid ""
"The Point of Sale only supports the \"add a rounding line\" rounding "
"strategy."
msgstr "POS에서는 \"반올림 내역 추가\" 만 반올림 방식으로 지원하고 있습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"The Point of Sale order with the following reference %s was received by the Odoo server, but the order processing phase failed.\n"
"The datas received from the point of sale has been saved in the attachments.\n"
"Please contact your support service to assist you on restoring it"
msgstr ""

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/controllers/main.py:0
#, python-format
msgid "The Ticket Number should be at least 14 characters long."
msgstr "티켓 번호는 14자 이상이어야 합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"The amount cannot be higher than the due amount if you don't have a cash "
"payment method configured."
msgstr "현금 결제 방식을 설정하지 않은 경우, 대금 금액보다 큰 금액은 입력할 수 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"The amount of your payment lines must be rounded to validate the transaction.\n"
"The rounding precision is %s so you should set %s or %s as payment amount instead of %s."
msgstr ""
"거래의 유효성을 확인하려면 결제 금액의 금액을 반올림해야 합니다. 반올림 정밀도가 %s이므로 %s 또는 %s를 %s 대신 결제 금액으로 "
"사용해야 합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The cash rounding strategy of the point of sale %(pos)s must be: '%(value)s'"
msgstr "%(pos)s POS은 '%(value)s' 반올림 방식을 사용해야 합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "The default pricelist must be included in the available pricelists."
msgstr "기본 가격 목록이 사용 가능한 가격 목록에 포함되어 있어야 합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The default pricelist must belong to no company or the company of the point "
"of sale."
msgstr "기본 가격표는 회사나 POS상의 회사에 대한 것이 아니어야 합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The default tip product is missing. Please manually specify the tip product."
" (See Tips field.)"
msgstr "기본 도움말 품목이 없습니다. 도움말 품목을 수동으로 지정하십시오 (도움말 필드를 확인하시기 바랍니다)."

#. module: point_of_sale
#: model:product.template,description_sale:point_of_sale.desk_organizer_product_template
msgid ""
"The desk organiser is perfect for storing all kinds of small things and "
"since the 5 boxes are loose, you can move and place them in the way that "
"suits you and your things best."
msgstr ""
"책상 정리대는 어떤 종류든 작은 물품들을 보관하기에 알맞으며 5개의 상자가 느슨하게 연결되어 있어서 사용자가 물품에 가장 적합한 방식으로"
" 옮기며 정리할 수 있습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"The fiscal position used in the original order is not loaded. Make sure it "
"is loaded by adding it in the pos configuration."
msgstr "원 주문에 사용된 재정 위치를 불러오지 못했습니다. POS 환경 설정에 추가한 후 불러오시기 바랍니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "The function to load %s has not been implemented."
msgstr "%s 항목을 불러오는 기능이 구현되지 않았습니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__proxy_ip
msgid ""
"The hostname or ip address of the hardware proxy, Will be autodetected if "
"left empty."
msgstr "왼쪽이 비어있는 경우 자동 감지되는 하드웨어 프록시의 호스트명 또는 ip 주소"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The invoice journal must be in the same currency as the Sales Journal or the"
" company currency if that is not set."
msgstr "청구서 전표는 매출 전표 또는 회사 통화와 같은 통화로 설정해야 합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.js:0
#, python-format
msgid ""
"The maximum difference allowed is %s.\n"
"Please contact your manager to accept the closing difference."
msgstr ""
"허용되는 최대 차액은 %s입니다 \n"
"마감 차액을 허용하려면 관리자에게 문의하세요."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_bill.py:0
#, python-format
msgid "The name of the Coins/Bills must be a number."
msgstr "현금/청구서의 이름은 숫자여야 합니다."

#. module: point_of_sale
#: model:ir.model.constraint,message:point_of_sale.constraint_pos_session_uniq_name
msgid "The name of this POS Session must be unique!"
msgstr "POS 세션은 고유한 이름을 사용해야 합니다!"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_partner__pos_order_count
#: model:ir.model.fields,help:point_of_sale.field_res_users__pos_order_count
msgid "The number of point of sales orders related to this customer"
msgstr "이 고객과 관련된 POS 주문 수"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "The order could not be sent to the server due to an unknown error"
msgstr "알 수없는 오류로 인해 주문을 서버로 보낼 수 없습니다"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "The order has been already paid."
msgstr "주문이 이미 결제되었습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid ""
"The order has been synchronized earlier. Please make the invoice from the "
"backend for the order: "
msgstr "주문이 이전에 동기화되었습니다. 주문에 대한 백엔드에서 송장을 작성하십시오 :"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_payment.py:0
#, python-format
msgid ""
"The payment method selected is not allowed in the config of the POS session."
msgstr "매장 세션 구성에서는 선택한 결제 방법이 허용되지 않습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The payment methods for the point of sale %s must belong to its company."
msgstr "POS %s의 결제 방법은 이 회사에 속해 있어야 합니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_start_categ_id
msgid ""
"The point of sale will display this product category by default. If no "
"category is specified, all available products will be shown."
msgstr "판매 시점에는 기본적으로 이 제품 범주가 표시됩니다. 범주를 지정하지 않으면 사용 가능한 모든 제품이 표시됩니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_available_categ_ids
msgid ""
"The point of sale will only display products which are within one of the "
"selected category trees. If no category is specified, all available products"
" will be shown"
msgstr ""
"POS 에는 선택한 범주 목록 중 하나에 속하는 제품만 표시됩니다. 범주를 지정하지 않으면 사용 가능한 모든 제품이 표시됩니다"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__pricelist_id
msgid ""
"The pricelist used if no customer is selected or if the customer has no Sale"
" Pricelist configured if any."
msgstr "선택된 고객이 없거나 고객이 판매 가격표를 설정하지 않은 경우 사용되는 가격표입니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_order__currency_rate
msgid ""
"The rate of the currency to the currency of rate applicable at the date of "
"the order"
msgstr "주문 날짜에 적용되는 환율에 대한 환율"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_skip_screen
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_print_skip_screen
msgid ""
"The receipt screen will be skipped if the receipt can be printed "
"automatically."
msgstr "영수증을 자동으로 인쇄할 수 있으면 영수증 화면을 건너 뜁니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__iface_print_auto
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_iface_print_auto
msgid "The receipt will automatically be printed at the end of each order."
msgstr "영수증은 주문이 끝날 때마다 자동으로 인쇄됩니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the ordered quantity. "
"%s is requested while only %s can be refunded."
msgstr "환불 요청한 수량이 주문 수량보다 큽니다. 요청 수량은 %s 인 반면, 환불 가능 수량은 %s입니다. "

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"The requested quantity to be refunded is higher than the refundable quantity"
" of %s."
msgstr "환불 요청 수량이 환불 가능 수량 %s보다 큽니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_combo_line__lst_price
msgid ""
"The sale price is managed from the product template. Click on the 'Configure"
" Variants' button to set the extra attribute prices."
msgstr "판매 가격은 품목 양식에서 관리합니다. 추가 속성별 가격을 설정하려면 '세부 품목 설정' 버튼을 클릭하세요."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "The selected customer needs an address."
msgstr "선택한 고객 항목에 주소 정보가 필요합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"The selected pricelists must belong to no company or the company of the "
"point of sale."
msgstr "선택한 가격표는 회사 또는 POS의 회사에 속하지 않아야 합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "The server encountered an error while receiving your order."
msgstr "주문을 받는 중 서버에 오류가 발생했습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"The session has been already closed by another User. All sales completed in "
"the meantime have been saved in a Rescue Session, which can be reviewed "
"anytime and posted to Accounting from Point of Sale's dashboard."
msgstr ""
"다른 사용자가 이미 세션을 종료하였습니다. 그동안 완료된 전체 매출 내역은 헬프 세션에 저장되어 언제든 확인하실 수 있으며 POS "
"현황판에서 회계로 발행할 수 있습니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid ""
"The session has been opened for an unusually long period. Please consider "
"closing."
msgstr "세션이 비정상적으로 오랫동안 열려 있습니다. 폐쇄를 고려하십시오."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_adyen
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Adyen. Set your Adyen credentials on the "
"related payment method."
msgstr "거래는 Adyen에서 처리합니다. 관련 결제 수단에서 Adyen 자격 증명을 설정하십시오."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_paytm
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by PayTM. Set your PayTM credentials on the "
"related payment method."
msgstr "거래는 PayTM에서 처리합니다. 관련 결제 수단에서 PayTM 자격 증명을 설정하세요."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_six
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Six. Set the IP address of the terminal on"
" the related payment method."
msgstr "Six에서 거래를 처리합니다. 해당 결제 방법에서 단말기의 IP 주소를 설정하십시오."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_stripe
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Stripe. Set your Stripe credentials on the"
" related payment method."
msgstr "Stripe에서 거래를 처리합니다. 해당 결제 방법에서 Stripe 자격 증명을 설정하십시오. "

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__module_pos_mercury
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"The transactions are processed by Vantiv. Set your Vantiv credentials on the"
" related payment method."
msgstr "거래는 Vantiv에서 처리합니다. 관련 결제 수단에서 Vantiv 자격 증명을 설정하십시오."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_combo__base_price
msgid ""
"The value from which pro-rating of the component price is based. This is to "
"ensure that whatever product the user chooses for a component, it will "
"always be they same price."
msgstr ""
"구성 요소 가격의 비례값에서 기준으로 하는 값입니다. 사용자가 어떤 품목을 구성 요소로 선택하든 항상 같은 가격이 적용되도록 하기 위한 "
"것입니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_balance_end
msgid "Theoretical Closing Balance"
msgstr "이론적인 결산 잔액"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "There are no products in this category."
msgstr "이 범주에 제품이 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There are still orders in draft state in the session. Pay or cancel the following orders to validate the session:\n"
"%s"
msgstr ""
"세션에서 여전히 초안 상태의 주문이 있습니다. 세션을 확인하려면 다음 주문을 결제하거나 취소하십시오. : \n"
"%s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "There are unsynced orders. Do you want to sync these orders?"
msgstr "동기화되지 않은 주문이 있습니다. 해당 주문을 동기화하시겠습니까?"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"There is a difference between the amounts to post and the amounts of the "
"orders, it is probably caused by taxes or accounting configurations changes."
msgstr "발행 금액과 주문 금액 사이에 차이가 있으며, 세금이나 회계 구성 변경으로 발생한 것일 수 있습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "There is already an electronic payment in progress."
msgstr "이미 전자 결제가 진행 중입니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"There is no Chart of Accounts configured on the company. Please go to the "
"invoicing settings to install a Chart of Accounts."
msgstr "회사에 구성된 계정과목 일람표가 없습니다. 계정과목 일람표를 설치하려면 송장 설정으로 이동하십시오."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"There is no cash payment method available in this point of sale to handle the change.\n"
"\n"
" Please pay the exact amount or add a cash payment method in the point of sale configuration"
msgstr ""
"변경 사항을 처리하기 위한 점포판매시스템에 사용 가능한 현금 결제 방법이 없습니다.\n"
"\n"
" 정확한 금액을 기불하거나 점포판매시스템 환경설정에서 현금 결제 방법을 추가하세요."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash payment method for this PoS Session"
msgstr "이 POS 세션에는 현금 결제 방법이 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "There is no cash register in this session."
msgstr "이 세션에는 금전등록기가 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"There must be at least one product in your order before it can be validated "
"and invoiced."
msgstr "승인 후 청구서를 발행하려면 하나 이상의 품목이 주문에 있어야 합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"This cash payment method is already used in another Point of Sale.\n"
"A new cash payment method should be created for this Point of Sale."
msgstr ""
"이 현금 결제 방법은 이미 다른 POS에서 사용하고 있습니다.\n"
"POS에 새로운 현금 결제 방법을 생성해야 합니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__amount_authorized_diff
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_amount_authorized_diff
msgid ""
"This field depicts the maximum difference allowed between the ending balance"
" and the theoretical cash when closing a session, for non-POS managers. If "
"this maximum is reached, the user will have an error message at the closing "
"of his session saying that he needs to contact his manager."
msgstr ""
"이 필드는 POS가 아닌 관리자의 경우 세션을 닫을 때 기말 잔액과 이론적 현금간에 허용되는 최대 차이를 나타냅니다. 이 최대 값에 "
"도달하면 세션 종료시 관리자에게 문의해야 한다는 오류 메시지가 표시됩니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_manager_id
msgid ""
"This field is there to pass the id of the pos manager group to the point of "
"sale client."
msgstr "이 필드는 POS 관리자 그룹의 ID를 POS 클라이언트로 전달하기 위해 있습니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__group_pos_user_id
msgid ""
"This field is there to pass the id of the pos user group to the point of "
"sale client."
msgstr "이 필드는 POS 사용자 그룹의 ID를 POS 클라이언트로 전달하기 위해 있습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "This invoice has been created from the point of sale session: %s"
msgstr "POS 세션에서 생성된 청구서입니다: %s"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__fiscal_position_ids
msgid ""
"This is useful for restaurants with onsite and take-away services that imply"
" specific tax rates."
msgstr "이는 특정 세율을 암시하는 현장 및 테이크 아웃 서비스가 있는 레스토랑에 유용합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid ""
"This journal is associated with a payment method. You cannot modify its type"
msgstr "이 전표는 결제 방법과 연계되어 있습니다. 유형을 수정할 수 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_journal.py:0
#, python-format
msgid ""
"This journal is associated with payment method %s that is being used by "
"order %s in the active pos session %s"
msgstr "이 전표는 활성화된 POS 세션 %s의 주문 %s에서 사용 중인 %s 결제 수단과 연결되어 있습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid ""
"This operation will destroy all unpaid orders in the browser. You will lose "
"all the unsaved data and exit the point of sale. This operation cannot be "
"undone."
msgstr ""
"이 작업은 브라우저의 모든 미결제 주문을 파기합니다. 저장하지 않은 모든 데이터가 손실되고 POS가 종료됩니다. 이 작업은 취소할 수 "
"없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.js:0
#, python-format
msgid ""
"This operation will permanently destroy all paid orders from the local "
"storage. You will lose all the data. This operation cannot be undone."
msgstr ""
"이 작업은 로컬 스토리지에서 모든 유료 주문을 영구적으로 파기합니다. 모든 데이터가 손실됩니다. 이 작업은 취소할 수 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/pos_store.js:0
#, python-format
msgid ""
"This order already has refund lines for %s. We can't change the customer "
"associated to it. Create a new order for the new customer."
msgstr ""
"이 주문에는 이미 %s 관련 환불 내역이 있습니다. 연결되어 있는 고객을 변경할 수 없습니다. 새 고객에 대한 새로운 주문을 생성합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "This order is empty"
msgstr "주문 내역이 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid ""
"This order is not yet synced to server. Make sure it is synced then try "
"again."
msgstr "이 주문은 아직 서버와 동기화되지 않았습니다. 동기화 여부를 확인 한 후 다시 시도하십시오."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This product is used as reference on customer receipts."
msgstr "이 상품은 고객 영수증에 대한 참조로 사용됩니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_line_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders lines."
msgstr "이 순서는 Odoo에 의해 자동으로 생성되지만 주문 내역의 참조 번호를 사용자 정의하도록 변경할 수 있습니다."

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__sequence_id
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_sequence_id
msgid ""
"This sequence is automatically created by Odoo but you can change it to "
"customize the reference numbers of your orders."
msgstr "이 순서는 Odoo에 의해 자동으로 생성되나, 주문의 참조 번호를 사용자 정의로 변경할 수 있습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "This session is already closed."
msgstr "이 세션은 이미 닫혀 있습니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "This tax is applied to any new product created in the catalog."
msgstr "이 세금은 카탈로그에 생성되는 새로운 품목 전체에 적용됩니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Those settings are common to all PoS."
msgstr "해당 설정은 POS 전체 공통 사항입니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__ticket_code
msgid "Ticket Code"
msgstr "티켓 코드"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Ticket Nr"
msgstr "티켓 번호"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Tip"
msgstr "팁"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__tip_amount
msgid "Tip Amount"
msgstr "팁 금액"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__tip_product_id
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_tip_product_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Tip Product"
msgstr "팁 상품"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.product_product_tip_product_template
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Tips"
msgstr "팁"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "Tips:"
msgstr "팁:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "To Close"
msgstr "마감 대기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "To Pay"
msgstr "지불하기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "To Refund:"
msgstr "환불:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__to_weight
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__to_weight
msgid "To Weigh With Scale"
msgstr "저울로 무게 측정"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid ""
"To delete a product, make sure all point of sale sessions are closed.\n"
"\n"
"Deleting a product available in a session would be like attempting to snatch a hamburger from a customer’s hand mid-bite; chaos will ensue as ketchup and mayo go flying everywhere!"
msgstr ""
"품목을 삭제하려면 전체 POS 세션 종료 여부를 확인하세요.\n"
"\n"
"세션에서 사용되고 있는 품목을 삭제하는 것은 고객이 막 선택한 상품이 갑자기 삭제되는 결과를 낳을 수 있으며, 큰 혼란을 줄 수 있습니다!"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__to_invoice
msgid "To invoice"
msgstr "발행할 청구서"

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_payment_form
#: model_terms:ir.actions.act_window,help:point_of_sale.action_pos_pos_form
msgid "To record new orders, start a new session."
msgstr "새 주문을 기록하려면 새 세션을 시작하십시오."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "To return product(s), you need to open a session in the POS %s"
msgstr "제품을 반품하려면 %s POS에서 세션을 열어야 합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#: code:addons/point_of_sale/static/src/app/utils/money_details_popup/money_details_popup.xml:0
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order__amount_total
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total"
msgstr "총계"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Total (Tax excluded)"
msgstr "합계 (세금 별도)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_register_total_entry_encoding
msgid "Total Cash Transaction"
msgstr "총 현금 거래"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Total Cost:"
msgstr "총 비용:"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__total_discount
msgid "Total Discount"
msgstr "총 할인"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_status/payment_status.xml:0
#, python-format
msgid "Total Due"
msgstr "결제 예정액 합계"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Total Margin:"
msgstr "총 마진:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "Total Paid (with rounding)"
msgstr "총 지불 (반올림 적용)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__total_payments_amount
msgid "Total Payments Amount"
msgstr "총 결제 금액"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__price_total
msgid "Total Price"
msgstr "총금액"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "Total Price excl. Tax:"
msgstr "세금 제외 가격:"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_payment__amount
msgid "Total amount of the payment."
msgstr "총 결제 금액."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__total_cost
msgid "Total cost"
msgstr "총 비용"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_line
msgid "Total qty"
msgstr "총 수량"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/order_widget/order_widget.xml:0
#: code:addons/point_of_sale/static/src/app/navbar/sale_details_button/sales_detail_report.xml:0
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
#, python-format
msgid "Total:"
msgstr "합계 :"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__cash_real_transaction
msgid "Transaction"
msgstr "거래"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Transaction cancelled"
msgstr "거래 취소됨"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_picking
msgid "Transfer"
msgstr "전송"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Trusted POS"
msgstr "신뢰할 수 있는 POS"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__trusted_config_ids
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_trusted_config_ids
msgid "Trusted Point of Sale Configurations"
msgstr "신뢰할 수 있는 POS 구성"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_barcode_rule__type
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__type
#: model:ir.model.fields,field_description:point_of_sale.field_product_product__type
#: model:ir.model.fields,field_description:point_of_sale.field_product_template__type
msgid "Type"
msgstr "유형"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment__card_type
msgid "Type of card used"
msgstr "사용된 카드의 종류"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "레코드에 있는 예외 활동의 유형입니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
#, python-format
msgid "UNSELECT"
msgstr "선택 취소"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Unable to close and validate the session.\n"
"Please set corresponding tax account in each repartition line of the following taxes: \n"
"%s"
msgstr ""
"세션을 닫고 유효성을 검사할 수 없습니다.\n"
"다음 세금의 각 분할 명세에 해당 세금 계정을 설정하십시오 :\n"
"%s"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/invoice_button/invoice_button.js:0
#, python-format
msgid "Unable to download invoice."
msgstr "청구서를 다운로드할 수 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"Unable to modify this PoS Configuration because you can't modify %s while a "
"session is open."
msgstr "세션이 열려 있는 동안에는 %s 수정이 불가능하므로 이 POS 구성을 수정할 수 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/barcode_reader_service.js:0
#, python-format
msgid "Unable to parse barcode"
msgstr "바코드를 읽을 수 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
#, python-format
msgid "Unable to show information about this error."
msgstr "이 오류에 대한 정보를 표시할 수 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Unable to sync order"
msgstr "주문을 동기화할 수 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "Unique Code:"
msgstr "고유 코드:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "Unique code"
msgstr "고유 코드"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_saledetails
msgid "Unit"
msgstr "단위"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__price_unit
msgid "Unit Price"
msgstr "단가"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/barcode/error_popup/barcode_error_popup.xml:0
#, python-format
msgid "Unknown Barcode:"
msgstr "알 수 없는 바코드:"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/error_handlers.js:0
#: code:addons/point_of_sale/static/src/app/utils/hooks.js:0
#, python-format
msgid "Unknown Error"
msgstr "알 수 없는 오류"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_line/partner_line.xml:0
#, python-format
msgid "Unselect"
msgstr "선택 취소"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Unsupported File Format"
msgstr "지원되지 않는 파일 형식"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "Unsupported search operation"
msgstr "지원되지 않는 검색 작업"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt_screen.js:0
#, python-format
msgid "Unsynced order"
msgstr "동기화되지 않은 주문"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
msgid "UoM"
msgstr "단위"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_update_stock_quantities
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__update_stock_quantities
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Update quantities in stock"
msgstr "재고 수량 업데이트"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_company__point_of_sale_use_ticket_qr_code
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__point_of_sale_use_ticket_qr_code
msgid "Use QR code on ticket"
msgstr "티켓에 QR 코드 사용하기"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_payment_method__use_payment_terminal
msgid "Use a Payment Terminal"
msgstr "결제 터미널 사용"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__use_pricelist
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_use_pricelist
msgid "Use a pricelist."
msgstr "가격표를 사용하십시오."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Use barcodes to scan products, customer cards, etc."
msgstr "바코드를 사용하여 제품이나 고객 카드 등을 스캔하십시오."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Use fiscal positions to get different taxes by order"
msgstr "재정 위치를 사용하여 주문별로 세금을 다르게 받으십시오."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Used to record product pickings. Products are consumed from its default "
"source location."
msgstr "품목 픽업을 기록하는데 사용합니다. 품목은 기본값인 원래 장소에서 사용됩니다."

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_report_pos_order__user_id
#: model:res.groups,name:point_of_sale.group_pos_user
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_order_filter
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_pos_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_report_pos_order_search
msgid "User"
msgstr "사용자"

#. module: point_of_sale
#: model:ir.actions.report,name:point_of_sale.report_user_label
msgid "User Labels"
msgstr "사용자 꼬리표"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__uuid
#: model:ir.model.fields,field_description:point_of_sale.field_pos_order_line__uuid
msgid "Uuid"
msgstr "Uuid"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.xml:0
#, python-format
msgid "Validate"
msgstr "승인"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Vantiv (US & Canada)"
msgstr "Vantiv (미국 및 캐나다)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__module_pos_mercury
msgid "Vantiv Payment Terminal"
msgstr "Vantiv 결제 단말기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_lines/payment_lines.xml:0
#, python-format
msgid "Waiting for card"
msgstr "카드를 기다리는 중"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.wall_shelf_product_template
msgid "Wall Shelf Unit"
msgstr "벽 선반 부품"

#. module: point_of_sale
#: model:ir.model,name:point_of_sale.model_stock_warehouse
#: model:ir.model.fields,field_description:point_of_sale.field_pos_config__warehouse_id
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "Warehouse"
msgstr "창고"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_res_config_settings__pos_warehouse_id
msgid "Warehouse (PoS)"
msgstr "창고 (POS)"

#. module: point_of_sale
#: model:ir.model.fields,field_description:point_of_sale.field_pos_session__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_session__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/debug_widget.xml:0
#, python-format
msgid "Weighing"
msgstr "계량"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "중량이 있는 품목"

#. module: point_of_sale
#: model:ir.model.fields.selection,name:point_of_sale.selection__pos_config__picking_policy__one
msgid "When all products are ready"
msgstr "모든 품목이 준비될 때"

#. module: point_of_sale
#: model:ir.model.fields,help:point_of_sale.field_pos_config__is_margins_costs_accessible_to_every_user
#: model:ir.model.fields,help:point_of_sale.field_res_config_settings__pos_is_margins_costs_accessible_to_every_user
msgid ""
"When disabled, only PoS manager can view the margin and cost of product "
"among the Product info."
msgstr "비활성화할 경우, 품목 정보 중 품목의 마진 및 비용에 대한 내용은 POS 관리자만 조회할 수 있습니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid ""
"Whenever you close a session, one entry is generated in the following "
"accounting journal for all the orders not invoiced. Invoices are recorded in"
" accounting separately."
msgstr ""
"세션을 닫을 때마다 청구되지 않은 모든 주문에 대해 다음 회계 전표에 하나의 항목이 생성됩니다. 청구서는 회계에 별도로 기록됩니다."

#. module: point_of_sale
#: model:product.template,name:point_of_sale.whiteboard_product_template
msgid "Whiteboard"
msgstr "화이트보드"

#. module: point_of_sale
#: model:product.template,name:point_of_sale.whiteboard_pen_product_template
msgid "Whiteboard Pen"
msgstr "화이트보드 마커"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#, python-format
msgid "With a"
msgstr "와"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "Yes"
msgstr "예"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You are not allowed to change the cash rounding configuration while a pos "
"session using it is already opened."
msgstr "현금 반올림 환경 설정을 사용중인 pos 세션이 이미 열려있으면 현금 반올림 환경 설정을 변경할 수 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.js:0
#, python-format
msgid "You are not allowed to change this quantity"
msgstr "수량을 변경할 권한이 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid ""
"You are trying to sell products with serial/lot numbers, but some of them are not set.\n"
"Would you like to proceed anyway?"
msgstr "판매하려는 품목 중 일련번호/LOT 번호가 설정되지 않은 품목이 있습니다. 계속 진행하시겠습니까?"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "You can go to"
msgstr "다음으로 이동할 수 있습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You can only unlink PoS order lines that are related to orders in new or "
"cancelled state."
msgstr "신규 또는 취소 상태의 주문과 관련된 PoS 주문 항목의 연결만 해제할 수 있습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid ""
"You can't: create a pos order from the backend interface, or unset the "
"pricelist, or create a pos.order in a python test with Form tool, or edit "
"the form view in studio if no PoS order exist"
msgstr ""
"다음 작업을 진행할 수 없습니다: 백엔드 인터페이스에서 POS 주문 생성, 가격표 설정 해제, 양식 도구를 사용하여 Python "
"테스트에서 pos.order 생성, POS 주문이 없는 경우 스튜디오에서 양식 보기 편집"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/stock_picking.py:0
#, python-format
msgid "You cannot archive '%s' as it is used by a POS configuration '%s'."
msgstr "POS 구성 '%s'에서 사용하고 있으므로 .'%s' 항목은 보관 처리할 수 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You cannot close the POS when invoices are not posted.\n"
"Invoices: %s"
msgstr ""
"청구서를 발행하지 않으면 POS를 종료할 수 없습니다.\n"
"청구서: %s"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot close the POS when orders are still in draft"
msgstr "주문서가 미결 상태인 경우 POS를 종료할 수 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You cannot create a session before the accounting lock date."
msgstr "계정 잠금 날짜 이전에는 세션을 작성할 수 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_category.py:0
#, python-format
msgid ""
"You cannot delete a point of sale category while a session is still opened."
msgstr "세션이 열려 있는 동안에는 POS 카테고리를 삭제할 수 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/account_cash_rounding.py:0
#, python-format
msgid ""
"You cannot delete a rounding method that is used in a Point of Sale "
"configuration."
msgstr "POS 환경설정에서 사용 중인 반올림 방식은 삭제할 수 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "You cannot invoice orders belonging to different companies."
msgstr "다른 회사의 주문에 대해서는 청구서를 발행할 수 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/control_buttons/save_button/save_button.js:0
#, python-format
msgid "You cannot save an empty order"
msgstr "내용이 없는 주문서는 저장할 수 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"You cannot share open orders with configuration that does not use the same "
"currency."
msgstr "진행 중인 주문은 동일한 통화를 사용하지 않는 구성과 공유될 수 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You cannot use the same journal on multiples cash payment methods."
msgstr "동일한 전표를 여러 개의 현금 결제 방법에 사용할 수 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You do not have permission to open a POS session. Please try opening a "
"session with a different user"
msgstr "POS 세션을 열 수 있는 권한이 없습니다. 다른 사용자 계정을 사용해 보십시오. "

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You don't have the access rights to get the point of sale closing control "
"data."
msgstr "POS 마감 관리 데이터를 가져올 수 있는 접근 권한이 없습니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"You have enabled the \"Identify Customer\" option for %s payment method,but "
"the order %s does not contain a customer."
msgstr "%s 결제 방법에 대해 \"고객 확인\" 옵션을 활성화하였으나, %s 주문 내용에 고객 정보가 없습니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.js:0
#, python-format
msgid ""
"You have selected orderlines from multiple invoiced orders. To proceed "
"refund, please select orderlines from the same invoiced order."
msgstr ""

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "You have to round your payments lines. is not rounded."
msgstr "결제 내역을 반올림해야 합니다. 반올림되지 않았습니다."

#. module: point_of_sale
#: model_terms:ir.actions.act_window,help:point_of_sale.product_product_action
msgid ""
"You must define a product for everything you sell through\n"
"                the point of sale interface."
msgstr "POS 인터페이스를 통해 판매하는 모든 제품에 대한 제품을 정의해야 합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/product.py:0
#, python-format
msgid "You must first remove this product from the %s combo"
msgstr "먼저 %s 콤보에서 품목을 삭제해야 합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid ""
"You must have at least one payment method configured to launch a session."
msgstr "세션을 시작하려면 결제 방법이 하나 이상은 구성되어 있어야 합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_config.py:0
#, python-format
msgid "You need a loss and profit account on your cash journal."
msgstr "현금 전표에 손익 계정이 있어야 합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"You need to select the customer before you can invoice or ship an order."
msgstr "주문에서 청구서를 발행하거나 배송하기 전에 고객을 선택해야 합니다."

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid "You should assign a Point of Sale to your session."
msgstr "세션에 점포판매시스템을 할당해야 합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/errors/popups/offline_error_popup.js:0
#, python-format
msgid "You're offline"
msgstr "오프라인 상태입니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Your Order"
msgstr "주문 내역"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_session.py:0
#, python-format
msgid ""
"Your PoS Session is open since %(date)s, we advise you to close it and to "
"create a new one."
msgstr "POS 세션이 %(date)s 이후 열려 있는 상태입니다. 종료 후 새로운 세션을 생성하시기 바랍니다."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid ""
"Your address is missing or incomplete. <br/>\n"
"                                Please make sure to"
msgstr ""
"주소가 누락되어 있거나 일부 정보가 누락되었습니다. <br/>\n"
"                                다음을 확인하시기 바랍니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/partner_list/partner_editor/partner_editor.js:0
#, python-format
msgid "Zip"
msgstr "우편번호"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "available,"
msgstr "사용하실 수 있으며,"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "before continuing."
msgstr "계속하기 전에"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "belong to another session:"
msgstr "다른 세션에 속합니다 :"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_tree
msgid "combos"
msgstr "콤보"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "create your own"
msgstr "직접 다음 항목을 생성하세요."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "demo data"
msgstr "데모용 데이터"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/point_of_sale/static/src/app/generic_components/orderline/orderline.xml:0
#, python-format
msgid "discount"
msgstr "할인"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_combo_form
msgid "e.g. Burger Menu"
msgstr "예: 햄버거 메뉴"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_payment_method_view_form
msgid "e.g. Cash"
msgstr "예: 현금"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "e.g. Company Address, Website"
msgstr "예: 회사 주소, 웹사이트"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
msgid "e.g. NYC Shop"
msgstr "예: 뉴욕 상점"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "e.g. Return Policy, Thanks for shopping with us!"
msgstr "예를 들어 반품 정책, 우리와 함께 쇼핑해 주셔서 감사합니다!"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.product_pos_category_form_view
msgid "e.g. Soft Drinks"
msgstr "예: 청량 음료"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.pos_config_view_form
#: model_terms:ir.ui.view,arch_db:point_of_sale.res_config_settings_view_form
msgid "ePos Printer"
msgstr "ePOS 프린터"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "fill all relevant information"
msgstr "관련된 모든 정보를 입력하십시오."

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_validation_screen
msgid "for"
msgstr "for"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "for an order of"
msgstr "연관된 주문"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_info_popup/product_info_popup.xml:0
#, python-format
msgid "forecasted"
msgstr "예측"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/ticket_screen/ticket_screen.xml:0
#, python-format
msgid "in"
msgstr "분류"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "in this category."
msgstr "이 카테고리에 해당합니다."

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_screen.xml:0
#, python-format
msgid "items"
msgstr "항목"

#. module: point_of_sale
#: model:mail.activity.type,summary:point_of_sale.mail_activity_old_session
msgid "note"
msgstr "노트"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/screens/product_screen/product_list/product_list.xml:0
#, python-format
msgid "or"
msgstr "또는"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/navbar/closing_popup/closing_popup.xml:0
#, python-format
msgid "orders:"
msgstr "주문:"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.view_pos_config_kanban
msgid "outstanding rescue session"
msgstr "미해결 헬프 세션"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "paid orders"
msgstr "결제된 주문"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.ticket_request_with_code
msgid "qx9h1"
msgstr "qx9h1"

#. module: point_of_sale
#. odoo-python
#: code:addons/point_of_sale/models/pos_order.py:0
#, python-format
msgid "return"
msgstr "돌아가기"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "the invoice"
msgstr "청구서"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/store/models.js:0
#, python-format
msgid "the receipt"
msgstr "영수증"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "unpaid orders"
msgstr "미결제 주문"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "unpaid orders could not be imported"
msgstr "미결제 주문을 가져올 수 없습니다"

#. module: point_of_sale
#: model_terms:ir.ui.view,arch_db:point_of_sale.report_invoice_document
msgid "using"
msgstr "사용"

#. module: point_of_sale
#. odoo-javascript
#: code:addons/point_of_sale/static/src/app/debug/order_import_popup/order_import_popup.xml:0
#, python-format
msgid "were duplicates of existing orders"
msgstr "기존 주문과 중복"
