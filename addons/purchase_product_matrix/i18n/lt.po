# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_product_matrix
# 
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <linask<PERSON>iu<PERSON><EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Atributų reikšmės"

#. module: purchase_product_matrix
#. odoo-javascript
#: code:addons/purchase_product_matrix/static/src/js/purchase_product_field.js:0
#, python-format
msgid "Edit Configuration"
msgstr "Redaguoti konfigūraciją"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid
msgid "Grid"
msgstr "Tinklelis"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid_product_tmpl_id
msgid "Grid Product Tmpl"
msgstr ""

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid_update
msgid "Grid Update"
msgstr ""

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__report_grids
msgid ""
"If set, the matrix of configurable products will be shown on the report of "
"this order."
msgstr ""

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr ""

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__report_grids
msgid "Print Variant Grids"
msgstr ""

#. module: purchase_product_matrix
#: model_terms:ir.ui.view,arch_db:purchase_product_matrix.purchase_order_form_matrix
msgid "Product"
msgstr "Produktas"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_template_id
msgid "Product Template"
msgstr "Produkto šablonas"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_no_variant_attribute_value_ids
msgid "Product attribute values that do not create variants"
msgstr "Produkto atributo reikšmės, kurios nesukuria variantų"

#. module: purchase_product_matrix
#: model:ir.model,name:purchase_product_matrix.model_purchase_order
msgid "Purchase Order"
msgstr "Pirkimo užsakymas"

#. module: purchase_product_matrix
#: model:ir.model,name:purchase_product_matrix.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Pirkimo užsakymo eilutė "

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid_product_tmpl_id
msgid "Technical field for product_matrix functionalities."
msgstr ""

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid
msgid ""
"Technical storage of grid. \n"
"If grid_update, will be loaded on the PO. \n"
"If not, represents the matrix to open."
msgstr ""

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid_update
msgid "Whether the grid field contains a new matrix to apply or not."
msgstr ""

#. module: purchase_product_matrix
#. odoo-python
#: code:addons/purchase_product_matrix/models/purchase.py:0
#, python-format
msgid ""
"You cannot change the quantity of a product present in multiple purchase "
"lines."
msgstr ""
