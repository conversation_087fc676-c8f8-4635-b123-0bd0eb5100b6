# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# Wil <PERSON>do<PERSON>, 2025
# <AUTHOR> <EMAIL>, 2025
# <PERSON><PERSON> CHEN <<EMAIL>>, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-13 00:52+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#, python-format
msgid " Add Images"
msgstr "添加图像"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "\" alert with a"
msgstr "’警告带有"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "\"%s form submission\" <%s>"
msgstr "\"%s 提交表格\" <%s>"

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL from\" can not be empty."
msgstr "\"来源网址\" 不能为空。"

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" can not be empty."
msgstr "“链接”不能为空"

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid ""
"\"URL to\" cannot be set to \"/\". To change the homepage content, use the "
"\"Homepage URL\" field in the website settings or the page properties on any"
" custom page."
msgstr "不能将 “前往网址” 设置为 “/”。若要更改主页内容，请使用网站设置中的“主页网址”字段，或任何自定义页面上的页面属性。"

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" cannot be set to an existing page."
msgstr "“前往网址” 不可设为现有页面。"

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" cannot contain parameter %s which is not used in \"URL from\"."
msgstr "“链接到”不能包含“链接从”中未使用的参数 %s。"

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" is invalid: %s"
msgstr "\"链接到\"无效: %s"

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must contain parameter %s used in \"URL from\"."
msgstr "“链接到”不能包含“链接从”中未使用的参数 %s。"

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "\"URL to\" must start with a leading slash."
msgstr "“链接”必须 /开头"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__page_count
msgid "# Visited Pages"
msgstr "# 已访问网页"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__visit_count
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "# Visits"
msgstr "# 访问次数"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.navbar
msgid "#{_navbar_name if _navbar_name else 'Main'}"
msgstr "#{_navbar_name if _navbar_name else 'Main'}"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "#{display_label} #{depth}"
msgstr "#{display_label} #{depth}"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "#{heading_label} #{depth}"
msgstr "#{heading_label} #{depth}"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$10.50"
msgstr "$10.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$12.00"
msgstr "$12.00"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$15.50"
msgstr "$15.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$7.50"
msgstr "$7.50"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "$9.00"
msgstr "$9.00"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_list.js:0
#, python-format
msgid "%s record(s) selected, are you sure you want to publish them all?"
msgstr "选择%s记录，您确定要全部发布吗？"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "&amp;lt;head&amp;gt; and &amp;lt;/body&amp;gt;"
msgstr "&amp;lt;头部&amp;gt;和&amp;lt;/身体&amp;gt;；"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "' did not match any pages."
msgstr "'不匹配任何网页。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "' did not match anything."
msgstr "' 没有匹配任何东西。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid ""
"' did not match anything.\n"
"                        Results are displayed for '"
msgstr ""
"' 没有匹配任何东西.\n"
"                        结果显示为 '"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/web_editor.xml:0
#, python-format
msgid "' to link to an anchor."
msgstr "' 链接到锚点"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/web_editor.xml:0
#, python-format
msgid ""
"' to search a page.\n"
"                    '"
msgstr ""
"' 到搜索网页\n"
"                    '"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "'. Showing results for '"
msgstr "'. 显示结果 '"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "(could be used in"
msgstr "（可用于"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "+ Field"
msgstr "+ 字段"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "+ New Website"
msgstr "+ 新网站"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "******-555-5556"
msgstr "******-555-5556"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "******-555-5556\""
msgstr "******-555-5556\""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ", author:"
msgstr ", 作者:"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
".\n"
"                                                The website will still work if you reject or discard those cookies."
msgstr ""
".\n"
"                                                拒绝放弃这些Cookies网站依然能正常访问。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid ""
".\n"
"                    Changing its name will break these calls."
msgstr ""
".\n"
"                改变其名称将破坏这些调用。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "...and switch the timeline contents to fit your needs."
msgstr "...并切换时间线内容以满足您的需求。"

#. module: website
#: model:website,contact_us_button_url:website.default_website
#: model:website,contact_us_button_url:website.website2
#: model_terms:ir.ui.view,arch_db:website.layout
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "/contactus"
msgstr "/contactus"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1 km"
msgstr "1 千米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/2 - 1/2"
msgstr "1/2 - 1/2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/3 - 2/3"
msgstr "1/3 - 2/3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "1/4 - 3/4"
msgstr "1/4 - 3/4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "10 m"
msgstr "10 米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 km"
msgstr "100 千米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "100 m"
msgstr "100 米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "100%"
msgstr "100%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "1000 km"
msgstr "1000 千米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "12"
msgstr "12"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "15 km"
msgstr "15 千米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown
msgid "16"
msgstr "16"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "2 <span class=\"visually-hidden\">(current)</span>"
msgstr "2<span class=\"visually-hidden\">(当前)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2 km"
msgstr "2 千米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2.5 m"
msgstr "2.5 米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "20 m"
msgstr "20 米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 km"
msgstr "200 千米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "200 m"
msgstr "200 米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "2000 km"
msgstr "2000 千米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "24x7 toll-free support"
msgstr "24x7 全天候免费支持"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "25%"
msgstr "25%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"
msgstr ""
"250 Executive Park Blvd, Suite 3400 <br/> San Francisco CA 94134 <br/>United"
" States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
msgid ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"250 Executive Park Blvd, Suite 3400 • San Francisco CA 94134 • United States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown
msgid "30"
msgstr "30"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "30 km"
msgstr "30 千米"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/fields.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__301
#, python-format
msgid "301 Moved permanently"
msgstr "301 永久移动"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/fields.xml:0
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__302
#, python-format
msgid "302 Moved temporarily"
msgstr "302 临时移动"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__308
msgid "308 Redirect / Rewrite"
msgstr "308 重定向 / 重写"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "4 km"
msgstr "4 千米"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "4 steps"
msgstr "4 个步骤"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 km"
msgstr "400 千米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "400 m"
msgstr "400 米"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_rewrite__redirect_type__404
msgid "404 Not Found"
msgstr "404 没有找到"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown
msgid "45"
msgstr "45"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "5 m"
msgstr "5 米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 km"
msgstr "50 千米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "50 m"
msgstr "50 米"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "50%"
msgstr "50%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "50,000+ companies run Odoo to grow their businesses."
msgstr "超过50,000家公司使用Odoo来发展业务。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "75%"
msgstr "75%"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "8 km"
msgstr "8 千米"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_progress_bar/options.js:0
#, python-format
msgid "80% Development"
msgstr "80% 开发"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
msgid "<b>50,000+ companies</b> run Odoo to grow their businesses."
msgstr "<b>50000+ 公司</b>使用Odoo发展其业务。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Add</b> the selected image."
msgstr "<b>添加</b> 选择的图像."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click Edit</b> to start designing your homepage."
msgstr "<b>点击编辑</b> 开始设计您的主页。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a snippet</b> to access its options menu."
msgstr "<b>单击单片段</b> 可以访问他的选项菜单."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click on a text</b> to start editing it."
msgstr "<b>单击文本</b>可以开始编辑它."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this column to access its options."
msgstr "<b>单机</b>此列可以访问它的选项菜单."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this header to configure it."
msgstr "<b>单击</b> 此标题可以进行配置"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Click</b> on this option to change the %s of the block."
msgstr "<b>单击</b> 此选项可以改变构建块的 %s "

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"color of this block."
msgstr "通过此菜单<b>自定义</b> 任何构建块。尝试修改此部件背景色。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"<b>Customize</b> any block through this menu. Try to change the background "
"image of this block."
msgstr "通过此菜单可以<b>自定义</b> 任何构建块. 尝试更改此部件的图像背景颜色."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "<b>Designed</b> <br/>for Companies"
msgstr "<b>设计</b> <br/>为公司"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<b>Designed</b> for companies"
msgstr "<b>设计</b> 为公司"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an icon</b> to change it with one of your choice."
msgstr "<b>双击图标</b> 可以选择其他的图标"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Double click on an image</b> to change it with one of your choice."
msgstr "<b>双击图像</b>可以选择其他的图像."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"<b>My Company</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"
msgstr ""
"<b>My Company</b><br/>250 Executive Park Blvd, Suite 3400 <br/> San "
"Francisco CA 94134 <br/>United States"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a %s."
msgstr "<b>选择</b> 一个 %s."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Select</b> a Color Palette."
msgstr "<b>选择</b> 调色板"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the %s padding"
msgstr "<b>滑动</b>此按钮可以更改  %s 的内边距"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "<b>Slide</b> this button to change the column size."
msgstr "<b>滑动</b> 此按钮可以改变这列的尺寸."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid ""
"<br/><br/>\n"
"                    Example of rule:<br/>"
msgstr ""
"<br/><br/>\n"
"                    规则示例：<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font class=\"display-3-fs\" style=\"background-color: rgb(255, 255, "
"255);\">Edit this title</font>"
msgstr ""
"<font class=\"display-3-fs\" style=\"background-color: rgb(255, 255, "
"255);\">编辑此标题</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"<font style=\"background-color: rgb(255, 255, 255);\">Good writing is "
"simple, but not simplistic.</font>"
msgstr ""
"<font style=\"background-color: rgb(255, 255, 255);\"> 好的写作很简洁，但不简单。</font>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 days ago</small>"
msgstr "<i class=\"fa fa-1x fa-clock-o mr8\"/><small>2 天前</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid ""
"<i class=\"fa fa-1x fa-fw fa-envelope "
"me-2\"/><span><EMAIL></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-envelope "
"me-2\"/><span><EMAIL></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid ""
"<i class=\"fa fa-1x fa-fw fa-map-marker me-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • United States"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-map-marker me-2\"/>250 Executive Park Blvd, "
"Suite 3400 • San Francisco CA 94134 • United States"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid ""
"<i class=\"fa fa-1x fa-fw fa-phone me-1\"/>\n"
"                        <span class=\"o_force_ltr\"><small>******-555-5556</small></span>"
msgstr ""
"<i class=\"fa fa-1x fa-fw fa-phone me-1\"/>\n"
"                        <span class=\"o_force_ltr\"><small>******-555-5556</small></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid "<i class=\"fa fa-1x fa-fw fa-phone me-1\"/>͏"
msgstr "<i class=\"fa fa-1x fa-fw fa-phone me-1\"/>͏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-circle\"/> Circles"
msgstr "<i class=\"fa fa-fw fa-circle\"/> 圈子"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-heart\"/> Hearts"
msgstr "<i class=\"fa fa-fw fa-heart\"/> 心形"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-refresh me-1\"/> Replace Icon"
msgstr "<i class=\"fa fa-fw fa-refresh me-1\"/>更换图标"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-square\"/> Squares"
msgstr "<i class=\"fa fa-fw fa-square\"/> 正方形"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-star\"/> Stars"
msgstr "<i class=\"fa fa-fw fa-star\"/> 星形"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<i class=\"fa fa-fw fa-thumbs-up\"/> Thumbs"
msgstr "<i class=\"fa fa-fw fa-thumbs-up\"/> 大拇指"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "<i class=\"fa fa-fw o_button_icon fa-circle text-danger\" title=\"Offline\"/>"
msgstr "<i class=\"fa fa-fw o_button_icon fa-circle text-danger\" title=\"Offline\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\" "
"title=\"Connected\"/>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-circle text-success\" "
"title=\"Connected\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"<i class=\"fa fa-info-circle\"/> Edit the content below this line to adapt "
"the default <strong>Page not found</strong> page."
msgstr ""
"<i class=\"fa fa-info-circle\"/> 编辑这行下面的内容可以适配默认的  <strong>网页未找到</strong> "
"网页."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">A password is required to access this page.</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">访问此网页需要密码。</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">Wrong password</span>"
msgstr ""
"<i class=\"fa fa-lock fa-2x\"/><br/>\n"
"                            <span class=\"mt-1\">密码错误</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/><span class=\"o_force_ltr\">3575 "
"Fake Buena Vista Avenue</span>"
msgstr ""
"<i class=\"fa fa-map-marker fa-fw me-2\"/><span class=\"o_force_ltr\">3575 "
"Fake Buena Vista Avenue</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid ""
"<i class=\"fa fa-phone fa-fw me-2\"/><span class=\"o_force_ltr\">+1 "
"************</span>"
msgstr ""
"<i class=\"fa fa-phone fa-fw me-2\"/><span class=\"o_force_ltr\">+1 "
"************</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    How to create my Plausible Shared Link"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    如何建立我的 Plausible 共享链接"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    How to get my Measurement ID"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    如何获取我的测量识别码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-calendar fa-fw me-2\"/>\n"
"                            <b>Events</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-calendar fa-fw me-2\"/>\n"
"                             <b>活动</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-eye fa-fw me-2\"/>\n"
"                            <b>About us</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-eye fa-fw me-2\"/>\n"
"                            <b>关于我们</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-group fa-fw me-2\"/>\n"
"                            <b>Partners</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-group fa-fw me-2\"/>\n"
"                           <b>合作伙伴</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-handshake-o fa-fw me-2\"/>\n"
"                            <b>Services</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-handshake-o fa-fw me-2\"/>\n"
"                            <b>服务</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-headphones fa-fw me-2\"/>\n"
"                            <b>Help center</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-headphones fa-fw me-2\"/>\n"
"                            <b>帮助中心</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-map-o fa-fw me-2\"/>\n"
"                            <b>Guides</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-map-o fa-fw me-2\"/>\n"
"                            <b>向导</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-newspaper-o fa-fw me-2\"/>\n"
"                            <b>Our blog</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-newspaper-o fa-fw me-2\"/>\n"
"                            <b>我们的博客</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-star-o fa-fw me-2\"/>\n"
"                            <b>Customers</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-star-o fa-fw me-2\"/>\n"
"                            <b>客户</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-tags fa-fw me-2\"/>\n"
"                            <b>Products</b>"
msgstr ""
"<i class=\"s_mega_menu_little_icons_icon fa fa-tags fa-fw me-2\"/>\n"
"                            <b>产品</b>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-comments me-2\"/> Contact us"
msgstr "<i class=\"s_mega_menu_thumbnails_icon fa fa-comments me-2\"/> 联系我们"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "<i class=\"s_mega_menu_thumbnails_icon fa fa-cube me-2\"/> Free returns"
msgstr "<i class=\"s_mega_menu_thumbnails_icon fa fa-cube me-2\"/> 免费退货"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-shopping-basket me-2\"/> Pickup"
" in store"
msgstr "<i class=\"s_mega_menu_thumbnails_icon fa fa-shopping-basket me-2\"/>店内取货"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<i class=\"s_mega_menu_thumbnails_icon fa fa-truck me-2\"/> Express delivery"
msgstr "<i class=\"s_mega_menu_thumbnails_icon fa fa-truck me-2\"/>特快专递服务"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<i>Instant setup, satisfied or reimbursed.</i>"
msgstr "<i>立即安装，满意可退款。</i>"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "<p>Attached files: </p>"
msgstr "<p>附件档案：</p>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"<select id=\"exampleSelect\" class=\"form-select\">\n"
"                                                            <option selected=\"true\">Open this select menu</option>\n"
"                                                            <option value=\"1\">One</option>\n"
"                                                            <option value=\"2\">Two</option>\n"
"                                                            <option value=\"3\">Three</option>\n"
"                                                        </select>"
msgstr ""
"<select id=\"exampleSelect\" class=\"form-select\">\n"
"                                                            <option selected=\"true\">打开此选择菜单</option>\n"
"                                                            <option value=\"1\">一</option>\n"
"                                                            <option value=\"2\">二</option>\n"
"                                                            <option value=\"3\">三</option>\n"
"                                                        </select>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid ""
"<small class=\"d-flex align-items-center\">\n"
"                        <i class=\"fa fa-1x fa-fw fa-usd fa-stack me-1\"/>\n"
"                        Low Price Guarantee\n"
"                    </small>\n"
"                    <small class=\"d-flex align-items-center\">\n"
"                        <i class=\"fa fa-1x fa-fw fa-shopping-basket fa-stack me-1\"/>\n"
"                        30 Days Online Returns\n"
"                    </small>\n"
"                    <small class=\"d-flex align-items-center\">\n"
"                        <i class=\"fa fa-1x fa-fw fa-truck fa-stack me-1\"/>\n"
"                        Standard Shipping\n"
"                    </small>"
msgstr ""
"<small class=\"d-flex align-items-center\">\n"
"                       <i class=\"fa fa-1x fa-fw fa-usd fa-stack me-1\"/>\n"
"                        低价保证\n"
"                    </small>\n"
"                    <small class=\"d-flex align-items-center\">\n"
"                       <i class=\"fa fa-1x fa-fw fa-shopping-basket fa-stack me-1\"/>\n"
"                        30 天在线退货\n"
"                    </small>\n"
"                    <small class=\"d-flex align-items-center\">\n"
"                       <i class=\"fa fa-1x fa-fw fa-truck fa-stack me-1\"/>\n"
"                        标准送货\n"
"                    </small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"<small class=\"text-muted\">\n"
"                                    <i class=\"fa fa-info\"/>: type some of the first chars after 'google' is enough, we'll guess the rest.\n"
"                                </small>"
msgstr ""
"<small class=\"text-muted\">\n"
"                                    <i class=\"fa fa-info\"/>：输入\"google\"之后首几个字符就足够，剩下的交给我们。\n"
"                                </small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(rounded-0)</small>"
msgstr "<small class=\"text-muted\">(四舍五入-0)</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(rounded-1)</small>"
msgstr "<small class=\"text-muted\">（四舍五入-1）</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(rounded-2)</small>"
msgstr "<small class=\"text-muted\">（四舍五入-2）</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(rounded-3)</small>"
msgstr "<small class=\"text-muted\">(四舍五入-3)</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(shadow)</small>"
msgstr "<small class=\"text-muted\">(阴影)</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(shadow-lg)</small>"
msgstr "<small class=\"text-muted\">(大阴影)</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">(shadow-sm)</small>"
msgstr "<small class=\"text-muted\">(小阴影)</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<small class=\"text-muted\">Last updated 3 mins ago</small>"
msgstr "<small class=\"text-muted\">最后更新 3 分钟前</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">Form field help "
"text</small>"
msgstr "<small id=\"emailHelp\" class=\"form-text text-muted\">表单字段帮助文本</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"<small id=\"emailHelp\" class=\"form-text text-muted\">We'll never share "
"your email with anyone else.</small>"
msgstr ""
"<small id=\"emailHelp\" class=\"form-text text-"
"muted\">我们永远不会与其他人分享您的电子邮件地址.</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid ""
"<small>\n"
"                            <i class=\"fa fa-1x fa-fw fa-envelope me-1\"/>\n"
"                            <EMAIL>\n"
"                        </small>"
msgstr ""
"<small>\n"
"                            <i class=\"fa fa-1x fa-fw fa-envelope me-1\"/>\n"
"                            <EMAIL>\n"
"                        </small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "<small>/ month</small>"
msgstr "<small>/ 月</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid ""
"<small><i class=\"fa fa-1x fa-fw fa-envelope me-1\"/> "
"<EMAIL></small>"
msgstr ""
"<small><i class=\"fa fa-1x fa-fw fa-envelope me-1\"/> "
"<EMAIL></small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid "<small>Free Returns and Standard Shipping</small>"
msgstr "<small>免费退货和标准送货</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "<small>TABS</small>"
msgstr "<small>选项卡</small>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "<span class=\"badge bg-primary rounded-pill\">14</span>"
msgstr "<span class=\"badge bg-primary rounded-pill\">14</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"carousel-control-next-icon\"/>\n"
"                <span class=\"visually-hidden\">下一页</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"carousel-control-prev-icon\"/>\n"
"                <span class=\"visually-hidden\">上一页</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid ""
"<span class=\"d-block p-2 small\">\n"
"                            <b>Discover our new products</b>\n"
"                        </span>"
msgstr ""
"<span class=\"d-block p-2 small\">\n"
"                            <b>了解我们的新产品</b>\n"
"                        </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>Your message has been sent <b>successfully</b></span>"
msgstr ""
"<span class=\"fa fa-check-circle\"/>\n"
"                                            <span>您的消息已经发送 <b>成功</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.language_selector_inline
msgid "<span class=\"list-inline-item\">|</span>"
msgstr "<span class=\"list-inline-item\">|</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "<span class=\"mx-2\">/</span>"
msgstr "<span class=\"mx-2\">/</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "<span class=\"mx-2\">to</span>"
msgstr "<span class=\"mx-2\">到</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_copyright_company_name
msgid ""
"<span class=\"o_footer_copyright_name me-2\">Copyright &amp;copy; Company "
"name</span>"
msgstr "<span class=\"o_footer_copyright_name me-2\">版权 &amp;copy; 公司名称</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.header_text_element
msgid "<span class=\"o_force_ltr\">******-555-5556</span>"
msgstr "<span class=\"o_force_ltr\">******-555-5556</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "<span class=\"o_stat_text\">Connected</span>"
msgstr "<span class=\"o_stat_text\">已连线</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "<span class=\"o_stat_text\">Offline</span>"
msgstr "<span class=\"o_stat_text\">离线</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"oi oi-chevron-left fa-2x text-white\"/>\n"
"                    <span class=\"visually-hidden\">Previous</span>"
msgstr ""
"<span class=\"oi oi-chevron-left fa-2x text-white\"/>\n"
"                    <span class=\"visually-hidden\">上一个</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
msgid ""
"<span class=\"oi oi-chevron-right fa-2x text-white\"/>\n"
"                    <span class=\"visually-hidden\">Next</span>"
msgstr ""
"<span class=\"oi oi-chevron-right fa-2x text-white\"/>\n"
"                    <span class=\"visually-hidden\">下一个</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
msgid ""
"<span class=\"pe-1\">We use cookies to provide you a better user experience "
"on this website.</span>"
msgstr "<span class=\"pe-1\">我们使用cookies为您提供在这网站上更好的用户体验。</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_badge
msgid ""
"<span class=\"s_badge badge text-bg-secondary o_animable\" data-name=\"Badge\">\n"
"        <i class=\"fa fa-1x fa-fw fa-folder o_not-animable\"/>Category\n"
"    </span>"
msgstr ""
"<span class=\"s_badge badge text-bg-secondary o_animable\" data-name=\"Badge\">\n"
"        <i class=\"fa fa-1x fa-fw fa-folder o_not-animable\"/>类别\n"
"    </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author text-muted\"><b>Iris DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author text-muted\"><b>Iris DOE</b>- MyCompany "
"首席执行官</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author text-muted\"><b>Jane DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author text-muted\"><b>Jane DOE</b>- MyCompany "
"首席执行官</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"<span class=\"s_blockquote_author text-muted\"><b>John DOE</b> • CEO of "
"MyCompany</span>"
msgstr ""
"<span class=\"s_blockquote_author text-muted\"><b>John DOE</b>- MyCompany "
"首席执行官</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>125</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                               <span class=\"s_comparisons_price\"><b>35</b></span>\n"
"                               <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"
msgstr ""
"<span class=\"s_comparisons_currency\">$</span>\n"
"                                <span class=\"s_comparisons_price\"><b>65</b></span>\n"
"                                <span class=\"s_comparisons_decimal\">.00</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">12</span>"
msgstr "<span class=\"s_number display-4\">12</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">37</span>"
msgstr "<span class=\"s_number display-4\">37</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">45</span>"
msgstr "<span class=\"s_number display-4\">45</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "<span class=\"s_number display-4\">8</span>"
msgstr "<span class=\"s_number display-4\">8</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "<span class=\"s_progress_bar_text\">80% Development</span>"
msgstr "<span class=\"s_progress_bar_text\">80% 开发</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid "<span class=\"s_website_form_label_content\">Company</span>"
msgstr "<span class=\"s_website_form_label_content\">公司</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid "<span class=\"s_website_form_label_content\">Email To</span>"
msgstr "<span class=\"s_website_form_label_content\">发电子邮件给</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Email</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">电子邮件</span>\n"
"                                                           <span class=\"s_website_form_mark\">*</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Name</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">名称</span>\n"
"                                                           <span class=\"s_website_form_mark\">*</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr "<span class=\"s_website_form_label_content\">电话号码</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Question</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">问题描述</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">主题</span>\n"
"                                                            <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid ""
"<span class=\"s_website_form_label_content\">Subject</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">主题</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid "<span class=\"s_website_form_label_content\">Your Company</span>"
msgstr "<span class=\"s_website_form_label_content\">贵公司</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid ""
"<span class=\"s_website_form_label_content\">Your Email</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">您的电子邮件</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid ""
"<span class=\"s_website_form_label_content\">Your Name</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">您的姓名</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid ""
"<span class=\"s_website_form_label_content\">Your Question</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">您的问题</span>\n"
"                                <span class=\"s_website_form_mark\"> *</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid ""
"<span invisible=\"name_slugified\" class=\"bg-300\">...</span>\n"
"                            <span invisible=\"page_type == 'listing'\">\n"
"                                /my-little-record-23\n"
"                            </span>"
msgstr ""
"<span invisible=\"name_slugified\" class=\"bg-300\">...</span>\n"
"                            <span invisible=\"page_type == 'listing'\">\n"
"                                /my-little-record-23\n"
"                            </span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid "<span>/model/</span>"
msgstr "<span>/model/</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span><b>2015</b></span>"
msgstr "<span><b>2015</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span><b>2018</b></span>"
msgstr "<span><b>2018</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "<span><b>2019</b></span>"
msgstr "<span><b>2019</b></span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "<span>Theme</span>"
msgstr "<span>主题</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"A CDN helps you serve your website’s content with high availability and high"
" performance to any visitor wherever they are located."
msgstr "CDN 帮助您向各地的访客提供高可用性和高性能的网站内容。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart
msgid "A Chart Title"
msgstr "图表标题"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"A Google Map error occurred. Make sure to read the key configuration popup "
"carefully."
msgstr "发生Google地图错误。 确保仔细阅读弹出的密钥配置。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "A Section Subtitle"
msgstr "部分副标题"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "A bit more patience as your website takes shape."
msgstr "请多一点耐心，静待网站生成。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid ""
"A card is a flexible and extensible content container. It includes options "
"for headers and footers, a wide variety of content, contextual background "
"colors, and powerful display options."
msgstr "一个卡片是灵活且可扩展的内容容器。它包括页眉和页脚选项，各种内容，上下文背景颜色和强大的显示选项。"

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_published
#: model:ir.model.fields,help:website.field_ir_cron__website_published
msgid ""
"A code server action can be executed from the website, using a dedicated "
"controller. The address is <base>/website/action/<website_path>. Set this "
"field as True to allow users to run this action. If it is set to False the "
"action cannot be run through the website."
msgstr ""
"使用专用控制器可从网站上运行代码服务器动作。地址为<base> /website/action/<website_path>。将此字段设置为 "
"True，可允许用户运行此动作。如果将此字段设置为 False，则无法通过网站运行该动作。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "A color block"
msgstr "颜色模块"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "A great title"
msgstr "一个伟大的标题"

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__field_names
msgid "A list of comma-separated field names"
msgstr "以逗号分隔的字段名称列表"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_stores_locator
msgid "A map and a listing of your stores"
msgstr "地图和商店列表"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visit_count
msgid ""
"A new visit is considered if last connection was more than 8 hours ago."
msgstr "如果最后一次连接时间超过8小时，则会考虑重新访问。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "A punchy Headline"
msgstr "醒目的标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "A second item"
msgstr "第二个项目"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "A short description of this great feature."
msgstr "此功能的简短描述。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "A small explanation of this great <br/>feature, in clear words."
msgstr "用清晰的文字来描述这个强大的<br/>功能。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "A third item"
msgstr "第三个项目"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"A timeline is a graphical representation on which important events are "
"marked."
msgstr "时间线是标记重要事件的图形表示形式。"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__is_connected
msgid ""
"A visitor is considered as connected if his last page view was within the "
"last 5 minutes."
msgstr "如果访问者的最后一次网页视图时间在最后5分钟内，则会将其视为已连接。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/web_editor.xml:0
#, python-format
msgid "AI"
msgstr "AI"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "API Key"
msgstr "API密钥"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
msgid "About"
msgstr "关于"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_personal_s_image_text
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_timeline_s_text_block_h2
msgid "About Me"
msgstr "关于我"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_about_us
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_cover
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_text_block_h1
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_4_s_text_block_h2
msgid "About Us"
msgstr "关于我们"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.aboutus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "About us"
msgstr "关于我们"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#, python-format
msgid "Access Denied"
msgstr "访问被拒绝"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__access_token
msgid "Access Token"
msgstr "访问令牌"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.protected_403
msgid "Access to this page"
msgstr "访问此网页"

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_visitor_access_token_unique
msgid "Access token should be unique."
msgstr "访问令牌应是唯一的。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Accessories"
msgstr "配件"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Accordion"
msgstr "手风琴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Account &amp; Sales management"
msgstr "会计和销售管理"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Accounts are usable across all your multiple websites"
msgstr "帐户可以在您所有的多个网站上使用"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_three_columns
msgid ""
"Achieve holistic health with personalized nutritional advice that "
"complements your workouts, promoting overall well-being."
msgstr "通过个性化的营养建议实现全面健康，与您的锻炼相辅相成，促进整体健康。"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__redirect_type
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Action"
msgstr "操作"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Activating the last features."
msgstr "正在启用最新功能。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Activating your %s."
msgstr "正在启用您的%s。"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__active
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__active
#: model:ir.model.fields,field_description:website.field_website_controller_page__active
#: model:ir.model.fields,field_description:website.field_website_page__active
#: model:ir.model.fields,field_description:website.field_website_rewrite__active
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Active"
msgstr "已启用"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Adapt these three columns to fit your design need. To duplicate, delete or "
"move columns, select the column and use the top icons to perform your "
"action."
msgstr "调整这三个列以适应您的设计需求。要复制、删除或移动列，请选择列并使用顶部图标执动作作。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#, python-format
msgid "Add"
msgstr "添加"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.grid_layout_options
msgid "Add Elements"
msgstr "添加元素"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Add Files"
msgstr "加入档案"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Item"
msgstr "添加明细"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Add Media"
msgstr "添加媒体"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Add Mega Menu Item"
msgstr "添加大幅网页菜单项"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Add Menu Item"
msgstr "添加菜单项目"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_social_media_options
msgid "Add New Social Network"
msgstr "添加新社交网络"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
msgid "Add Product"
msgstr "添加产品"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Row"
msgstr "添加行"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Add Serie"
msgstr "添加系列"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add Slide"
msgstr "添加幻灯片"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Add Tab"
msgstr "添加选项卡"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/web_editor.xml:0
#, python-format
msgid "Add Text Highlight Effects"
msgstr "添加文本高亮效果"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Add Year"
msgstr "添加年"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Add a Google Font"
msgstr "添加Google字体"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Add a Language"
msgstr "添加语言"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid "Add a caption to enhance the meaning of this image."
msgstr "添加标题以增强此图像的含义。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_product_catalog/options.js:0
#, python-format
msgid "Add a description here"
msgstr "在此处添加描述"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Add a great slogan."
msgstr "添加一句口号。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Add a menu description."
msgstr "添加菜单描述。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.js:0
#, python-format
msgid "Add a menu item"
msgstr "添加一个菜单项目"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field after this one"
msgstr "在此之后添加一个新字段"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Add a new field at the end"
msgstr "在末尾添加一个新字段"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Add groups in the \"Access Rights\" tab below."
msgstr "在下面的“访问权限”选项卡中新增组别。"

#. module: website
#. odoo-python
#: code:addons/website/models/res_lang.py:0
#, python-format
msgid "Add languages"
msgstr "添加语言"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Add option"
msgstr "增加选项"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.xml:0
#, python-format
msgid "Add page template"
msgstr "添加页面模板"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Add to Cart Button"
msgstr "按钮：加入购物车"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Add to cart"
msgstr "加入购物车"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#, python-format
msgid "Add to menu"
msgstr "添加到菜单"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Adding a language requires to leave the editor. This will save all your "
"changes, are you sure you want to proceed?"
msgstr "添加语言需要离开编辑器。这将保存您的所有更改，您确定要继续吗？"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Address"
msgstr "地址"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid ""
"Adjust volume, skip tracks, answer calls, and activate voice assistants with"
" a simple tap, keeping your hands free and your focus on what matters most."
msgstr "只需轻点一下，就能调节音量、跳过曲目、接听电话和激活语音助手，让您解放双手，专注于最重要的事情。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Advanced"
msgstr "高级"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Advertising &amp; Marketing<br/>(optional)"
msgstr "广告和营销<br/>(可选)"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__after
msgid "After"
msgstr "之后"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Alert"
msgstr "警报"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Alerts"
msgstr "警告"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Bottom"
msgstr "底部对齐"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Middle"
msgstr "中间对齐"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Align Top"
msgstr "顶部对齐"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Alignment"
msgstr "对齐"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_0_s_three_columns
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_image_text_2nd
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_media_list
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Aline Turner, CTO"
msgstr "Aline Turner，首席技术官"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_0_s_three_columns
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_image_text_2nd
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_media_list
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Aline is one of the iconic people in life who can say they love what they "
"do. She mentors 100+ in-house developers and looks after the community of "
"thousands of developers."
msgstr "艾琳是生活中的标志性人物之一，他们可以说自己热爱自己的工作。她指导了 100 多名内部开发人员，并负责管理由数千名开发人员组成的社区。"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__
msgid "All"
msgstr "全部"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
#, python-format
msgid "All SCSS Files"
msgstr "所有 SCSS 文件"

#. module: website
#: model:ir.model,name:website.model_website_route
msgid "All Website Route"
msgstr "所有网站路线"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_views_mixin.js:0
#, python-format
msgid "All Websites"
msgstr "所有网站"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns_menu
msgid "All You Can Eat"
msgstr "吃到饱"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "All informations you need"
msgstr "您需要的所有信息"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "All pages"
msgstr "所有网页"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "All results"
msgstr "所有结果"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "All these icons are completely free for commercial use."
msgstr "所有这些图标可以免费用于商业用途。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_3_s_three_columns
msgid "All-Day Comfort"
msgstr "全天候舒适体验"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "Allow all cookies"
msgstr "允许所有cookies"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "Allow the use of cookies from this website on this browser?"
msgstr "是否允许在此浏览器上使用本网站的cookies？"

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__track
#: model:ir.model.fields,help:website.field_website_controller_page__track
#: model:ir.model.fields,help:website.field_website_page__track
msgid "Allow to specify for one page of the website to be trackable or not"
msgstr "允许指定在网站中的一个网页可否被追踪"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_access
msgid "Allowed to use in forms"
msgstr "允许使用表单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Allows to do mass mailing campaigns to contacts"
msgstr "允许向联系人进行群发邮件活动"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Alows your visitors to chat with you"
msgstr "允许您的访客与您聊天"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Already installed"
msgstr "已安装"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Image Text"
msgstr "替代图像文本"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text"
msgstr "替代文本"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image"
msgstr "替代文本图像"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Alternate Text Image Text"
msgstr "替代文本图像文本"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Although this Website may be linked to other websites, we are not, directly "
"or indirectly, implying any approval, association, sponsorship, endorsement,"
" or affiliation with any linked website, unless specifically stated herein."
msgstr "虽然本网站可能与其他网站相关联，但我们并非直接或间接暗示任何批准，关联，赞助，认可或与任何链接网站的关联，除非在此明确说明。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Always Underline"
msgstr "永远加底线"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Always Visible"
msgstr "始终可见"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_personal_s_numbers
msgid "Amazing Pages"
msgstr "令人惊叹的页面"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_3_s_three_columns
msgid "Amazing Sound Quality"
msgstr "令人惊叹的音质"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Amazing pages"
msgstr "惊人的页面"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map
msgid "An address must be specified for a map to be embedded"
msgstr "必须为要嵌入的地图指定地址"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "An error has occured, the form has not been sent."
msgstr "出现错误，表单未被发送。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "An error occurred while rendering the template"
msgstr "渲染模板时发生错误"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "An example alert with an icon"
msgstr "带图标的警报示例"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "An item"
msgstr "一个项目"

#. module: website
#: model:ir.actions.client,name:website.backend_dashboard
#: model:ir.ui.menu,name:website.menu_website_analytics
msgid "Analytics"
msgstr "分析"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics cookies and privacy information."
msgstr "cookies数据分析和 隐私信息."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Analytics<br/>(optional)"
msgstr "分析<br/>(可选)"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Anchor copied to clipboard<br>Link: %s"
msgstr "锚点已经复制到剪贴板<br>链接: %s"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Anchor name"
msgstr "锚点名称"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_image_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_alternation_text_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_default_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_image_texts_image_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_mosaic_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_reversed_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_texts_image_texts_template
msgid "And a great subtitle"
msgstr "添加一句口号"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/web_editor.xml:0
#, python-format
msgid "Animate"
msgstr "动画"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/web_editor.xml:0
#: code:addons/website/static/src/xml/web_editor.xml:0
#, python-format
msgid "Animate text"
msgstr "动画文本"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Animated"
msgstr "动画"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Animation"
msgstr "动画"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "Another color block"
msgstr "另一个颜色模块"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Another feature"
msgstr "另一个功能"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Another link"
msgstr "其他链接"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__append
msgid "Append"
msgstr "附加"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_add_features
#: model:ir.ui.menu,name:website.menu_website_add_features
msgid "Apps"
msgstr "应用"

#. module: website
#. odoo-python
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Apps url"
msgstr "应用链接"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch
msgid "Arch"
msgstr "结构"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__arch_db
#: model:ir.model.fields,field_description:website.field_website_page__arch_db
msgid "Arch Blob"
msgstr "弧形斑点"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__arch_fs
#: model:ir.model.fields,field_description:website.field_website_page__arch_fs
msgid "Arch Filename"
msgstr "弧形文件名"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__arch_fs
msgid "Arch Fs"
msgstr "结构 Fs"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Archived"
msgstr "已存档"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "Are you sure you want to delete this page?"
msgstr "您确定要删除此页面吗？"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "Are you sure you want to delete those pages?"
msgstr "您确定要删除这些页面吗？"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Arrows"
msgstr "箭头"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/options.xml:0
#, python-format
msgid "As promised, we will offer 4 free tickets to our next summit."
msgstr "正如承诺的那样，我们将提供 4 张免费参加下一次峰会的门票。"

#. module: website
#: model:ir.model,name:website.model_ir_asset
msgid "Asset"
msgstr "资产"

#. module: website
#: model:ir.model,name:website.model_web_editor_assets
msgid "Assets Utils"
msgstr "资产工具"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__copy_ids
msgid "Assets using a copy of me"
msgstr "使用我的副本的资产"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "At The End"
msgstr "在最后"

#. module: website
#: model:ir.model,name:website.model_ir_attachment
msgid "Attachment"
msgstr "附件"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__copy_ids
msgid "Attachment using a copy of me"
msgstr "使用我的副本附件"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Authenticate users, protect user data and allow the website to deliver the services users expects,\n"
"                                                such as maintaining the content of their cart, or allowing file uploads."
msgstr ""
"对用户进行身份验证，保护用户数据并允许网站提供用户期望的服务，\n"
"例如维护购物车的内容，或允许文件上传。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Author"
msgstr "编写者"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Authorized Groups"
msgstr "授权群组"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Auto"
msgstr "自动"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Automatically opens the pop-up if the user stays on a page longer than the "
"specified time."
msgstr "如果用户停留在网页上的时间超过指定时间，则自动打开弹出窗口。"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__auto_redirect_lang
msgid "Autoredirect Language"
msgstr "自动重定向语言"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Autosizing"
msgstr "自动尺寸"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_published
#: model:ir.model.fields,field_description:website.field_ir_cron__website_published
msgid "Available on the Website"
msgstr "可用于网站"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"
msgstr "BSgzTvR5L1GB9jriT451iTN4huVPxHmltG6T6eo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "BTS Base Colors"
msgstr "BTS 底色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Backdrop"
msgstr "背景"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid "Backend"
msgstr "后端"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Background"
msgstr "背景"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#: code:addons/website/static/src/js/tours/configurator_tour.js:0
#, python-format
msgid "Background Shape"
msgstr "后台形状"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.background.video.xml:0
#, python-format
msgid "Background video"
msgstr "背景视频"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Badge"
msgstr "徽标"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Bags"
msgstr "包"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Banner"
msgstr "横幅"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Horizontal"
msgstr "横条纹"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Bar Vertical"
msgstr "竖条纹"

#. module: website
#: model:ir.model,name:website.model_base
msgid "Base"
msgstr "基数"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__arch_base
#: model:ir.model.fields,field_description:website.field_website_page__arch_base
msgid "Base View Architecture"
msgstr "基础视图结构"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__primary
msgid "Base view"
msgstr "基本视图"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
#, python-format
msgid "Basic"
msgstr "基本"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Basic example"
msgstr "基本示例"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Basic sales &amp; marketing for up to 2 users"
msgstr "基本销售和营销 - 最多 2 名用户"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Beautiful snippets"
msgstr "漂亮的片段"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Beef Carpaccio"
msgstr "生牛肉薄片"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns_menu
msgid "Beef Carpaccio, Filet Mignon 8oz and Cheesecake"
msgstr "卡帕奇牛肉、8 盎司菲力牛排和芝士蛋糕"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__before
msgid "Before"
msgstr "在之前"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Beginner"
msgstr "初级版"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Below Each Other"
msgstr "彼此下方"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big"
msgstr "大的"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Big Boxes"
msgstr "大盒子"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Big Icons Subtitles"
msgstr "大图标字幕"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklist this field for web forms"
msgstr "在网上表单中为此字段设置黑名单"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model_fields__website_form_blacklisted
msgid "Blacklisted in web forms"
msgstr "已经在网站表单的黑名单中"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.xml:0
#, python-format
msgid "Blank Page"
msgstr "空白页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Blazers"
msgstr "开拓者"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Block"
msgstr "阻塞"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Blockquote"
msgstr "引用"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Blog"
msgstr "博客"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Blog Post"
msgstr "博文"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_news
msgid "Blogging and posting relevant content"
msgstr "写博客和发布相关内容"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Blur"
msgstr "模糊"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action_menu
msgid "Book your table today"
msgstr "今天就预订餐桌"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Books"
msgstr "图书"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Bootstrap-based templates"
msgstr "基于引导的模板"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
#, python-format
msgid "Border"
msgstr "边框"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Bottom"
msgstr "下边框"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
msgid "Border Color"
msgstr "边框颜色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Radius"
msgstr "边框半径"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Border Width"
msgstr "边框宽度"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Border radius large"
msgstr "大圆角边框"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Border radius medium"
msgstr "中圆角边框"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Border radius none"
msgstr "无圆角边框"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Border radius small"
msgstr "小圆角边框"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bordered"
msgstr "有边框"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_four
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_one
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_three
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_two
#: model_terms:ir.ui.view,arch_db:website.template_header_search
msgid "Bottom"
msgstr "下边"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Bottom to Top"
msgstr "自下而上"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Bounce"
msgstr "退回"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Boxed"
msgstr "盒装的"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Boxes"
msgstr "盒子"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Breadcrumb"
msgstr "面包屑"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Build my website"
msgstr "构建我的网站"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Building blocks system"
msgstr "构建构建块系统"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Building your %s"
msgstr "构建您的 %s"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Building your website."
msgstr "正在建立您的网站。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
#, python-format
msgid "Building your website..."
msgstr "构建您的网站..."

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__bundle
msgid "Bundle"
msgstr "捆"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.grid_layout_options
#: model_terms:ir.ui.view,arch_db:website.s_button
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Button"
msgstr "按钮"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Button Position"
msgstr "按钮位置"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Buttons"
msgstr "按钮"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_1_s_banner
msgid ""
"By Crafting unique and compelling brand identities that leave a lasting "
"impact."
msgstr "打造独一无二、引人入胜的品牌形象，留下持久的影响。"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_url
#: model:ir.model.fields,field_description:website.field_website__cdn_url
msgid "CDN Base URL"
msgstr "CDN基本网址"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,field_description:website.field_website__cdn_filters
msgid "CDN Filters"
msgstr "CDN筛选"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "CTA"
msgstr "CTA"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Call to Action"
msgstr "调用动作"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Call us"
msgstr "致电我们"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Call-to-action"
msgstr "调用动作"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Camera"
msgstr "摄像头"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__can_publish
#: model:ir.model.fields,field_description:website.field_res_users__can_publish
#: model:ir.model.fields,field_description:website.field_website_controller_page__can_publish
#: model:ir.model.fields,field_description:website.field_website_page__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__can_publish
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__can_publish
msgid "Can Publish"
msgstr "可以发布"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/dialog.js:0
#: code:addons/website/static/src/components/dialog/page_properties.js:0
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#: code:addons/website/static/src/js/utils.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid ""
"Cannot apply this option on current text selection. Try clearing the format "
"and try again."
msgstr "无法在当前文本选择中应用该选项。请尝试清除格式，然后再试一次。"

#. module: website
#. odoo-python
#: code:addons/website/models/res_lang.py:0
#, python-format
msgid "Cannot deactivate a language that is currently used on a website."
msgstr "无法停用当前在网站上使用的语言。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Cannot load google map."
msgstr "无法加载谷歌地图。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Card"
msgstr "卡片"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Card Style"
msgstr "卡片样式"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Card link"
msgstr "卡片链接"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Card title"
msgstr "卡片标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Cards"
msgstr "卡片"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_career
msgid "Career"
msgstr "职业"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
#, python-format
msgid "Careful"
msgstr "小心"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Carousel"
msgstr "旋转木马"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Case Studies"
msgstr "案例研究"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid "Catchy Headline"
msgstr "醒目的标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
msgid "Category"
msgstr "类别"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Category of Cookie"
msgstr "Cookie 类别"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Center"
msgstr "居中"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Centered"
msgstr "居中的"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Change Icons"
msgstr "更改图标"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Change theme in a few clicks, and browse through Odoo's catalog of\n"
"                            ready-to-use themes available in our app store."
msgstr ""
"只需要点击几下就可以改变主题，同时可以在我们的应用商店中\n"
"                           浏览 ERP 提供的可用主题."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing the color palette will reset all your color customizations, are you"
" sure you want to proceed?"
msgstr "更改调色板会重置您的所有颜色自定义设置，您确定要继续吗？"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Changing theme requires to leave the editor. This will save all your "
"changes, are you sure you want to proceed? Be careful that changing the "
"theme will reset all your color customizations."
msgstr "更改主题需要离开编辑器。这将保存您的所有更改，您确定要继续吗？请注意，更改主题将重置您的所有颜色自定义设置。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Chart"
msgstr "图表"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_live_chat
msgid "Chat with visitors to improve traction"
msgstr "与访客聊天，增加互动"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "Check out now and get $20 off your first order."
msgstr "现在付款可以享受首个订单立减$20。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/content/website_root.js:0
#, python-format
msgid "Check your configuration."
msgstr "检查配置。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Check your connection and try again"
msgstr "检查您的连接并重试"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Checkbox"
msgstr "复选框"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Checkbox List"
msgstr "复选框列表"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Cheese Onion Rings"
msgstr "芝士洋葱圈"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Chefs Fresh Soup of the Day"
msgstr "厨师推荐每日鲜汤"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__child_id
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Child Menus"
msgstr "下级菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Children"
msgstr "子级"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Choose"
msgstr "选择"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_picture
msgid ""
"Choose a vibrant image and write an inspiring paragraph about it.<br/> It "
"does not have to be long, but it should reinforce your image."
msgstr "选择一个充满活力的图像，并写一个鼓舞人心的段落。<br/>它不需要很长，但它应该强化您的图像。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Choose an anchor name"
msgstr "选择一个锚点名称"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#, python-format
msgid "Choose another theme"
msgstr "选择其他主题"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Choose your favorite"
msgstr "选择您喜欢的"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Circle"
msgstr "圆形"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Classic"
msgstr "经典"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Clean"
msgstr "干净"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Clever Slogan"
msgstr "聪明的口号"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Click and change content directly from the front-end: no complex back\n"
"                            end to deal with."
msgstr ""
"点击并直接从前端更改内容：\n"
"无需处理复杂的后端。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Click here to go back to block tab."
msgstr "点击此处返回构建块页卡。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#, python-format
msgid "Click here to setup your social networks"
msgstr "点击这里来设置您的社交网络"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#, python-format
msgid "Click on"
msgstr "点击"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_embed_code
msgid ""
"Click on <b>\"Edit\"</b> in the right panel to replace this with your own "
"HTML code"
msgstr "点击右侧面板中的<b>\"编辑\"</b>，将其替换为您自己的 HTML 代码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Click on the icon to adapt it <br/>to your purpose."
msgstr "单击图标以使其 <br/>适应您的目的。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Click to choose more images"
msgstr "点击选择更多图像"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Click to select"
msgstr "单击选择"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_personal_s_numbers
msgid "Clients"
msgstr "客户"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.xml:0
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/xml/website.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.qweb_500
#: model_terms:ir.ui.view,arch_db:website.s_popup
#: model_terms:ir.ui.view,arch_db:website.show_website_info
#: model_terms:ir.ui.view,arch_db:website.template_header_hamburger
#: model_terms:ir.ui.view,arch_db:website.template_header_mobile
#, python-format
msgid "Close"
msgstr "关闭"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Close Button Color"
msgstr "关闭按钮颜色"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
#, python-format
msgid "Close editor"
msgstr "关闭编辑器"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Clothes"
msgstr "衣服"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Clothes, Marketing, ..."
msgstr "服装、市场营销, ..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Code"
msgstr "代码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Code Injection"
msgstr "代码注入"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
msgid "Collapse Icon"
msgstr "折叠图标"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_badge_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Color"
msgstr "颜色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Color Presets"
msgstr "色彩预设"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid ""
"Color blocks are a simple and effective way to <b>present and highlight your"
" content</b>. Choose an image or a color for the background. You can even "
"resize and duplicate the blocks to create your own layout. Add images or "
"icons to customize the blocks."
msgstr ""
"颜色块是一种简单而有效的方式来<b>展示和突出您的内容</b>。选择一个图像或颜色作为背景。你甚至可以调整大小和复制模块来创建自己的布局。添加图像或图标可以自定义模块。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Color filter"
msgstr "颜色筛选"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Colors"
msgstr "颜色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Cols"
msgstr "列"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Columns"
msgstr "列"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_0_s_cover
msgid "Coming Soon"
msgstr "即将推出"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__website_config_preselection
msgid ""
"Comma-separated list of website type/purpose for which this feature should "
"be pre-selected"
msgstr "以逗号分隔的网站类型/目的列表，应为其预选该功能"

#. module: website
#: model:ir.model,name:website.model_res_company
msgid "Companies"
msgstr "公司"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#: model:ir.model.fields,field_description:website.field_website__company_id
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Company"
msgstr "公司"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Comparisons"
msgstr "比较"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Complete CRM for any size team"
msgstr "为任何规模的团队完成CRM"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Components"
msgstr "组件"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers"
msgstr "电脑"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Computers &amp; Devices"
msgstr "电脑 &amp; 设备"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Conditionally"
msgstr "用条件地"

#. module: website
#: model:ir.model,name:website.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_global_configuration
msgid "Configuration"
msgstr "配置"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__configurator_done
msgid "Configurator Done"
msgstr "配置完成"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Configuring your %s."
msgstr "正在设置您的%s。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/dialog.js:0
#, python-format
msgid "Confirmation"
msgstr "确认"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_dashboard/website_dashboard.xml:0
#, python-format
msgid "Connect Plausible"
msgstr "连接Plausible"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Connect with us"
msgstr "与我们联系"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Connected"
msgstr "已连接"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
msgid "Connector"
msgstr "连接器"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_search_console
msgid "Console Google Search"
msgstr "控制台谷歌搜索"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_features
msgid "Consulting"
msgstr "咨询"

#. module: website
#: model:ir.model,name:website.model_res_partner
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_id
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Contact"
msgstr "联系人"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website.header_call_to_action
#: model_terms:ir.ui.view,arch_db:website.s_text_block_h2_contact
#, python-format
msgid "Contact Us"
msgstr "联系我们"

#. module: website
#. odoo-python
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Contact Visitor"
msgstr "联系访客"

#. module: website
#: model:website.menu,name:website.menu_contactus
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
#: model_terms:ir.ui.view,arch_db:website.s_cover
#: model_terms:ir.ui.view,arch_db:website.s_text_cover
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "Contact us"
msgstr "联系我们"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
msgid ""
"Contact us about anything related to our company or services.<br/>\n"
"                                    We'll do our best to get back to you as soon as possible."
msgstr ""
"如有任何关于我们公司或服务的疑问，欢迎联络我们。<br/>\n"
"                                    我们会尽快回复您。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Contact us anytime"
msgstr "随时联系我们"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Contact us for any issue or question"
msgstr "如有任何问题，请与我们联系"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_call_to_action
msgid ""
"Contact us today to embark on your path to a healthier, more vibrant you. "
"Your fitness journey begins here."
msgstr "立即与我们联系，踏上更健康、更有活力的新旅程。您的健身之旅由此开始。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Contacts"
msgstr "联系人"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Contain"
msgstr "包含"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Contains"
msgstr "包含"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_robots__content
#: model:ir.ui.menu,name:website.menu_content
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Content"
msgstr "内容"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__cdn_activated
#: model:ir.model.fields,field_description:website.field_website__cdn_activated
msgid "Content Delivery Network (CDN)"
msgstr "内容发布网络 (CDN)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Content Width"
msgstr "内容宽度"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/editor/editor.js:0
#, python-format
msgid "Content saved."
msgstr "内容已保存。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
#, python-format
msgid "Content to translate"
msgstr "待翻译的内容"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Continue reading <i class=\"fa fa-long-arrow-right align-middle ms-1\"/>"
msgstr "继续阅读<i class=\"fa fa-long-arrow-right align-middle ms-1\"/>"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__controller_page_ids
#: model:ir.model.fields,field_description:website.field_website_controller_page__controller_page_ids
#: model:ir.model.fields,field_description:website.field_website_page__controller_page_ids
msgid "Controller Page"
msgstr "控制器页面"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "Cookie Policy"
msgstr "Cookie 政策"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,field_description:website.field_website__cookies_bar
msgid "Cookies Bar"
msgstr "Cookie 栏位"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Cookies are small bits of text sent by our servers to your computer or device when you access our services.\n"
"                            They are stored in your browser and later sent back to our servers so that we can provide contextual content.\n"
"                            Without cookies, using the web would be a much more frustrating experience.\n"
"                            We use them to support your activities on our website. For example, your session (so you don't have to login again) or your shopping cart.\n"
"                            <br/>\n"
"                            Cookies are also used to help us understand your preferences based on previous or current activity on our website (the pages you have\n"
"                            visited), your language and country, which enables us to provide you with improved services.\n"
"                            We also use cookies to help us compile aggregate data about site traffic and site interaction so that we can offer\n"
"                            better site experiences and tools in the future."
msgstr ""
"Cookies 是当您访问我们的服务时由我们的服务器发送到您的计算机或设备上的一小段文本。\n"
"                            它们存储在您的浏览器，然后发送回我们的服务器，以便我们可以提供上下文内容。\n"
"                            如果没有 cookie，使用网络将是一个更令人沮丧的体验。\n"
"                            我们使用它们来支持您在我们网站上的活动。例如，您的会话（这样您就不必再次登录）或您的购物车。\n"
"                            <br/>\n"
"                            Cookies 还用于帮助我们了解您在我们网站（您访问过的网页）上的活动偏好\n"
"                            ，您的语言和国家，使我们能够为您提供更佳服务。\n"
"                            我们还使用 cookie 来帮助我们汇编网站流量和网站互动的综合数据，以便我们将来提供\n"
"                            更好的网站体验和工具。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Copyright"
msgstr "Copyright"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Countdown"
msgstr "倒数计时"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#, python-format
msgid "Countdown ends in"
msgstr "倒计时结束"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/options.xml:0
#, python-format
msgid "Countdown is over - Firework"
msgstr "倒计时结束 - 烟花"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_id
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Country"
msgstr "国家/地区"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__country_flag
msgid "Country Flag"
msgstr "国旗"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Course"
msgstr "课程"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.record_cover
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Cover"
msgstr "覆盖"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Cover Photo"
msgstr "封面照片"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_cover_properties_mixin__cover_properties
msgid "Cover Properties"
msgstr "封面属性"

#. module: website
#: model:ir.model,name:website.model_website_cover_properties_mixin
msgid "Cover Properties Website Mixin"
msgstr "封面属性网站混入"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_cover
msgid "Crafting Your Digital Success Story"
msgstr "打造您的数字成功故事"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Cras justo odio"
msgstr "Cras justo odio"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#: code:addons/website/static/src/js/utils.js:0
#: model_terms:ir.ui.view,arch_db:website.view_website_form_view_themes_modal
#, python-format
msgid "Create"
msgstr "创建"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid "Create Page"
msgstr "创建网页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "Create a"
msgstr "创建"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Create a Google Project and Get a Key"
msgstr "创建 Google 项目并获取密钥"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Create a link to target this section"
msgstr "创建指向此部分的链接"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Create new"
msgstr "创建新的"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Create your page from scratch by dragging and dropping pre-made,\n"
"                            fully customizable building blocks."
msgstr ""
"通过拖放预制的、完全可定制的构建块\n"
"可以从头开始创建您的网页。"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website__create_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_uid
#: model:ir.model.fields,field_description:website.field_website_controller_page__create_uid
#: model:ir.model.fields,field_description:website.field_website_menu__create_uid
#: model:ir.model.fields,field_description:website.field_website_page__create_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_uid
#: model:ir.model.fields,field_description:website.field_website_robots__create_uid
#: model:ir.model.fields,field_description:website.field_website_route__create_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__create_uid
msgid "Created by"
msgstr "创建人"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid ""
"Created in 2021, the company is young and dynamic. Discover the composition "
"of the team and their skills."
msgstr "公司成立于 2021 年，年轻而充满活力。了解团队的组成和技能。"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__create_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website__create_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__create_date
#: model:ir.model.fields,field_description:website.field_website_controller_page__create_date
#: model:ir.model.fields,field_description:website.field_website_menu__create_date
#: model:ir.model.fields,field_description:website.field_website_page__create_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__create_date
#: model:ir.model.fields,field_description:website.field_website_robots__create_date
#: model:ir.model.fields,field_description:website.field_website_route__create_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__create_date
msgid "Created on"
msgstr "创建日期"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_color_blocks_2
msgid "Crystal Clear Sound"
msgstr "水晶般清晰的声音"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
msgid "Curved arrow"
msgstr "弧形箭头"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.column_count_option
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#, python-format
msgid "Custom"
msgstr "自定义"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_head
msgid "Custom <head> code"
msgstr "自定义 <head> 代码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Custom Code"
msgstr "客户编码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Custom Key"
msgstr "自定义键"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Custom Text"
msgstr "自定义文本"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Custom Url"
msgstr "自定义Url"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__custom_code_footer
msgid "Custom end of <body> code"
msgstr "自定义结尾<body> 代码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Custom field"
msgstr "自定义字段"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__auth_signup_uninvited
#: model:ir.model.fields,field_description:website.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "客户账户"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Customers"
msgstr "客户"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Customization tool"
msgstr "定制化工具"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__customize_show
msgid "Customize Show"
msgstr "自定义显示"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M"
msgstr "D - H - M"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "D - H - M - S"
msgstr "D - H - M - S"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "DRAG BUILDING BLOCKS HERE"
msgstr "在这里拖拽模块"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
msgid "Danger"
msgstr "危险"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dashed"
msgstr "虚线"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/services/website_service.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#, python-format
msgid "Data"
msgstr "数据"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Border"
msgstr "数据边框"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Data Color"
msgstr "数据颜色"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Border"
msgstr "数据集边界"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Dataset Color"
msgstr "数据集颜色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Date"
msgstr "日期"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Date &amp; Time"
msgstr "日期时间"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#: model_terms:ir.ui.view,arch_db:website.s_countdown
#, python-format
msgid "Days"
msgstr "天"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Decimal Number"
msgstr "小数分割符"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Default"
msgstr "默认"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Access Rights"
msgstr "默认访问权限"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/options.js:0
#, python-format
msgid "Default Input Style"
msgstr "默认输入样式"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__default_lang_id
msgid "Default Language"
msgstr "默认语言"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__default_layout
msgid "Default Layout"
msgstr "默认布局"

#. module: website
#: model:website.menu,name:website.main_menu
msgid "Default Main Menu"
msgstr "默认主菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Default Reversed"
msgstr "默认反转"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,field_description:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Default Social Share Image"
msgstr "默认社交分享图像"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Default Value"
msgstr "默认值"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Default checkbox"
msgstr "默认复选框"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_id
msgid "Default language"
msgstr "默认语言"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_default_lang_code
msgid "Default language code"
msgstr "默认语言代码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_controller_page_listing_layout
msgid "Default layout"
msgstr "默认布局"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Default radio"
msgstr "默认无线电"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Default switch checkbox input"
msgstr "默认开关复选框输入"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Delay"
msgstr "延迟"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Delete Blocks"
msgstr "删除模块"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Delete Menu Item"
msgstr "移除菜单项目"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.js:0
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "Delete Page"
msgstr "删除网页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"Delete the above image or replace it with a picture that illustrates your "
"message. Click on the picture to change its <em>rounded corner</em> style."
msgstr "删除上面的图像或将其替换为说明您的信息的图像。点击图像更改其 <em>圆角</em> 样式。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Delete this font"
msgstr "移除这字体"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"Deleting a font requires a reload of the page. This will save all your "
"changes and reload the page, are you sure you want to proceed?"
msgstr "删除字体需要重新加载网页。这将保存您的所有更改并重新加载网页，您确定要继续吗？"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Deliveries"
msgstr "交货"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Departments"
msgstr "部门"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.js:0
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "Dependencies"
msgstr "依赖"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Describe your field here."
msgstr "在这里描述您的字段"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__description
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#, python-format
msgid "Description"
msgstr "描述"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_our_services
msgid "Description of your services offer"
msgstr "描述您提供的服务"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "Descriptions"
msgstr "描述"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Descriptive"
msgstr "描述"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Design"
msgstr "设计"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Design features"
msgstr "设计功能"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_pricing
msgid "Designed to drive conversion"
msgstr "旨在推动转化"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_text_cover
msgid "Designed to provide an immersive audio experience on the go."
msgstr "专为在旅途中提供身临其境的音频体验而设计。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#, python-format
msgid "Desktop"
msgstr "桌面"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Desktop computers"
msgstr "台式机"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Detail"
msgstr "明细"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Details"
msgstr "细节"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Detect"
msgstr "检测"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_image_text_2nd
msgid "Digital Consulting Expertise"
msgstr "数字咨询专长"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Direction"
msgstr "方向"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__directive
msgid "Directive"
msgstr "指示"

#. module: website
#: model:ir.actions.server,name:website.website_disable_unused_snippets_assets_ir_actions_server
msgid "Disable unused snippets assets"
msgstr "禁用未使用的片段资产"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Disabled"
msgstr "已禁用"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Disappearing"
msgstr "消失"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disappears"
msgstr "消失"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Discard"
msgstr "丢弃"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_gallery_s_banner
msgid "Discover Our Univers"
msgstr "了解我们的大学"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Discover all the features"
msgstr "了解所有功能"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Discover more"
msgstr "发现更多"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Discover more <i class=\"fa fa-long-arrow-right align-middle ms-1\"/>"
msgstr "了解更多<i class=\"fa fa-long-arrow-right align-middle ms-1\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_image_text
msgid ""
"Discover our comprehensive marketing service designed to amplify your "
"brand's reach and impact."
msgstr "了解我们旨在扩大您的品牌影响力的综合营销服务。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Discover our culture and our values"
msgstr "发现我们的文化和价值观"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our legal notice"
msgstr "发现我们的法律声明"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Discover our realisations"
msgstr "发现我们的认知"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "Discover our team"
msgstr "发现我们的团队"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Discrete"
msgstr "离散"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Discussion Group"
msgstr "讨论组"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Disk"
msgstr "磁盘"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Display"
msgstr "显示"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Display 1"
msgstr "显示 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Display 2"
msgstr "显示2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Display 3"
msgstr "显示3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Display 4"
msgstr "显示4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Display 5"
msgstr "显示 5"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Display 6"
msgstr "显示 6"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Display Inline"
msgstr "显示内联"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__display_name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_theme_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website__display_name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__display_name
#: model:ir.model.fields,field_description:website.field_website_controller_page__display_name
#: model:ir.model.fields,field_description:website.field_website_menu__display_name
#: model:ir.model.fields,field_description:website.field_website_page__display_name
#: model:ir.model.fields,field_description:website.field_website_rewrite__display_name
#: model:ir.model.fields,field_description:website.field_website_robots__display_name
#: model:ir.model.fields,field_description:website.field_website_route__display_name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__display_name
#: model:ir.model.fields,field_description:website.field_website_track__display_name
#: model:ir.model.fields,field_description:website.field_website_visitor__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display a customizable cookies bar on your website"
msgstr "在您的网站上显示可自定义的cookie栏"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_cookies_bar
#: model:ir.model.fields,help:website.field_website__cookies_bar
msgid "Display a customizable cookies bar on your website."
msgstr "在您的网站上显示一个可定制的cookie栏位。"

#. module: website
#. odoo-python
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the badges"
msgstr "显示徽标"

#. module: website
#. odoo-python
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the biography"
msgstr "显示传记"

#. module: website
#. odoo-python
#: code:addons/website/models/ir_qweb_fields.py:0
#, python-format
msgid "Display the website description"
msgstr "显示网站说明"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_logo
#: model:ir.model.fields,help:website.field_website__logo
msgid "Display this logo on the website."
msgstr "在网站上显示此徽标。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Display this website when users visit this domain"
msgstr "用户访问此域时显示此网站"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Do not copy/paste code you do not understand, this could put your data at "
"risk."
msgstr "不要复制/粘贴您不理解的代码，这可能会使您的数据处于危险之中."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Do you need specific information? Our specialists will help you with "
"pleasure."
msgstr "您需要具体信息吗？我们的专家将愉快地帮助您。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Do you want to install the \"%s\" App?"
msgstr "您想安装 \"%s\" 这个应用程序?"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Documentation"
msgstr "文档"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Doesn't contain"
msgstr "不包含"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Dolly Zoom"
msgstr "多利变焦"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__record_domain
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Domain"
msgstr "域"

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__record_domain
msgid "Domain to restrict records that can be viewed publicly"
msgstr "限制可公开查看记录的域"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "Don't forget to update all links referring to it."
msgstr "别忘记更新所有指向它的链接。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
#, python-format
msgid "Don't show again"
msgstr "不要再显示"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#, python-format
msgid "Don't worry, you can switch later."
msgstr "无需担心，您可以之后转换。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation"
msgstr "打赏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Donation Button"
msgstr "打赏按钮"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Dots"
msgstr "点"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Dotted"
msgstr "点线"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Double"
msgstr "双"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Double click an icon to replace it with one of your choice."
msgstr "双击图标将其替换为您选择的图标。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Doughnut"
msgstr "甜甜圈"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid ""
"Drag the <b>%s</b> building block and drop it at the bottom of the page."
msgstr "拖拽 <b>%s</b>构建构建块 同时将它拖拽到网页底部.."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Drag to the right to get a submenu"
msgstr "向右拖动以获取子菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Dresses"
msgstr "连衣裙"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Dropdown"
msgstr "下拉"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Dropdown menu"
msgstr "下拉菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Due Date"
msgstr "到期日期"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate"
msgstr "复制"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "Duplicate Page"
msgstr "复制网页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Duplicate blocks <br/>to add more steps."
msgstr "复制构建块<br/>可以添加更多步骤。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Duplicate blocks and columns to add more features."
msgstr "可以复制模块和列添加更多的功能。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Duration"
msgstr "时长"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Dynamic Carousel"
msgstr "动态循环播放"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Dynamic Content"
msgstr "动态内容"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Dynamic Snippet"
msgstr "动态片段"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_homepage_url
#: model:ir.model.fields,help:website.field_website__homepage_url
msgid "E.g. /contactus or /shop"
msgstr "例如 /联系我们或/商店"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_domain
#: model:ir.model.fields,help:website.field_website__domain
msgid "E.g. https://www.mydomain.com"
msgstr "例如 https://www.mydomain.com"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Easily design your own Odoo templates thanks to clean HTML\n"
"                            structure and bootstrap CSS."
msgstr "借助简单的 HTML 结构和 Bootstrap CSS，轻松设计您自己的 ERP 模板."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_dashboard/website_dashboard.xml:0
#, python-format
msgid "Easily track your visitor with Plausible"
msgstr "用Plausible轻松跟踪您的访客"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_text_cover
msgid "EchoTunes Wireless Earbuds"
msgstr "EchoTunes 无线耳机"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid ""
"EchoTunes comes with customizable ear tip sizes that provide a secure and "
"comfortable fit."
msgstr "EchoTunes 配有可定制的耳塞尺寸，佩戴安全舒适。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/systray_items/edit_website.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit"
msgstr "编辑"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/services/website_custom_menus.js:0
#, python-format
msgid "Edit %s"
msgstr "编辑 %s"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
#, python-format
msgid "Edit HTML anyway"
msgstr "无论如何编辑 HTML"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.xml:0
#, python-format
msgid "Edit Head and Body Code"
msgstr "编辑 Head 及 Body 程式代码"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.js:0
#: code:addons/website/static/src/js/widgets/link_popover_widget.js:0
#: model:ir.ui.menu,name:website.custom_menu_edit_menu
#, python-format
msgid "Edit Menu"
msgstr "编辑菜单"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Edit Menu Item"
msgstr "编辑菜单项目"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Edit Message"
msgstr "编辑消息"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Edit Styles"
msgstr "编辑样式"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_embed_code/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#, python-format
msgid "Edit embedded code"
msgstr "编辑嵌入代码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.publish_management
msgid "Edit in backend"
msgstr "在后端编辑"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Edit robots.txt"
msgstr "编辑robots.txt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "Edit this content"
msgstr "编辑此内容"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Edit video"
msgstr "编辑视频"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "Editor"
msgstr "编辑器"

#. module: website
#: model:res.groups,name:website.group_website_designer
msgid "Editor and Designer"
msgstr "编辑器和设计器"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Effect"
msgstr "结果"

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Either action_server_id or filter_id must be provided."
msgstr "必须提供 action_server_id 或 filter_id。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Electronics"
msgstr "电子产品"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Elements"
msgstr "元素"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_3_s_call_to_action
msgid "Elevate Your Audio Journey Today"
msgstr "今天就提升您的音频之旅"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_1_s_banner
msgid "Elevate Your Brand With Us"
msgstr "与我们一起提升您的品牌"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__email
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Email"
msgstr "电子邮件"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Email & Marketing"
msgstr "电邮及市场营销"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Email Marketing"
msgstr "邮件营销"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Email address"
msgstr "电子邮件地址"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Email support"
msgstr "电子邮件支持"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_full_s_image_text
msgid ""
"Embark on a journey through time as we share the story of our humble "
"beginnings. What started as a simple idea in a garage has evolved into an "
"innovative force in the industry."
msgstr "踏上时间之旅，我们将与您分享我们的创业故事。从车库中的一个简单想法开始，我们已经发展成为行业中的一股创新力量。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Embed Code"
msgstr "嵌入代码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_features
msgid ""
"Empowering your business through strategic digital insights and expertise."
msgstr "通过战略性数字洞察力和专业知识为您的业务赋能。"

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Empty field name in %r"
msgstr "在%r中的空字段名称"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable billing on your Google Project"
msgstr "为您的 Google 项目启用结算功能"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_access
msgid "Enable the form builder feature for this model."
msgstr "启用此模型的表单生成器特性。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Enable the right google map APIs in your google account"
msgstr "在您的google帐户中启用正确的google地图Api"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Enabling your %s."
msgstr "正在启用您的%s。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Engage with your community and build relationships."
msgstr "与您的社群互动并建立关系。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Enter an API Key"
msgstr "输入 API 密钥"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.xml:0
#, python-format
msgid ""
"Enter code that will be added before the </body> of every page of your site."
msgstr "输入将在您网站的</body>每个网页之前添加的代码。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Enter code that will be added into every page of your site"
msgstr "输入将添加到您网站的每个网页的代码"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.xml:0
#, python-format
msgid ""
"Enter code that will be added into the <head> of every page of your site."
msgstr "输入将添加到您网站<head>每个网页的代码。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Enter email"
msgstr "输入电子邮件"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Equal Widths"
msgstr "等宽"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Error"
msgstr "错误"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Essential oils"
msgstr "精油"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Event"
msgstr "活动"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Event heading"
msgstr "活动标题"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_event
#: model_terms:ir.ui.view,arch_db:website.external_snippets
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Events"
msgstr "活动"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_s_carousel
msgid "Every Friday From 6PM to 7PM !"
msgstr "每周五下午 6 点至 7 点！"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Every Time"
msgstr "每次"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Everything"
msgstr "一切"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Examples"
msgstr "例子"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Existing Fields"
msgstr "现有字段"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/utils.js:0
#, python-format
msgid "Expected "
msgstr "预期 "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_cover
msgid ""
"Experience a digital transformation like never before with our range of "
"innovative solutions, designed to illuminate your brand's potential."
msgstr "我们的一系列创新解决方案旨在激发您的品牌潜力，让您体验前所未有的数字化转型。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_pricing_s_text_block_2nd
msgid ""
"Experience the power of our software without breaking the bank – choose a "
"plan that suits you best and start unlocking its full potential today."
msgstr "体验我们软件的强大功能，无需破费--选择最适合您的计划，现在就开始释放它的全部潜能。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_banner
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_text_cover
msgid "Experienced fullstack developer."
msgstr "经验丰富的全栈开发人员。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid ""
"Experienced in effective project management, adept at leading cross-"
"functional teams and delivering successful outcomes with a strategic "
"approach."
msgstr "在有效的项目管理方面经验丰富，善于领导跨职能团队，并以战略方法取得成功。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Expert"
msgstr "定制版"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_privacy_policy
msgid "Explain how you protect privacy"
msgstr "解释您如何保护隐私"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert
msgid ""
"Explain the benefits you offer. <br/>Don't write about products or services "
"here, write about solutions."
msgstr "解释您的的效益。<br/>不要在这里写关于产品或服务，写解决方案。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Explore"
msgstr "探索"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_gallery_s_text_block_2nd
msgid ""
"Explore our captivating gallery, a visual journey showcasing our finest work"
" and creative projects. Immerse yourself in a collection of images that "
"capture the essence of our craftsmanship, innovation, and dedication to "
"excellence."
msgstr "探索我们迷人的画廊，这是一次展示我们最优秀作品和创意项目的视觉之旅。您将沉浸在这些能捕捉到我们的精湛工艺、创新精神和卓越奉献的图片中。"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__mode__extension
msgid "Extension View"
msgstr "扩展视图"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,field_description:website.field_ir_cron__xml_id
#: model:ir.model.fields,field_description:website.field_website_controller_page__xml_id
#: model:ir.model.fields,field_description:website.field_website_page__xml_id
msgid "External ID"
msgstr "外部 ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Extra Large"
msgstr "加大"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "Extra items button"
msgstr "额外项目按钮"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Extra link"
msgstr "额外链接"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Extra page"
msgstr "附加页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Large"
msgstr "特大"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Extra-Small"
msgstr "特小"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "F.A.Q."
msgstr "F.A.Q."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_facebook_page/000.js:0
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.header_social_links
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.s_social_media
#: model_terms:ir.ui.view,arch_db:website.snippets
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#, python-format
msgid "Facebook"
msgstr "脸书"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_facebook
msgid "Facebook Account"
msgstr "Facebook 账户"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade"
msgstr "褪色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fade Out"
msgstr "飞出"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Failed to install \"%s\""
msgstr "无法安装\"%s\""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Farm Friendly Chicken Supreme"
msgstr "生态农场至尊鸡肉"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__favicon
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Favicon"
msgstr "图标"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature One"
msgstr "功能一"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Three"
msgstr "功能三"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_card
msgid "Feature Title"
msgstr "功能标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid "Feature Two"
msgstr "功能二"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__feature_url
msgid "Feature Url"
msgstr "特性Url"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Features"
msgstr "功能"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Features Grid"
msgstr "特色功能列表"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
msgid "Fetched elements"
msgstr "获取元素"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Field"
msgstr "字段"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__field_names
msgid "Field Names"
msgstr "字段名称"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_default_field_id
msgid "Field for custom form data"
msgstr "表单数据的自定义字段"

#. module: website
#: model:ir.model,name:website.model_ir_model_fields
msgid "Fields"
msgstr "字段"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "File Upload"
msgstr "文件上传"

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__arch_fs
#: model:ir.model.fields,help:website.field_website_page__arch_fs
msgid ""
"File from where the view originates.\n"
"                                                          Useful to (hard) reset broken views or to read arch from file in dev-xml mode."
msgstr ""
"视图来源的文件。\n"
"                                                          用于（硬）重置断开的视图或以dev-xml模式从文件读取arch。"

#. module: website
#: model:ir.model,name:website.model_ir_binary
msgid "File streaming helper model for controllers"
msgstr "控制器文件流帮助器模型"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Filet Mignon 8oz"
msgstr "菲力牛柳 8 盎司"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fill"
msgstr "填充"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Fill and justify"
msgstr "填充和对齐"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.xml:0
#: code:addons/website/static/src/components/resource_editor/resource_editor.xml:0
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__filter_id
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
#, python-format
msgid "Filter"
msgstr "筛选"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Filter Intensity"
msgstr "筛选强度"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Find a store near you"
msgstr "查找您附近的商店"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find all information about our deliveries, express deliveries and all you "
"need to know to return a product."
msgstr "查找有关我们的送货、快递和退货所需的所有信息。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Find out how we were able helping them and set in place solutions adapted to"
" their needs."
msgstr "了解我们如何能够帮助他们并制定适合他们需求的解决方案。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Find the best talent for your team."
msgstr "为您的团队寻觅最佳人才。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Find the perfect solution for you"
msgstr "为您找到完美的解决方案"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__create_date
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "First Connection"
msgstr "第一次连接"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "First Feature"
msgstr "第一功能"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "First Menu"
msgstr "首个菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "First Time Only"
msgstr "仅限第一次"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "First feature"
msgstr "第一功能"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "First list of Features"
msgstr "第一功能列表"

#. module: website
#: model:ir.model.fields,help:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,help:website.field_website_controller_page__first_page_id
#: model:ir.model.fields,help:website.field_website_page__first_page_id
msgid "First page linked to this view"
msgstr "第一页链接到这个视图"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit content"
msgstr "调整内容"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Fit text"
msgstr "调整文本大小"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Fixed"
msgstr "固定的"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag"
msgstr "标记"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag and Code"
msgstr "旗帜和代码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flag and Text"
msgstr "标志和文本"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flash"
msgstr "闪烁"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flat"
msgstr "普通"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-X"
msgstr "水平翻入"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Flip-In-Y"
msgstr "垂直翻入"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Float"
msgstr "浮动的"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "Follow Us"
msgstr "关注我们"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.header_social_links
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Follow us"
msgstr "跟随我们"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font"
msgstr "字体"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font Size"
msgstr "字体大小"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font family"
msgstr "字形体系"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Font size"
msgstr "字体大小"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__footer_visible
#: model:ir.model.fields,field_description:website.field_website_page__footer_visible
msgid "Footer Visible"
msgstr "页脚可见"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Form"
msgstr "窗体"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_label
msgid ""
"Form action label. Ex: crm.lead could be 'Send an e-mail' and project.issue "
"could be 'Create an Issue'."
msgstr "来自动作标注。提示：crm.lead 可以是 ‘发送一封邮件’，project.issue 可以是 ‘创建一个问题’。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Format"
msgstr "格式"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#: model:website.configurator.feature,name:website.feature_module_forum
#, python-format
msgid "Forum"
msgstr "论坛"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. He loves\n"
"                                to keep his hands full by participating in the development of the software,\n"
"                                marketing, and customer experience strategies."
msgstr ""
"创始人兼首席远见者托尼是公司背后的推动力。他喜欢\n"
"通过参与软件、营销和客户体验策略的开发\n"
"来保持忙碌。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_0_s_three_columns
msgid ""
"Founder and chief visionary, Tony is the driving force behind the company. "
"He loves to keep his hands full by participating in the development of the "
"software, marketing, and customer experience strategies."
msgstr "托尼是公司的创始人和首席远见者，也是公司背后的推动力。他热衷于参与软件开发、市场营销和客户体验战略，从而让自己的工作变得更加充实。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_media_list
msgid ""
"Founder, Tony is the driving force behind the company. He loves to keep his "
"hands full by participating in the development of the software, marketing, "
"and UX strategies."
msgstr "公司创始人托尼是公司的幕后推手。他热衷于参与软件开发、市场营销和用户体验战略，让自己的双手变得充实。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_image_text
msgid ""
"Founder, Tony is the driving force behind the company. He loves to keep his "
"hands full by participating in the development of the software, marketing, "
"and customer experience."
msgstr "公司创始人托尼是公司的幕后推手。他热衷于参与软件开发、市场营销和客户体验，从而让自己的双手变得充实。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Framed"
msgstr "装裱"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr "自由注册"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Bottom"
msgstr "从底部"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Bottom Left"
msgstr "从左下角"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Bottom Right"
msgstr "从右下角"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Left"
msgstr "从左边"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Right"
msgstr "从右边"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_features
msgid ""
"From SEO to social media, we create campaigns that not only get you noticed "
"but also drive engagement and conversions."
msgstr "从搜索引擎优化到社交媒体，我们创建的活动不仅能让您受到关注，还能推动参与度和转化率。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Top"
msgstr "从顶部"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Top Left"
msgstr "从左上角"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "From Top Right"
msgstr "从右上角"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_image
msgid ""
"From revitalizing your visual identity to realigning your messaging for the "
"digital landscape, we'll guide you through a strategic process that ensures "
"your brand remains relevant and resonates with your audience."
msgstr "从重塑您的视觉形象到重新调整您的信息以适应数字环境，我们将指导您完成一个战略过程，确保您的品牌保持相关性并与受众产生共鸣。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"From seminars to team building activities, we offer a wide choice of events "
"to organize."
msgstr "从研讨会到团队建设活动，我们提供多种活动可供选择。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid "Frontend"
msgstr "前端"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full"
msgstr "全"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full Screen"
msgstr "全屏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Full Width"
msgstr "全宽"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full screen"
msgstr "全屏显示"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Full-Width"
msgstr "全宽"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Furniture"
msgstr "家具"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "G-XXXXXXXXXX"
msgstr "G-XXXXXXXXXX"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "GPS &amp; navigation"
msgstr "GPS &amp; 导航"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_gallery_s_text_block_h2
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
msgid "Gallery"
msgstr "画廊"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Gaming"
msgstr "游戏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Get Delivered"
msgstr "得到交付"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules"
msgstr "可使用所有模块"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Get access to all modules and features"
msgstr "可使用所有模块和功能"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Get in touch"
msgstr "保持联系"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_social_media
#, python-format
msgid "GitHub"
msgstr "GitHub"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_github
msgid "GitHub Account"
msgstr "GitHub账户"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_forum
msgid "Give visitors the information they need"
msgstr "为访问者提供他们需要的信息"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Glasses"
msgstr "眼镜"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/redirect_field.xml:0
#, python-format
msgid "Go to"
msgstr "转到"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Go to Page Manager"
msgstr "转到网页管理"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/view_hierarchy.xml:0
#, python-format
msgid "Go to View"
msgstr "前往查看页面"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_dashboard/website_dashboard.xml:0
#: code:addons/website/static/src/client_actions/website_dashboard/website_dashboard.xml:0
#, python-format
msgid "Go to Website"
msgstr "转至网站"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Go to the Theme tab"
msgstr "转到主题选项卡"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.layout
msgid "Go to your Odoo Apps"
msgstr "转到您的Odoo应用程序"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Good copy starts with understanding how your product or service helps your "
"customers. Simple words communicate better than big words and pompous "
"language."
msgstr "好副本首先从解您的产品或服务如何可以帮助您的客户。简单的单词比浮夸的语言更有助于沟通。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/tours/tour_utils.js:0
#, python-format
msgid "Good job! It's time to <b>Save</b> your work."
msgstr "做得好！是时候<b>保存</b>您的工作了。"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_google_analytics
msgid "Google Analytics"
msgstr "Google 分析"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_analytics_key
#: model:ir.model.fields,field_description:website.field_website__google_analytics_key
msgid "Google Analytics Key"
msgstr "谷歌分析密钥"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Google Font address"
msgstr "谷歌字体地址"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Google Map"
msgstr "谷歌地图"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__google_maps_api_key
msgid "Google Maps API Key"
msgstr "Google 地图 API 密钥"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,field_description:website.field_website__google_search_console
msgid "Google Search Console"
msgstr "Google 搜索控制台"

#. module: website
#. odoo-python
#: code:addons/website/models/res_config_settings.py:0
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid ""
"Google doesn't need to be pinged anymore. It will automatically fetch your "
"/sitemap.xml."
msgstr "谷歌不需要再被监视了。它将自动获取 /sitemap.xml."

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__google_search_console
#: model:ir.model.fields,help:website.field_website__google_search_console
msgid "Google key, or Enable to access first reply"
msgstr "Google 密钥，或启用访问第一个回复"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Gray #{grayCode}"
msgstr "灰色 #{grayCode}"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grays"
msgstr "灰色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Great Value"
msgstr "巨大价值"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories are <b>for everyone</b> even when only written <b>for just one"
" person</b>. If you try to write with a wide, general audience in mind, your"
" story will sound fake and lack emotion. No one will be interested. Write "
"for one person. If it’s genuine for the one, it’s genuine for the rest."
msgstr ""
"伟大的故事适合<b>每个人</b>，<b>即使只写给一个人</b>。如果您试图在写作时考虑到广泛的普通读者，您的故事听起来会很假，而且缺乏情感。没有人会感兴趣。为一个人写。如果对一个人来说是真的，那么对其他人来说也是真的。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_block
msgid ""
"Great stories have a <b>personality</b>. Consider telling a great story that"
" provides personality. Writing a story with personality for potential "
"clients will assist with making a relationship connection. This shows up in "
"small quirks like word choices or phrases. Write from your point of view, "
"not from someone else's experience."
msgstr ""
"伟大的故事有<b>个性</b>。考虑讲一个提供个性的好故事。为潜在客户写一个有个性的故事将有助于建立关系。这会出现在单词选择或短语等小怪癖中。从您的角度写，而不是从别人的经验。"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_controller_page__default_layout__grid
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_website_controller_page_listing_layout
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Grid"
msgstr "表格"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Group"
msgstr "组"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Group By"
msgstr "分组方式"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__groups_id
#: model:ir.model.fields,field_description:website.field_website_page__groups_id
msgid "Groups"
msgstr "群组"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "H1"
msgstr "H1"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "H2"
msgstr "H2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "H4 Card title"
msgstr "H4 卡片标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "H5 Card subtitle"
msgstr "H5 卡片子标题"

#. module: website
#: model:ir.ui.menu,name:website.menu_ace_editor
msgid "HTML / CSS Editor"
msgstr "HTML / CSS 编辑器"

#. module: website
#: model:ir.model,name:website.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half Screen"
msgstr "半屏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Half screen"
msgstr "半屏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hamburger menu"
msgstr "汉堡包菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_s_carousel
msgid "Happy Hour"
msgstr "欢乐时光"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/options.xml:0
#, python-format
msgid "Happy Odoo Anniversary!"
msgstr "Odoo 周年快乐！"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__has_social_default_image
msgid "Has Social Default Image"
msgstr "具有社交默认图像"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Header"
msgstr "页眉"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__header_color
#: model:ir.model.fields,field_description:website.field_website_page__header_color
msgid "Header Color"
msgstr "标题颜色"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__header_overlay
#: model:ir.model.fields,field_description:website.field_website_page__header_overlay
msgid "Header Overlay"
msgstr "标题覆盖层"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Header Position"
msgstr "标题位置"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__header_text_color
msgid "Header Text Color"
msgstr "页眉文本颜色"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__header_visible
#: model:ir.model.fields,field_description:website.field_website_page__header_visible
msgid "Header Visible"
msgstr "标题可见"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Heading"
msgstr "标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Heading 1"
msgstr "标题1"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Headings"
msgstr "标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 1"
msgstr "标题1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 2"
msgstr "标题2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 3"
msgstr "标题3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 4"
msgstr "标题4"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 5"
msgstr "标题5"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Headings 6"
msgstr "标题6"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Headline"
msgstr "头条"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height"
msgstr "高度"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Height (Scrolled)"
msgstr "高度（滚动）"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_banner
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_text_cover
msgid "Hello, I'm Tony Fred"
msgstr "你好，我是托尼-弗雷德"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Help center"
msgstr "帮助中心"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
#, python-format
msgid "Here are the visuals used to help you translate efficiently:"
msgstr "这是用来帮助您提高翻译效率的可视化工具："

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Here is an overview of the cookies that may be stored on your device when "
"you visit our website:"
msgstr "以下是您访问我们网站时可能存储在您设备上的 cookie 的概述："

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hidden"
msgstr "隐藏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Hidden for"
msgstr "隐藏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Hide"
msgstr "隐藏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Hide For"
msgstr "隐藏"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_popup/000.js:0
#, python-format
msgid "Hide the cookies bar"
msgstr "隐藏 cookies 通知列"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Hide this page from search results"
msgstr "在搜索结果中隐藏该网页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "High"
msgstr "高"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/web_editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Highlight"
msgstr "高亮"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Hint: How to use Google Map on your website (Contact Us page and as a "
"snippet)"
msgstr "提示:如何在您的网站上使用谷歌地图(联系我们网页和作为一个片段)"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/web_editor.xml:0
#, python-format
msgid ""
"Hint: Type '/' to search an existing page and '#' to link to an anchor."
msgstr "提示：键入“/”搜索现有网页，输入“＃”链接到锚点。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: model:website.menu,name:website.menu_home
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
#, python-format
msgid "Home"
msgstr "首页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Home <span class=\"visually-hidden\">(current)</span>"
msgstr "首页<span class=\"visually-hidden\">(当前)</span>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Home audio"
msgstr "家庭音响"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_list.xml:0
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
#, python-format
msgid "Home page of the current website"
msgstr "当前网站的主页"

#. module: website
#. odoo-javascript
#. odoo-python
#: code:addons/website/models/website.py:0
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#: model:ir.model.fields,field_description:website.field_website_page__is_homepage
#: model:ir.ui.menu,name:website.menu_website_preview
#, python-format
msgid "Homepage"
msgstr "主页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Homepage URL"
msgstr "主页URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_homepage_url
#: model:ir.model.fields,field_description:website.field_website__homepage_url
msgid "Homepage Url"
msgstr "主页URL"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Hoodies"
msgstr "连帽衫"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Horizontal"
msgstr "水平"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#: model_terms:ir.ui.view,arch_db:website.s_countdown
#, python-format
msgid "Hours"
msgstr "小时"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "How can we help?"
msgstr "我们能帮助你什么吗？"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Hue"
msgstr "色调"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Hybrid"
msgstr "混合动力"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "I agree"
msgstr "同意"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "I am sure about this."
msgstr "我确信这一点。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "I want"
msgstr "我想"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_personal_s_image_text
msgid ""
"I'm a fullstack developer with a background in management. My analytical "
"skills, coupled with effective communication, enable me to lead cross-"
"functional teams to success."
msgstr "我是一名具有管理背景的全栈开发人员。我的分析能力和有效的沟通使我能够领导跨职能团队取得成功。"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__id
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__id
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__id
#: model:ir.model.fields,field_description:website.field_theme_website_menu__id
#: model:ir.model.fields,field_description:website.field_theme_website_page__id
#: model:ir.model.fields,field_description:website.field_website__id
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__id
#: model:ir.model.fields,field_description:website.field_website_controller_page__id
#: model:ir.model.fields,field_description:website.field_website_menu__id
#: model:ir.model.fields,field_description:website.field_website_page__id
#: model:ir.model.fields,field_description:website.field_website_rewrite__id
#: model:ir.model.fields,field_description:website.field_website_robots__id
#: model:ir.model.fields,field_description:website.field_website_route__id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__id
#: model:ir.model.fields,field_description:website.field_website_track__id
#: model:ir.model.fields,field_description:website.field_website_visitor__id
msgid "ID"
msgstr "ID"

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__xml_id
#: model:ir.model.fields,help:website.field_ir_cron__xml_id
msgid "ID of the action if defined in a XML file"
msgstr "在 XML 文件中定义的动作的 ID"

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__xml_id
#: model:ir.model.fields,help:website.field_website_page__xml_id
msgid "ID of the view defined in xml file"
msgstr "定义在 XML 文件里的视图ID"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__iap_page_code
msgid "Iap Page Code"
msgstr "Iap 网页代码"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__icon
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Icon"
msgstr "图标"

#. module: website
#: model:ir.model.fields,help:website.field_website__specific_user_account
msgid "If True, new accounts will be associated to the current website"
msgstr "如果为正确的，新帐户将与当前网站相关联"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_sequence
msgid "If set, a website menu will be created for the feature."
msgstr "如果设置，将为该功能创建一个网站菜单。"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__menu_company
msgid ""
"If set, add the menu as a second level menu, as a child of \"Company\" menu."
msgstr "如果设置，则将该菜单添加为二级菜单，作为“公司”菜单的子级。"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__social_default_image
#: model:ir.model.fields,help:website.field_website__social_default_image
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "If set, replaces the website logo as the default social share image."
msgstr "如果设置，则将网站徽标替换为默认社交共享图像。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"If this error is caused by a change of yours in the templates, you have the "
"possibility to reset the template to its <strong>factory settings</strong>."
msgstr "如果此错误是由模板中的更改引起的，则可以将模板重置为<strong>出厂设置</strong>。"

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__groups_id
#: model:ir.model.fields,help:website.field_website_page__groups_id
msgid ""
"If this field is empty, the view applies to all users. Otherwise, the view "
"applies to the users of those groups only."
msgstr "如果这字段是空，视图应用到所有用户。否则这些视图只能用于这些群组内的用户。"

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__active
#: model:ir.model.fields,help:website.field_website_page__active
msgid ""
"If this view is inherited,\n"
"* if True, the view always extends its parent\n"
"* if False, the view currently does not extend its parent but can be enabled\n"
"         "
msgstr ""
"如果继承此视图，\n"
"* 如果为 True，则视图始终扩展其父视图\n"
"* 如果为 False，则视图当前不扩展其父视图，但可以启用\n"
"         "

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr "如果您放弃当前的编辑，所有未保存的更改都将丢失。您可以取消以返回编辑模式。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"If you need to add analytics or marketing tags, inject code in your <head> "
"or <body> instead."
msgstr "如果你需要添加分析或营销标签，在你的 <head> 或 <body> 中注入代码."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
#, python-format
msgid ""
"If you reset this file, all your customizations will be lost as it will be "
"reverted to the default file."
msgstr "如果重置该文件，您的所有自定义设置都将丢失，因为它将恢复为默认文件。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_image_gallery/options.js:0
#: model:ir.model.fields,field_description:website.field_website_visitor__partner_image
#: model_terms:ir.ui.view,arch_db:website.grid_layout_options
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Image"
msgstr "图像"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Image - Text"
msgstr "图像 - 文本"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Image Cover"
msgstr "图像封面"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Image Gallery"
msgstr "图库"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#, python-format
msgid "Image Gallery Dialog"
msgstr "图像库对话框"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Image Menu"
msgstr "图像菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Image Size"
msgstr "图像大小"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Image Text Image"
msgstr "图像文本图像"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/fields.js:0
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#, python-format
msgid "Images"
msgstr "图像"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Images Spacing"
msgstr "图像间距"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Images Subtitles"
msgstr "图像字幕"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Images Wall"
msgstr "照片墙"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "In"
msgstr "在"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
msgid "In Main Menu"
msgstr "在主菜单中"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "In Place"
msgstr "适当"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "In the meantime we invite you to visit our"
msgstr "与此同时，我们邀请您访问我们的"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
#, python-format
msgid ""
"In this mode, you can only translate texts. To change the structure of the page, you must edit the master page.\n"
"            Each modification on the master page is automatically applied to all translated versions."
msgstr ""
"在这种模式下，您只能翻译文本。要改变网页的结构，您必须编辑主网页。\n"
"            主网页上的每个修改都会自动应用于所有的翻译版本。"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__include
msgid "Include"
msgstr "包括"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Indexed"
msgstr "索引"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Indicators"
msgstr "指标"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Info"
msgstr "信息"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Info Page"
msgstr "网页信息"

#. module: website
#: model:website.configurator.feature,description:website.feature_page_about_us
msgid "Info and stats about your company"
msgstr "关于贵公司的信息和统计数据"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Information about the"
msgstr "关于"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__inherit_id
msgid "Inherit"
msgstr "继承"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__inherit_id
#: model:ir.model.fields,field_description:website.field_website_page__inherit_id
msgid "Inherited View"
msgstr "继承的视图"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
#, python-format
msgid "Inject code in header or body"
msgstr "在页眉或正文中注入代码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Inline"
msgstr "内联"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Inner"
msgstr "内部"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Inner content"
msgstr "内部内容"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Aligned"
msgstr "输入对齐"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Input Fields"
msgstr "输入字段"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Input Type"
msgstr "输入类型"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a badge snippet"
msgstr "插入徽章小标签"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a blockquote snippet"
msgstr "插入小段引用文字"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a card snippet"
msgstr "插入卡片文字区块"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a chart snippet"
msgstr "插入统计图表区块"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a progress bar snippet"
msgstr "插入进度条片段"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a rating snippet"
msgstr "插入评分区块"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a share snippet"
msgstr "插入分享 区块"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert a text Highlight snippet"
msgstr "插入高亮文本区块"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert an alert snippet"
msgstr "插入提醒区块"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Insert an horizontal separator snippet"
msgstr "插入水平分割线区块"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid ""
"Insert text styles like headers, bold, italic, lists, and fonts with\n"
"                            a simple WYSIWYG editor. Flexible and easy to use."
msgstr ""
"使用简单的所见即所得编辑器插入标题、粗体、斜体、\n"
"列表和字体等文本样式。灵活且易于使用。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Inset"
msgstr "插图"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_instagram_page/000.js:0
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#: model_terms:ir.ui.view,arch_db:website.header_social_links
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_social_media
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#, python-format
msgid "Instagram"
msgstr "Instagram"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_instagram
msgid "Instagram Account"
msgstr "Instagram 账号"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_instagram_page_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Instagram Page"
msgstr "Instagram 页面"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Install"
msgstr "安装"

#. module: website
#: model:ir.model,name:website.model_base_language_install
msgid "Install Language"
msgstr "安装语言"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Install languages"
msgstr "安装语言"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Applications"
msgstr "已安装的应用程序"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Installed Localizations / Account Charts"
msgstr "安装的本地化/科目表"

#. module: website
#: model:ir.model.fields,help:website.field_website__theme_id
msgid "Installed theme"
msgstr "已安装的主题"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Installing \"%s\""
msgstr "正在安装 \"%s\""

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Installing your %s."
msgstr "正在安装您的%s。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
#, python-format
msgid "Installing your features"
msgstr "正在安装您的功能"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Integrating your %s."
msgstr "正在整合您的%s。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Intensity"
msgstr "强度"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Interaction History<br/>(optional)"
msgstr "交互历史<br/>(可选)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid "Intuitive Touch Controls"
msgstr "直观的触摸控制"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Intuitive system"
msgstr "直观的系统"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Invalid API Key. The following error was returned by Google:"
msgstr "API 密钥无效。 Google 返回以下错误："

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Iris Joe, CFO"
msgstr "Iris Joe，首席财务官"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Iris, with her international experience, helps us easily understand the "
"numbers and improves them. She is determined to drive success and delivers "
"her professional acumen to bring the company to the next level."
msgstr "Iris 凭借她的国际经验，帮助我们轻松理解这些数字并加以改进。她决心推动成功，并以她的专业敏锐度将公司提升到一个新的水平。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Is In Main Menu"
msgstr "在主菜单中"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__is_in_menu
msgid "Is In Menu"
msgstr "在菜单中"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__website_indexed
msgid "Is Indexed"
msgstr "已索引"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__is_installed_on_current_website
msgid "Is Installed On Current Website"
msgstr "已安装在当前网站上"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_mega_menu
msgid "Is Mega Menu"
msgstr "是大幅网页菜单"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__is_published
#: model:ir.model.fields,field_description:website.field_res_users__is_published
#: model:ir.model.fields,field_description:website.field_theme_website_page__is_published
#: model:ir.model.fields,field_description:website.field_website_controller_page__is_published
#: model:ir.model.fields,field_description:website.field_website_page__is_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__is_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__is_published
msgid "Is Published"
msgstr "已发布"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__is_visible
#: model:ir.model.fields,field_description:website.field_website_page__is_visible
msgid "Is Visible"
msgstr "可见"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after"
msgstr "在之后"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is after or equal to"
msgstr "在之后或等于"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before"
msgstr "在之前"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is before or equal to"
msgstr "在之前或等于"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is between (included)"
msgstr "在之间(包括)"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__is_connected
msgid "Is connected?"
msgstr "已连接？"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is equal to"
msgstr "等于"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than"
msgstr "大于"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is greater than or equal to"
msgstr "大于或等于"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than"
msgstr "小于"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is less than or equal to"
msgstr "小于或等于"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not between (excluded)"
msgstr "不在之间(排除)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not equal to"
msgstr "不等于"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is not set"
msgstr "未设置"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Is set"
msgstr "已设置"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid ""
"It appears your website is still using the old color system of\n"
"            Odoo 13.0 in some places. We made sure it is still working but\n"
"            we recommend you to try to use the new color system, which is\n"
"            still customizable."
msgstr ""
"您的网站在某些地方似乎仍在使用旧的\n"
" ERP 13.0 颜色系统。我们确保它仍然有效，\n"
"但我们建议您尝试使用新的颜色系统，该系统仍可自定义."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "It looks like your file is being called by"
msgstr "似乎以下对象调用了您的文件"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_block_2nd
msgid ""
"It's time to elevate your fitness journey with coaching that's as unique as "
"you are. Choose your path, embrace the guidance, and transform your life."
msgstr "是时候通过与你一样独特的指导来提升你的健身之旅了。选择你的道路，接受指导，改变你的生活。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Item"
msgstr "项目"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Items"
msgstr "项目"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
#, python-format
msgid "JS file: %s"
msgstr "JS 文件：%s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jacket"
msgstr "夹克"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Jeans"
msgstr "牛仔裤"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Job Position"
msgstr "工作岗位"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Join us and make your company a better place."
msgstr "加入我们，让您的公司能够名列前茅。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action_menu
msgid ""
"Join us for a remarkable dining experience that blends exquisite flavors "
"with a warm ambiance."
msgstr "请与我们一起，体验精致美味与温馨氛围相融合的非凡用餐体验。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Keep empty to use default value"
msgstr "留空以使用默认值"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__key
#: model:ir.model.fields,field_description:website.field_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_product_document__key
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__key
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__key
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__key
#: model:ir.model.fields,field_description:website.field_website_controller_page__key
#: model:ir.model.fields,field_description:website.field_website_page__key
msgid "Key"
msgstr "键"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Keyboards"
msgstr "键盘"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Keyword"
msgstr "关键字"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Keywords"
msgstr "关键词"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Label"
msgstr "标签"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_label
msgid "Label for form action"
msgstr "表单动作标注"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Labels Width"
msgstr "标签宽度"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
msgid "Landing Pages"
msgstr "着陆页面"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__lang_id
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Language"
msgstr "语言"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Language Selector"
msgstr "语言选择器"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__lang_id
msgid "Language from the website when visitor has been created"
msgstr "创建访问者时网站上的语言"

#. module: website
#: model:ir.model,name:website.model_res_lang
#: model:ir.model.fields,field_description:website.field_res_config_settings__language_ids
#: model:ir.model.fields,field_description:website.field_website__language_ids
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Languages"
msgstr "语言"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Languages available on your website"
msgstr "在您的网站上可以使用的语言"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Laptops"
msgstr "笔记本电脑"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Large"
msgstr "大"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last 7 Days"
msgstr "最近7天"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Last Action"
msgstr "最近动作"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_connection_datetime
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Last Connection"
msgstr "最后的连接"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Last Feature"
msgstr "最新特性"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Last Menu"
msgstr "最后的菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Last Page"
msgstr "最后一页"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_uid
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website__write_uid
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_uid
#: model:ir.model.fields,field_description:website.field_website_controller_page__write_uid
#: model:ir.model.fields,field_description:website.field_website_menu__write_uid
#: model:ir.model.fields,field_description:website.field_website_page__write_uid
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_uid
#: model:ir.model.fields,field_description:website.field_website_robots__write_uid
#: model:ir.model.fields,field_description:website.field_website_route__write_uid
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_uid
#: model:ir.model.fields,field_description:website.field_website_visitor__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__write_date
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_theme_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website__write_date
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__write_date
#: model:ir.model.fields,field_description:website.field_website_controller_page__write_date
#: model:ir.model.fields,field_description:website.field_website_menu__write_date
#: model:ir.model.fields,field_description:website.field_website_page__write_date
#: model:ir.model.fields,field_description:website.field_website_rewrite__write_date
#: model:ir.model.fields,field_description:website.field_website_robots__write_date
#: model:ir.model.fields,field_description:website.field_website_route__write_date
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__write_date
#: model:ir.model.fields,field_description:website.field_website_visitor__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__last_visited_page_id
msgid "Last Visited Page"
msgstr "上次访问的网页"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__time_since_last_action
msgid "Last action"
msgstr "最后的动作"

#. module: website
#. odoo-python
#: code:addons/website/controllers/main.py:0
#, python-format
msgid "Last modified pages"
msgstr "最后修改的网页"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__last_connection_datetime
msgid "Last page view date"
msgstr "最近浏览网页日期"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Latests news and case studies"
msgstr "最新消息和案例研究"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_website_controller_page_listing_layout
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Layout"
msgstr "布局"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background"
msgstr "前景色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Layout Background Color"
msgstr "前景背景颜色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Lead text"
msgstr "线索文本"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Learn more"
msgstr "了解更多"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Left"
msgstr "左"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Left Menu"
msgstr "左菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Legal"
msgstr "法律"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Legal Notice"
msgstr "法律声明"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Legend"
msgstr "图例"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Let your customers follow <br/>and understand your process."
msgstr "让您的客户遵循<br/>并理解您的流程。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "允许您的客户登录来查看他们的文档"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action_about
msgid ""
"Let's collaborate to create innovative solutions that stand out in the "
"digital landscape. Reach out today and let's build something extraordinary "
"together."
msgstr "让我们携手合作，共同打造在数字领域脱颖而出的创新解决方案。今天就联系我们，让我们一起创造非凡。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Let's do it"
msgstr "让我们开始吧"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Let's go!"
msgstr "我们走吧！"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action_digital
msgid ""
"Let's turn your vision into reality. Contact us today to set your brand on "
"the path to digital excellence with us."
msgstr "让我们将您的愿景变为现实。现在就联系我们，与我们一起将您的品牌推向数字卓越之路。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Library"
msgstr "库"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#, python-format
msgid "Light"
msgstr "明亮"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Light &amp; Dark"
msgstr "浅色 &amp; 深色"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__limit
msgid "Limit"
msgstr "上限"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Limited customization"
msgstr "可使用部分定制功能"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
msgid "Line"
msgstr "行"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Line Color"
msgstr "线条颜色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Line height"
msgstr "行高"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
#, python-format
msgid "Link"
msgstr "链接"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Link Anchor"
msgstr "链接锚"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Link Style"
msgstr "链接样式"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Link button"
msgstr "链接按钮"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Link text"
msgstr "链接文本"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.header_social_links
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.s_social_media
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#, python-format
msgid "LinkedIn"
msgstr "领英"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_linkedin
msgid "LinkedIn Account"
msgstr "领英账号"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Linkedin"
msgstr "领英"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links"
msgstr "链接"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Links Style"
msgstr "链接样式"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Links to other Websites"
msgstr "与其他网站的链接"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_controller_page__default_layout__list
#: model_terms:ir.ui.view,arch_db:website.s_website_controller_page_listing_layout
msgid "List"
msgstr "列表"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "List-group"
msgstr "列表组"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_controller_page__page_type__listing
msgid "Listing"
msgstr "列表"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Little Icons"
msgstr "小图标"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_live_chat
msgid "Live Chat"
msgstr "在线聊天"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Live Preview"
msgstr "实时预览"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Livechat"
msgstr "在线客服"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "Livechat Widget"
msgstr "实时聊天小部件"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: code:addons/website/static/src/xml/web_editor.xml:0
#: code:addons/website/static/src/xml/web_editor.xml:0
#: code:addons/website/static/src/xml/website.background.video.xml:0
#, python-format
msgid "Loading..."
msgstr "正在加载... 稍安勿躁"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Logo"
msgstr "标识"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Logo of MyCompany"
msgstr "Logo of MyCompany"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Logos"
msgstr "Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Long Text"
msgstr "长文本"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Low"
msgstr "低"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Main"
msgstr "主要"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Main Course"
msgstr "主菜单"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__menu_id
msgid "Main Menu"
msgstr "主菜单"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#, python-format
msgid "Main actions"
msgstr "主要动作"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Main page of your website served to visitors"
msgstr "为访客提供的网站主页"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Make it easy for clients to book appointments with you."
msgstr "让客户轻松与您预约。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure billing is enabled"
msgstr "确保已启用计费"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid ""
"Make sure to wait if errors keep being shown: sometimes enabling an API "
"allows to use it immediately but Google keeps triggering errors for a while"
msgstr "如果错误不断显示，请稍等：有时启用 API 可以立即使用它，但 Google 会持续触发错误一段时间"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Make sure your settings are properly configured:"
msgstr "确保您的设置配置正确："

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid "Management"
msgstr "管理"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Map"
msgstr "地图"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps JavaScript API"
msgstr "地图 JavaScript API"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Maps Static API"
msgstr "地图静态API"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Margins"
msgstr "毛利"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Mark Text"
msgstr "标记文本"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Marked Fields"
msgstr "标记的字段"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Marker style"
msgstr "标记样式"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_features
msgid "Marketing"
msgstr "营销"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Marketplace"
msgstr "市场"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Masonry"
msgstr "石工"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid ""
"Mastering frontend craftsmanship with expertise in HTML, CSS, and JavaScript"
" to craft captivating and responsive user experiences."
msgstr "掌握 HTML、CSS 和 JavaScript 方面的专业知识，精通前端工艺，打造迷人的响应式用户体验。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Max # of files"
msgstr "文件数目上限"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Max Axis"
msgstr "最大中枢"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Max file size"
msgstr "文件大小上限"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Measurement ID"
msgstr "计量单位ID"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Media"
msgstr "媒介"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Media List"
msgstr "媒体列表"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Media heading"
msgstr "媒体标题"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/content/snippets.animation.js:0
#, python-format
msgid "Media video"
msgstr "媒体视频"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns_menu
msgid "Mediterranean buffet of starters, main dishes and desserts"
msgstr "由开胃菜、主菜和甜点组成的地中海自助餐"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Medium"
msgstr "媒介"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_full_1_s_text_block_h2
msgid "Meet The Team"
msgstr "认识团队"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Mega Menu"
msgstr "大幅网页菜单"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__mega_menu_classes
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_classes
msgid "Mega Menu Classes"
msgstr "大幅网页菜单群组定义"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__mega_menu_content
#: model:ir.model.fields,field_description:website.field_website_menu__mega_menu_content
msgid "Mega Menu Content"
msgstr "大幅网页菜单内容"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Mega menu default image"
msgstr "超级菜单默认图像"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Men"
msgstr "男士"

#. module: website
#: model:ir.model,name:website.model_ir_ui_menu
#: model:ir.model.fields,field_description:website.field_website_menu__name
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Menu"
msgstr "菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Menu - Sales 1"
msgstr "菜单 - 销售 1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Menu - Sales 2"
msgstr "菜单 - 销售 2"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Menu - Sales 3"
msgstr "菜单 - 销售 3"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Menu - Sales 4"
msgstr "菜单 - 销售 4"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_company
msgid "Menu Company"
msgstr "菜单公司"

#. module: website
#: model:ir.ui.menu,name:website.menu_edit_menu
msgid "Menu Editor"
msgstr "菜单编辑器"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Menu Item %s"
msgstr "菜单项目 %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns_menu
msgid "Menu One"
msgstr "菜单一"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__menu_sequence
msgid "Menu Sequence"
msgstr "菜单序号"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns_menu
msgid "Menu Two"
msgstr "菜单二"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__copy_ids
msgid "Menu using a copy of me"
msgstr "菜单使用我的副本"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Menu with Search bar"
msgstr "带搜索栏的菜单"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_menu_list
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid "Menus"
msgstr "菜单"

#. module: website
#: model:ir.model,name:website.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "合并业务伙伴向导"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Messages"
msgstr "消息"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Metadata"
msgstr "元数据"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_0_s_three_columns
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_media_list
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_text_image
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Mich Stark, COO"
msgstr "Mich Stark，首席运营官"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_text_image
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today."
msgstr "Mich 喜欢接受挑战。凭借在软件行业担任多年商务总监的经验，Mich 帮助公司取得了今天的成就。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_0_s_three_columns
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_media_list
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid ""
"Mich loves taking on challenges. With his multi-year experience as "
"Commercial Director in the software industry, Mich has helped the company to"
" get where it is today. Mich is among the best minds."
msgstr "Mich 喜欢接受挑战。凭借在软件行业担任商务总监的多年经验，Mich 帮助公司取得了今天的成就。米奇是最聪明的人之一。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_two
msgid "Middle"
msgstr "中间"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Min Axis"
msgstr "最大中枢"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Min-Height"
msgstr "最低高度"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Minimalist"
msgstr "极简"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#: model_terms:ir.ui.view,arch_db:website.s_countdown
#, python-format
msgid "Minutes"
msgstr "分钟"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Mirror Blur"
msgstr "镜面模糊"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#: model:ir.model.fields,field_description:website.field_website_visitor__mobile
#, python-format
msgid "Mobile"
msgstr "手机"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Mobile Alignment"
msgstr "移动设备对齐方式"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Mobile Preview"
msgstr "移动预览"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/mobile_preview.xml:0
#: code:addons/website/static/src/systray_items/mobile_preview.xml:0
#, python-format
msgid "Mobile preview"
msgstr "移动端预览"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Modal"
msgstr "模态"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Modal title"
msgstr "模态标题"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__mode
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Mode"
msgstr "模式"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__model
#: model:ir.model.fields,field_description:website.field_website_page__model
msgid "Model"
msgstr "模型"

#. module: website
#: model:ir.model,name:website.model_ir_model_data
#: model:ir.model.fields,field_description:website.field_website_controller_page__model_data_id
#: model:ir.model.fields,field_description:website.field_website_page__model_data_id
msgid "Model Data"
msgstr "模型数据"

#. module: website
#: model:ir.model,name:website.model_website_controller_page
msgid "Model Page"
msgstr "模型页面"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_controller_pages_list
msgid "Model Pages"
msgstr "模型页面"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__model_name
msgid "Model name"
msgstr "模型名称"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__model_id
#: model:ir.model.fields,field_description:website.field_website_page__model_id
msgid "Model of the view"
msgstr "视图模型"

#. module: website
#: model:ir.model,name:website.model_ir_model
msgid "Models"
msgstr "模型"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__arch_updated
#: model:ir.model.fields,field_description:website.field_website_page__arch_updated
msgid "Modified Architecture"
msgstr "修改的结构"

#. module: website
#: model:ir.model,name:website.model_ir_module_module
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__module_id
msgid "Module"
msgstr "模块"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__module_marketing_automation
msgid "Module Marketing Automation"
msgstr "营销自动化模块"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__module_website_livechat
msgid "Module Website Livechat"
msgstr "网站实时聊天模块"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Monitor Google Search results data"
msgstr "监控Google搜索结果数据"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid ""
"Monitor your visitors while they are browsing your website with the Odoo "
"Social app. Engage with them in just a click using a live chat request or a "
"push notification. If they have completed one of your forms, you can send "
"them an SMS, or call them right away while they are browsing your website."
msgstr ""
"在访客使用 ERP "
"社交应用程序浏览您的网站时监控他们。使用在线聊天请求或推送通知，只需点击一下即可与他们互动。如果他们填写了您的一份表格，您可以向他们发送短消息，或在他们浏览您的网站时立即致电给他们."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Monitors"
msgstr "显示器"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_color_blocks_2
msgid "More Details"
msgstr "更多细节"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid ""
"More than 90 shapes exist and their colors are picked to match your Theme."
msgstr "存在 90 多种形状，它们的颜色被挑选出来以匹配您的主题。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Mosaic"
msgstr "马赛克"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Most searched topics related to your keyword, ordered by importance"
msgstr "与您的关键字有关的大多数搜索主题，按重要性排序"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Mouse"
msgstr "老鼠"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Backward"
msgstr "后退"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move Forward"
msgstr "前进"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to first"
msgstr "移到第一个"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to last"
msgstr "移到最后一个"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to next"
msgstr "移到下一个"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Move to previous"
msgstr "移到前一个"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Multi Menus"
msgstr "多菜单"

#. module: website
#: model:ir.model,name:website.model_website_multi_mixin
msgid "Multi Website Mixin"
msgstr "多网站的Mixin"

#. module: website
#: model:ir.model,name:website.model_website_published_multi_mixin
msgid "Multi Website Published Mixin"
msgstr "多网站发布的Mixin"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__group_multi_website
#: model:res.groups,name:website.group_multi_website
msgid "Multi-website"
msgstr "多网站"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Multimedia"
msgstr "多媒体"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Multiple Checkboxes"
msgstr "多个复选框"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/view_hierarchy.xml:0
#, python-format
msgid "Multiple tree exists for this view"
msgstr "此视图存在多个树"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid "My Company"
msgstr "我的公司"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid "My Skills"
msgstr "我的技能"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_header_brand_name
msgid "My Website"
msgstr "我的网站"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "MyCompany"
msgstr "MyCompany"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__name
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__name
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__name
#: model:ir.model.fields,field_description:website.field_theme_website_menu__name
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__name
#: model:ir.model.fields,field_description:website.field_website_controller_page__page_name
#: model:ir.model.fields,field_description:website.field_website_rewrite__name
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__name
#: model:ir.model.fields,field_description:website.field_website_visitor__name
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Name"
msgstr "名称"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Name (A-Z)"
msgstr "名称 (A-Z)"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/hierarchy_navbar.xml:0
#, python-format
msgid "Name, id or key"
msgstr "名称, id 或者 键值"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Narrow"
msgstr "变窄"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Nav and tabs"
msgstr "导航和标签"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Navbar"
msgstr "导航条"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Need to pick up your order at one of our stores? Discover the nearest to "
"you."
msgstr "需要到我们的商店取货吗?发现离您最近的地方。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Network Advertising Initiative opt-out page"
msgstr "网络广告倡议选择退出网页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Networks"
msgstr "网络"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.neutralize_ribbon
msgid "Neutralized"
msgstr "中和"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.xml:0
#, python-format
msgid "New"
msgstr "新建"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#: code:addons/website/static/src/components/dialog/add_page_dialog.js:0
#, python-format
msgid "New Page"
msgstr "新网页"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__new_window
#: model:ir.model.fields,field_description:website.field_website_menu__new_window
msgid "New Window"
msgstr "新窗口"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "New collection"
msgstr "新款"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "New customer"
msgstr "新客户"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_news
msgid "News"
msgstr "新闻"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter"
msgstr "新闻信"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Newsletter Popup"
msgstr "新闻信弹出窗口"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#, python-format
msgid "Next"
msgstr "下一页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.step_wizard
msgid "Next:"
msgstr "下一步："

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "No Slide Effect"
msgstr "无幻灯片效果"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "No Underline"
msgstr "不加底线"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/widget_iframe.xml:0
#, python-format
msgid "No Url"
msgstr "无 Url"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid "No Visitors yet!"
msgstr "还没有访客！"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "No condition"
msgstr "没有条件"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No customization"
msgstr "无定制功能"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "No matching record!"
msgstr "没有找到匹配记录！"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_page_action
msgid "No page views yet for this visitor"
msgstr "此访客尚无网页浏览量"

#. module: website
#: model_terms:ir.actions.act_window,help:website.visitor_partner_action
msgid "No partner linked for this visitor"
msgstr "没有合作伙伴链接此访问者"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#, python-format
msgid "No results found for '"
msgstr "没有找到  '"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/000.xml:0
#, python-format
msgid "No results found. Please try another search."
msgstr "没有找到记录。请尝试另外的关键字。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "No support"
msgstr "不支持"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/website_switcher.js:0
#, python-format
msgid "No website domain configured for this website."
msgstr "此网站为配置网站域名。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.column_count_option
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "None"
msgstr "无"

#. module: website
#: model:website,prevent_zero_price_sale_text:website.default_website
#: model:website,prevent_zero_price_sale_text:website.website2
msgid "Not Available For Sale"
msgstr "不可用于销售"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
msgid "Not Published"
msgstr "未发布"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid ""
"Not only can you search for royalty-free illustrations, their colors are "
"also converted so that they always fit your Theme."
msgstr "您不仅可以搜索免版税插图，还可以转换它们的颜色，以便它们始终适合您的主题。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not published"
msgstr "未发布"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Not tracked"
msgstr "为跟踪"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Note that some third-party services may install additional cookies on your "
"browser in order to identify you."
msgstr "请注意，某些第三方服务可能会在您的浏览器上安装额外的 cookie 以识别您的身份。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
#, python-format
msgid ""
"Note: To embed code in this specific page, use the \"Embed Code\" building "
"block"
msgstr "注：要在此特定页面嵌入代码，请使用 \"嵌入代码 \"构建模块"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid ""
"Note: To hide this page, uncheck it from the Customize tab in edit mode."
msgstr "注意：要隐藏此网页，请在编辑模式下从定制页卡中取消选中它。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Nothing"
msgstr "没有什么"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Number"
msgstr "数量"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_language_count
#: model:ir.model.fields,field_description:website.field_website__language_count
msgid "Number of languages"
msgstr "语言数量"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Numbers"
msgstr "数字"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_three_columns
msgid "Nutritional Guidance"
msgstr "营养指导"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "OR"
msgstr "或"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Odoo Information"
msgstr "Odoo 信息"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Odoo Menu"
msgstr "Odoo 菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Odoo Version"
msgstr "Odoo版本"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Offer online courses and learning opportunities."
msgstr "提供在线课程和学习机会。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office audio"
msgstr "办公室音响"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Office screens"
msgstr "办公室屏幕"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Offline"
msgstr "离线"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Offset (X, Y)"
msgstr "偏移量(X，Y)"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/dialog.js:0
#: code:addons/website/static/src/components/dialog/page_properties.js:0
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#: code:addons/website/static/src/components/translator/translator.js:0
#, python-format
msgid "Ok"
msgstr "确定"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
#, python-format
msgid "Ok, never show me this again"
msgstr "好了, 别再给我看这个"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Appearance"
msgstr "外观上"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Click"
msgstr "点击"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "On Click (via link)"
msgstr "按下时（通过链接）"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Click Effect"
msgstr "按下时效果"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "On Exit"
msgstr "退出时"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Hover"
msgstr "悬停时"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "On Scroll"
msgstr "滚动时"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "On Success"
msgstr "成功"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr "应邀"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid "On-the-Go Charging"
msgstr "随身充电"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid ""
"Once the user closes the popup, it won't be shown again for that period of "
"time."
msgstr "一旦用户关闭弹出窗口，它就不会在这段时间内再次显示。"

#. module: website
#. odoo-python
#: code:addons/website/models/website_configurator_feature.py:0
#, python-format
msgid ""
"One and only one of the two fields 'page_view_id' and 'module_id' should be "
"set"
msgstr "应该设置两个字段“page_view_id”和“module_id”中的一个且仅一个"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Online"
msgstr "线上"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
#, python-format
msgid "Only Custom SCSS Files"
msgstr "仅自定义SCSS文件"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
#, python-format
msgid "Only Page SCSS Files"
msgstr "仅页面SCSS文件"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
#, python-format
msgid "Only Views"
msgstr "仅视图"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "Only allow essential cookies"
msgstr "只允许必要的cookies"

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__mode
#: model:ir.model.fields,help:website.field_website_page__mode
msgid ""
"Only applies if this view inherits from an other one (inherit_id is not False/Null).\n"
"\n"
"* if extension (default), if this view is requested the closest primary view\n"
"is looked up (via inherit_id), then all views inheriting from it with this\n"
"view's model are applied\n"
"* if primary, the closest primary view is fully resolved (even if it uses a\n"
"different model than this one), then this view's inheritance specs\n"
"(<xpath/>) are applied, and the result is used as if it were this view's\n"
"actual arch.\n"
msgstr ""
"仅适用于此视图继承于另一个视图的情况（inherit_id 不为 False/Null）。\n"
"\n"
"* 扩展（默认）情况下，如果请求此视图作为（通过inherit_id）\n"
"所视图的最近主视图时，则应用继承\n"
"此视图模型的所有视图\n"
"* 如果是主视图，且最接近的主视图已完全解析（即使它使用\n"
"与此视图不同的模型），则将应用此视图的继承规范\n"
"(<xpath/>)，并将结果用作该视图的\n"
"实际arch。\n"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website.cookies_bar
#, python-format
msgid "Only essentials"
msgstr "只有必需品"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Open Source ERP"
msgstr "开源ERP"

#. module: website
#: model:ir.actions.client,name:website.action_open_website_configurator
msgid "Open Website Configurator"
msgstr "打开网站配置器"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
#: model:ir.ui.menu,name:website.menu_optimize_seo
#, python-format
msgid "Optimize SEO"
msgstr "优化SEO"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 1"
msgstr "选项 1"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 2"
msgstr "选项 2"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option 3"
msgstr "选项 3"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Option List"
msgstr "选项列表"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Optional"
msgstr "可选的"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Order by"
msgstr "以…排序"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Order now"
msgstr "立即订购"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Other Information:"
msgstr "其他信息:"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#, python-format
msgid "Other social network"
msgstr "其他社交网络"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_text_image
msgid "Our Approach"
msgstr "我们的方法"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_0_s_three_columns
msgid ""
"Our Coaching combines personalized fitness plans with mindfulness practices,"
" ensuring you achieve harmony in your body and peace in your mind."
msgstr "我们的教练将个性化的健身计划与正念练习相结合，确保您的身体和谐，心灵平静。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Our Company"
msgstr "公司简介"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_full_s_text_image
msgid "Our Goals"
msgstr "我们的目标"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_pricing_5_s_text_block_h1
msgid "Our Menus"
msgstr "我们的菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_three_columns
msgid "Our Mission"
msgstr "我们的使命"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_3_s_text_block_h2
msgid "Our Offer"
msgstr "我们的报价"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_map_s_text_block_h2
msgid "Our Offices"
msgstr "我们的办事处"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "Our References"
msgstr "我们的参考"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_text_block_h2
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_block_h1
msgid "Our Services"
msgstr "我们的服务"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_full_s_image_text
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_mini_s_text_block_h2
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_three_columns
msgid "Our Story"
msgstr "我们的故事"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_picture
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_text_block_h1
msgid "Our Team"
msgstr "我们的团队"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_three_columns
msgid "Our Values"
msgstr "我们的价值观"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_three_columns
msgid ""
"Our experienced fitness coaches design workouts that align with your goals, "
"fitness level, and preferences."
msgstr "我们经验丰富的健身教练会根据您的目标、健身水平和喜好设计健身计划。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_three_columns
msgid ""
"Our mission is to create transformative experiences and foster growth, "
"driven by a relentless pursuit of innovation and a commitment to exceeding "
"expectations."
msgstr "我们的使命是在不懈追求创新和致力于超越期望的驱动下，创造变革性体验并促进增长。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_image_text_2nd
msgid ""
"Our seasoned consultants provide tailored guidance, leveraging their deep "
"industry knowledge to analyze your current strategies, identify "
"opportunities, and formulate data-driven recommendations."
msgstr "我们经验丰富的顾问利用其深厚的行业知识，为您提供量身定制的指导，分析您当前的战略，识别机遇，并制定以数据为导向的建议。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "Our seminars and trainings for you"
msgstr "我们为您举办研讨会和培训"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_pricing_s_text_block_2nd
msgid ""
"Our software plans are designed to cater to a variety of needs, ensuring "
"that you find the perfect fit for your requirements. From individual users "
"to businesses of all sizes, we offer pricing options that provide "
"exceptional value without compromising on features or performance."
msgstr ""
"我们的软件计划专为满足各种需求而设计，确保您能找到最适合自己要求的计划。从个人用户到各种规模的企业，我们都能提供超值的定价选项，而不会在功能或性能上打折扣。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Our team"
msgstr "我们的团队"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Our team will message you back as soon as possible."
msgstr "我们团队的客服工作人员将会尽快回信联系您。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_three_columns
msgid ""
"Our values shape our culture, influence our decisions, and guide us in "
"providing exceptional service. They reflect our dedication to integrity, "
"collaboration, and client satisfaction."
msgstr "我们的价值观塑造着我们的文化，影响着我们的决策，并指导我们提供卓越的服务。它们反映了我们对诚信、协作和客户满意度的执着追求。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Out"
msgstr "支出"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Outline"
msgstr "轮廓"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Outset"
msgstr "一开始"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Outstanding images"
msgstr "出色的图像"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Over The Content"
msgstr "内容之上"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Overlay"
msgstr "覆盖"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Padding"
msgstr "内边距"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Padding (Y, X)"
msgstr "填充 (Y, X)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Paddings"
msgstr "内边距"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.xml:0
#: model:ir.model,name:website.model_website_page
#: model:ir.model.fields,field_description:website.field_ir_ui_view__page_ids
#: model:ir.model.fields,field_description:website.field_theme_website_menu__page_id
#: model:ir.model.fields,field_description:website.field_website_controller_page__page_ids
#: model:ir.model.fields,field_description:website.field_website_page__page_ids
#: model:ir.model.fields,field_description:website.field_website_track__page_id
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#, python-format
msgid "Page"
msgstr "页"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/web_editor.xml:0
#, python-format
msgid "Page Anchor"
msgstr "网页锚点"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid "Page Details"
msgstr "页面详情"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__website_indexed
msgid "Page Indexed"
msgstr "网页已索引"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Layout"
msgstr "网页布局"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
#, python-format
msgid "Page Name"
msgstr "网页名称"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.js:0
#, python-format
msgid "Page Properties"
msgstr "网页属性"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_tree_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
#, python-format
msgid "Page Title"
msgstr "页面标题"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__page_type
msgid "Page Type"
msgstr "页面类型"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__url
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Page URL"
msgstr "网页 URL"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__page_view_id
msgid "Page View"
msgstr "网页预览"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_view_action
#: model:ir.model.fields,field_description:website.field_website_visitor__visitor_page_count
#: model:ir.ui.menu,name:website.menu_visitor_view_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Page Views"
msgstr "网页浏览"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitor_page_action
msgid "Page Views History"
msgstr "网页浏览历史记录"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Page Visibility"
msgstr "网页可见性"

#. module: website
#: model:ir.model.fields,help:website.field_website_configurator_feature__iap_page_code
msgid ""
"Page code used to tell IAP website_service for which page a snippet list "
"should be generated"
msgstr "网页代码用来告诉IAP website_service应该为哪个网页生成一个代码片段列表"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_page__copy_ids
msgid "Page using a copy of me"
msgstr "使用我的副本网页"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: model:ir.ui.menu,name:website.menu_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
#, python-format
msgid "Pages"
msgstr "页面"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Pagination"
msgstr "分页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Pants"
msgstr "裤子"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#, python-format
msgid "Paragraph"
msgstr "段落"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid ""
"Paragraph text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing "
"elit. <i>Integer posuere erat a ante</i>."
msgstr ""
"段落文本. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit. "
"<i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"Paragraph with <strong>bold</strong>, <span class=\"text-"
"muted\">muted</span> and <em>italic</em> texts"
msgstr "带<strong>粗体</strong>，<span class=\"text-muted\">柔和</span>和<em>斜体</em>的段落"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Paragraph."
msgstr "段落."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Parallax"
msgstr "视差"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__parent_id
msgid "Parent"
msgstr "上级"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_id
msgid "Parent Menu"
msgstr "上级菜单"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__parent_path
msgid "Parent Path"
msgstr "父级路径"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__partner_id
msgid "Partner of the last logged in user."
msgstr "最后登录用户的合作伙伴。"

#. module: website
#: model:ir.model.fields,help:website.field_website__partner_id
msgid "Partner-related data of the user"
msgstr "用户的业务伙伴相关数据"

#. module: website
#: model:ir.actions.act_window,name:website.visitor_partner_action
msgid "Partners"
msgstr "合作伙伴"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Password"
msgstr "密码"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__path
msgid "Path"
msgstr "路径"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pattern"
msgstr "模式"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Pay"
msgstr "支付"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_cover
msgid "Personalized Fitness"
msgstr "个性化健身"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_three_columns
msgid "Personalized Workouts"
msgstr "个性化锻炼"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
#, python-format
msgid "Phone Number"
msgstr "电话号码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Phones"
msgstr "电话"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#: model:ir.actions.act_window,name:website.theme_install_kanban_action
#, python-format
msgid "Pick a Theme"
msgstr "选择主题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Picture"
msgstr "图片"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Pie"
msgstr "派"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pills"
msgstr "药丸"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share
msgid "Pinterest"
msgstr "Pinterest"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Placeholder"
msgstr "占位符"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Places API"
msgstr "地点 API"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Plain"
msgstr "平面"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_plausible_shared_key
msgid "Plausible Analytics"
msgstr "Plausible分析"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__plausible_shared_key
msgid "Plausible Shared Key"
msgstr "Plausible分享密钥"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__plausible_site
msgid "Plausible Site"
msgstr "Plausible网站"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__plausible_site
msgid "Plausible Site (e.g. domain.com)"
msgstr "Plausible网站(例如 域名)"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__plausible_shared_key
msgid "Plausible auth Key"
msgstr "Plausible授权密钥"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Please fill in the form correctly."
msgstr "请正确填写表单。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid ""
"Please fill in the form correctly. The file \"%s\" is too big. (Maximum %s "
"MB)"
msgstr "请正确填写表格。文件 \"%s\" 过大。（最大%sMB）"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid ""
"Please fill in the form correctly. You uploaded too many files. (Maximum %s "
"files)"
msgstr "请正确填写表格。您上传的文件过多。（最多%s个文件）"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "Points of sale"
msgstr "销售点"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Popup"
msgstr "弹出"

#. module: website
#: model:ir.model,name:website.model_portal_wizard_user
msgid "Portal User Config"
msgstr "门户用户配置"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
msgid "Portfolio"
msgstr "文件夹"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Position"
msgstr "位置"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid "Post heading"
msgstr "后置标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Postcard"
msgstr "明信片"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Preferences<br/>(essential)"
msgstr "偏好<br/>(必要的)"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__prepend
msgid "Prepend"
msgstr "前缀"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Preset"
msgstr "预制"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fullscreen_indication/fullscreen_indication.js:0
#, python-format
msgid "Press %(key)s to exit full screen"
msgstr "按 %(key)s 离开全屏幕"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/view_hierarchy.xml:0
#, python-format
msgid "Press <Tab> for next ["
msgstr "按<Tab>键往下一个 ["

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Preview"
msgstr "预览"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Preview this URL in a new tab"
msgstr "在新标签页中预览此网页"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_dynamic_snippet_carousel/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: code:addons/website/static/src/snippets/s_image_gallery/000.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_carousel
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
#, python-format
msgid "Previous"
msgstr "上一页"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__arch_prev
#: model:ir.model.fields,field_description:website.field_website_page__arch_prev
msgid "Previous View Architecture"
msgstr "以前的视图架构"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Pricelist"
msgstr "价格表"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_pricing
#: model_terms:ir.ui.view,arch_db:website.pricing
msgid "Pricing"
msgstr "价格"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
#: model_terms:ir.ui.view,arch_db:website.new_page_template_pricing_s_text_block_h1
msgid "Pricing Plans"
msgstr "价格方案"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary"
msgstr "首要的"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Primary Style"
msgstr "主要样式"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Printers"
msgstr "打印机"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__priority
msgid "Priority"
msgstr "优先级"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Privacy"
msgstr "隐私"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#: model:website.configurator.feature,name:website.feature_page_privacy_policy
#: model_terms:ir.ui.view,arch_db:website.privacy_policy
#, python-format
msgid "Privacy Policy"
msgstr "‎隐私政策‎"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_add_product_widget
#, python-format
msgid "Product"
msgstr "产品"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Products"
msgstr "产品"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Professional"
msgstr "专业版"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "Professional themes"
msgstr "专业主题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_features
msgid ""
"Proficient in backend development, specializing in Python, Django, and "
"database management to create efficient and scalable solutions."
msgstr "精通后端开发，擅长 Python、Django 和数据库管理，以创建高效、可扩展的解决方案。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Profile"
msgstr "个人资料"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Progress Bar"
msgstr "进度条"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Color"
msgstr "进度条颜色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Style"
msgstr "进度条样式"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Progress Bar Weight"
msgstr "进度条权重"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_three_columns
msgid "Progress Tracking"
msgstr "进度跟踪"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "Progress bar"
msgstr "进度条"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Projectors"
msgstr "投影仪"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Promotions"
msgstr "促销"

#. module: website
#: model:ir.ui.menu,name:website.menu_page_properties
msgid "Properties"
msgstr "权益"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__partner_id
msgid "Public Partner"
msgstr "公众业务伙伴"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__user_id
msgid "Public User"
msgstr "公众用户"

#. module: website
#: model:res.groups,name:website.website_page_controller_expose
msgid "Public access to arbitrary exposed model"
msgstr "公开访问任意暴露的模型"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/publish_button.xml:0
#: code:addons/website/static/src/components/views/page_list.js:0
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
#, python-format
msgid "Publish"
msgstr "发布"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/page_list.js:0
#, python-format
msgid "Publish Website Content"
msgstr "发布网站内容"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_career
msgid "Publish job offers and let people apply"
msgstr "发布工作机会并让人们申请"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_event
msgid "Publish on-site and online events"
msgstr "发布现场和线上活动"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/publish_button.xml:0
#: code:addons/website/static/src/components/fields/redirect_field.js:0
#: code:addons/website/static/src/systray_items/publish.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
#, python-format
msgid "Published"
msgstr "已发布"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_page__date_publish
msgid "Publishing Date"
msgstr "发布日期"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Pulse"
msgstr "脉冲"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Purpose"
msgstr "目的"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
msgid "Put the focus on what you have to say!"
msgstr "把注意力放在您要说的话上！"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating
msgid "Quality"
msgstr "质量"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_pricing_s_text_block_h2
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_block_h2
msgid "Questions?"
msgstr "问题？"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Quotes"
msgstr "报价单"

#. module: website
#: model:ir.model,name:website.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: website
#: model:ir.model,name:website.model_ir_qweb_field_contact
msgid "Qweb Field Contact"
msgstr "Qweb字段联系人"

#. module: website
#: model:ir.model,name:website.model_ir_qweb_field_html
msgid "Qweb Field HTML"
msgstr "Qweb 字段 HTML"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Radar"
msgstr "雷达"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Radio Button List"
msgstr "单选按钮列表"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Radio Buttons"
msgstr "单选按钮"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Rating"
msgstr "点评"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Re-order"
msgstr "重新订购"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_call_to_action
msgid "Ready to Embrace Your Fitness Journey?"
msgstr "准备好迎接您的健身之旅？"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action_about
msgid "Ready to bring your digital vision to life?"
msgstr "准备好实现您的数字化愿景了吗？"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Ready to build the"
msgstr "准备构建"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_call_to_action_digital
msgid "Ready to embark on a journey of digital transformation?"
msgstr "准备好踏上数字化转型之旅了吗？"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_3_s_call_to_action
msgid ""
"Ready to embark on your auditory adventure? Order your EchoTunes Wireless "
"Earbuds today and let the symphony begin."
msgstr "准备好开始听觉探险了吗？今天就订购 EchoTunes 无线耳机，让交响乐开始吧。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_features
msgid "Rebranding"
msgstr "品牌重塑"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
#, python-format
msgid "Recipient Email"
msgstr "收件人电子邮件地址"

#. module: website
#: model:ir.model,name:website.model_ir_rule
msgid "Record Rule"
msgstr "记录规则"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Redirect"
msgstr "重定向"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.js:0
#, python-format
msgid "Redirecting..."
msgstr "正在重新导向⋯"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Redirection Type"
msgstr "重定向类型"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_rewrite
msgid "Redirects"
msgstr "重定向"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "References"
msgstr "参考"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Refresh route's list"
msgstr "刷新路由的列表"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Regular"
msgstr "常规"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Related Menu Items"
msgstr "相关菜单项"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__menu_ids
#: model:ir.model.fields,field_description:website.field_website_page__menu_ids
msgid "Related Menus"
msgstr "相关菜单"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__controller_page_id
msgid "Related Model Page"
msgstr "相关模型页面"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_menu__page_id
msgid "Related Page"
msgstr "相关网页"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Related keywords"
msgstr "相关的关键字"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Remember information about the preferred look or behavior of the website, "
"such as your preferred language or region."
msgstr "记住有关网站首选外观或行为的信息，例如您的首选语言或地区。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__remove
#, python-format
msgid "Remove"
msgstr "移除"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Row"
msgstr "删除行"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_chart/options.js:0
#, python-format
msgid "Remove Serie"
msgstr "删除系列"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Remove Slide"
msgstr "删除幻灯片"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Remove Tab"
msgstr "删除选项卡"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Remove all"
msgstr "删除所有"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Remove theme"
msgstr "删除主题"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_asset__directive__replace
msgid "Replace"
msgstr "替换"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Replace File"
msgstr "替换文件"

#. module: website
#: model:ir.ui.menu,name:website.menu_reporting
msgid "Reporting"
msgstr "报告"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Required"
msgstr "必填"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.xml:0
#, python-format
msgid "Reset"
msgstr "重置"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset templates"
msgstr "重置模板"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Reset to Headings Font Family"
msgstr "重设为标题字型"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Reset to Paragraph Font Family"
msgstr "重设为段落字型"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Reset to initial version (hard reset)."
msgstr "重置为初始版本（硬重置）。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
#, python-format
msgid "Reseting views is not supported yet"
msgstr "暂不支持重置视图"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "Resources"
msgstr "资源"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid "Respecting your privacy is our priority."
msgstr "尊重您的隐私是我们的首要任务。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Restore previous version (soft reset)."
msgstr "恢复以前的版本（软重置）。"

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_id
#: model:ir.model.fields,help:website.field_res_users__website_id
#: model:ir.model.fields,help:website.field_website_controller_page__website_id
#: model:ir.model.fields,help:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_page__website_id
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_id
msgid "Restrict publishing to this website."
msgstr "限制发布到本网站。"

#. module: website
#: model:res.groups,name:website.group_website_restricted_editor
msgid "Restricted Editor"
msgstr "受限编辑器"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__restricted_group
msgid "Restricted Group"
msgstr "限制用户组"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_rewrite_list
msgid "Rewrite"
msgstr "重写"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_embed_code_options
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse_options
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Right"
msgstr "右"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menu_image_menu
msgid "Right Menu"
msgstr "右菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Ripple"
msgstr "涟漪"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Road"
msgstr "路线"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "RoadMap"
msgstr "路线图"

#. module: website
#. odoo-python
#: code:addons/website/models/res_config_settings.py:0
#: model:ir.model.fields,field_description:website.field_website__robots_txt
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#, python-format
msgid "Robots.txt"
msgstr "Robots.txt"

#. module: website
#: model:ir.model,name:website.model_website_robots
msgid "Robots.txt Editor"
msgstr "Robots.txt 编辑器"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Robots.txt: This file tells to search engine crawlers which pages or files "
"they can or can't request from your site."
msgstr "Robots.txt：该文件告诉搜索引擎抓取工具，可以或无法从您的网站请求哪些页面或文件。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rotate"
msgstr "替换"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_widgets
msgid "Round Corners"
msgstr "圆角"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded"
msgstr "圆形"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Rounded Miniatures"
msgstr "圆形缩影"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Rounded box menu"
msgstr "圆框菜单"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__route_id
#: model:ir.model.fields,field_description:website.field_website_route__path
msgid "Route"
msgstr "路线"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
#, python-format
msgid "SCSS file: %s"
msgstr "SCSS 文件：%s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "SEO"
msgstr "SEO"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
msgid "SEO Optimized"
msgstr "SEO优化"

#. module: website
#: model:ir.model,name:website.model_website_seo_metadata
msgid "SEO metadata"
msgstr "SEO元数据"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_controller_page__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_page__is_seo_optimized
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO优化"

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "Sample %s"
msgstr "示例 %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Sample Icons"
msgstr "示例图标"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Satellite"
msgstr "卫星"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Saturation"
msgstr "饱和度"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.js:0
#: code:addons/website/static/src/components/dialog/seo.js:0
#: code:addons/website/static/src/components/edit_head_body_dialog/edit_head_body_dialog.xml:0
#: code:addons/website/static/src/components/resource_editor/resource_editor.xml:0
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
#, python-format
msgid "Save"
msgstr "保存"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Save & copy"
msgstr "保存和复制"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Save and Reload"
msgstr "保存并重新加载"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Save changes"
msgstr "保存更改"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Save the block to use it elsewhere"
msgstr "保存该构建块以在其他地方使用"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "Score"
msgstr "得分"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Screens"
msgstr "屏幕"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_module_module__image_ids
msgid "Screenshots"
msgstr "截图"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll"
msgstr "滚动"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Effect"
msgstr "转轴效果"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.option_footer_scrolltop
msgid "Scroll To Top"
msgstr "滚动到顶部"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Top Button"
msgstr "滚动到顶部按钮"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll Zone"
msgstr "滚动区"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Scroll down button"
msgstr "向下滚动按钮"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "Scroll down to next section"
msgstr "向下滚动到下一节"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/hierarchy_navbar.xml:0
#: code:addons/website/static/src/js/backend/view_hierarchy/hierarchy_navbar.xml:0
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.header_search_box
#: model_terms:ir.ui.view,arch_db:website.snippets
#: model_terms:ir.ui.view,arch_db:website.website_search_box
#, python-format
msgid "Search"
msgstr "搜索"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_search
msgid "Search Menus"
msgstr "搜索菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_rewrite_search
msgid "Search Redirect"
msgstr "搜索重定向"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Search Results"
msgstr "搜索结果"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Search Visitor"
msgstr "搜索访客"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid ""
"Search in the media dialogue when you need photos to illustrate your "
"website. Odoo's integration with Unsplash, featuring millions of royalty "
"free and high quality photos, makes it possible for you to get the perfect "
"picture, in just a few clicks."
msgstr ""
"当您需要照片来说明您的网站时，请在媒体对话中搜索。 ERP 与 Unsplash "
"集成，拥有数百万张免版税和高质量的图片，让您只需点击几下即可获得完美的照片."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "Search on our website"
msgstr "在我们的网站上搜索"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Search within"
msgstr "结果内搜索"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "Search..."
msgstr "搜索..."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Second Feature"
msgstr "第二功能"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Second Menu"
msgstr "第二个菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Second default radio"
msgstr "第二个默认无线电"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_showcase
msgid "Second feature"
msgstr "第二功能"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Second list of Features"
msgstr "第二功能列表"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary"
msgstr "次要的"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Secondary Style"
msgstr "次要风格"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.js:0
#: model_terms:ir.ui.view,arch_db:website.s_countdown
#, python-format
msgid "Seconds"
msgstr "秒"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
msgid "Section Subtitle"
msgstr "节子标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid "Secure and Comfortable Fit"
msgstr "安全舒适"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Select an image for social share"
msgstr "选择一个图像进行社交分享"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Select and delete blocks <br/>to remove some steps."
msgstr "选择并删除构建块<br/>可以删除某些步骤。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Select and delete blocks to remove features."
msgstr "选择并删除模块可以删除特性。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Select one font on"
msgstr "选择一种字体"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Selection"
msgstr "选择"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_cover
msgid "Sell Online. <br/>Easily."
msgstr "在线销售。<br/>轻而易举。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "Sell Online. <strong>Easily.</strong>"
msgstr "在线销售。<strong>轻而易举。</strong>"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_shop
msgid "Sell more with an eCommerce"
msgstr "通过电子商务销售更多产品"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Send Email"
msgstr "发送电邮"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Send us a message"
msgstr "给我们发信息"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__seo_name
#: model:ir.model.fields,field_description:website.field_website_controller_page__seo_name
#: model:ir.model.fields,field_description:website.field_website_page__seo_name
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__seo_name
msgid "Seo name"
msgstr "Seo 名称"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Separate email addresses with a comma."
msgstr "单独的电子邮件地址用逗号分隔。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Separated link"
msgstr "分离的链接"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Separator"
msgstr "分隔符"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__sequence
#: model:ir.model.fields,field_description:website.field_theme_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website__sequence
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__sequence
#: model:ir.model.fields,field_description:website.field_website_controller_page__priority
#: model:ir.model.fields,field_description:website.field_website_menu__sequence
#: model:ir.model.fields,field_description:website.field_website_page__priority
#: model:ir.model.fields,field_description:website.field_website_rewrite__sequence
msgid "Sequence"
msgstr "序列"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Serve font from Google servers"
msgstr "从谷歌服务器提供字体"

#. module: website
#: model:ir.model,name:website.model_ir_actions_server
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__action_server_id
msgid "Server Action"
msgstr "服务器动作"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_list
msgid "Service"
msgstr "服务"

#. module: website
#: model:website.configurator.feature,name:website.feature_page_our_services
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
#: model_terms:ir.ui.view,arch_db:website.our_services
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#: model_terms:ir.ui.view,arch_db:website.template_footer_minimalist
msgid "Services"
msgstr "服务"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "Session &amp; Security<br/>(essential)"
msgstr "会话和安全<br/>(必要的)"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Setting up your %s."
msgstr "正在设置您的%s。"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_configuration
#: model:ir.ui.menu,name:website.menu_website_website_settings
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website.website_pages_tree_view
msgid "Settings"
msgstr "设置"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Settings of Website"
msgstr "网站设置"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Settings on this page will apply to this website"
msgstr "此网页上的设置将应用于本网站"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Shadow"
msgstr "阴影"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Shadow large"
msgstr "阴影大"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Shadow medium"
msgstr "阴影媒介"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Shadow small"
msgstr "小阴影"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Shake"
msgstr "摇晃"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Share"
msgstr "分享"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_elearning
msgid "Share knowledge publicly or for a fee"
msgstr "公开或收费分享知识"

#. module: website
#: model:website.configurator.feature,description:website.feature_module_success_stories
msgid "Share your best case studies"
msgstr "分享您的最佳案例研究"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Share your thoughts and ideas with the world."
msgstr "与世界分享您的想法和创意。"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__shared_user_account
msgid "Shared Customer Accounts"
msgstr "分享的客户账户"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Shared Link Auth"
msgstr "共享链接授权"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Shoes"
msgstr "鞋"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_shop
msgid "Shop"
msgstr "商店"

#. module: website
#: model:ir.model.fields,help:website.field_website__auto_redirect_lang
msgid "Should users be redirected to their browser's language"
msgstr "是否应将用户重定向到他们的浏览器语言"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/view_hierarchy.xml:0
#, python-format
msgid "Show Arch Diff"
msgstr "显示架构差异"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__customize_show
#: model:ir.model.fields,field_description:website.field_website_page__customize_show
msgid "Show As Optional Inherit"
msgstr "作为可选继承显示"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show Header"
msgstr "显示标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show Message"
msgstr "显示消息"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and hide countdown"
msgstr "显示消息并隐藏倒计时"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Show Message and keep countdown"
msgstr "显示消息并保持倒计时"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Show in Top Menu"
msgstr "在顶部菜单中显示"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/hierarchy_navbar.xml:0
#, python-format
msgid "Show inactive views"
msgstr "显示非活动视图"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "Show on"
msgstr "显示在"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Show reCaptcha Policy"
msgstr "显示验证码政策"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Show site map"
msgstr "显示网站地图"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_popup/000.js:0
#, python-format
msgid "Show the cookies bar"
msgstr "显示 Cookies 通知栏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/Hide on Desktop"
msgstr "在桌面上显示/隐藏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/Hide on Mobile"
msgstr "在移动端显示/隐藏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide button"
msgstr "显示/隐藏按钮"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide language selector"
msgstr "显示/隐藏语言选择器"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide logo"
msgstr "显示/隐藏logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide search bar"
msgstr "显示/隐藏搜索栏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide sign in button"
msgstr "显示/隐藏登录按钮"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide social links"
msgstr "显示/隐藏社交链接"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Show/hide text element"
msgstr "显示/隐藏文本元素"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Showcase"
msgstr "产品展示"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sidebar"
msgstr "侧边栏"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps
msgid "Sign in"
msgstr "登录"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__connected
msgid "Signed In"
msgstr "已登录"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__website_controller_page__page_type__single
msgid "Single record"
msgstr "单一记录"

#. module: website
#: model:ir.ui.menu,name:website.menu_site
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Site"
msgstr "网站"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Sitemap"
msgstr "网站地图"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Sitemap.xml: Help search engine crawlers to find out what pages are present "
"and which have recently changed, and to crawl your site accordingly. This "
"file is automatically generated by Odoo."
msgstr ""
"站点地图 Sitemap.xml：帮助搜索引擎爬网程序查找存在哪些网页和最近更改过的网页，并相应地对您的网站进行爬网。此文件由 ERP 自动生成."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Size"
msgstr "尺寸"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Skip and start from scratch"
msgstr "跳过并从头开始"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid "Sleek and Modern Design"
msgstr "时尚现代的设计"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide"
msgstr "幻灯片"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Down"
msgstr "向下滑动"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slide Hover"
msgstr "滑动悬停"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Left"
msgstr "向左滑动"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Right"
msgstr "向右滑动"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Slide Title"
msgstr "幻灯片标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Slide Up"
msgstr "向上滑动"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Slideout Effect"
msgstr "滑出效果"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.dynamic_snippet_carousel_options_template
msgid "Slider Speed"
msgstr "滑动速度"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Slideshow"
msgstr "幻灯"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Small"
msgstr "小"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
msgid "Small Header"
msgstr "小标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid ""
"Small text. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit. "
"<i>Integer posuere erat a ante</i>."
msgstr ""
"小文本. Lorem <b>ipsum dolor sit amet</b>, consectetur adipiscing elit. "
"<i>Integer posuere erat a ante</i>."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Smartphones"
msgstr "智能手机"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_social_media
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Social Media"
msgstr "社交媒体"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_social_media_options
msgid "Social Networks"
msgstr "社交网络"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Social Preview"
msgstr "社会预览"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_border_line_widgets
msgid "Solid"
msgstr "实体"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"Some quick example text to build on the card title and make up the bulk of "
"the card's content."
msgstr "在卡片标题的基础上添加一些快速示例文字，以构成卡片的主要内容。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Something else here"
msgstr "这里还有别的"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "Something went wrong."
msgstr "发生了一些错误。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Sound"
msgstr "声音"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Spacing"
msgstr "间距"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.grid_layout_options
msgid "Spacing (Y, X)"
msgstr "间距（Y、X）"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Speakers from all over the world will join our experts to give inspiring "
"talks on various topics. Stay on top of the latest business management "
"trends &amp; technologies"
msgstr "‎来自全世界的演讲者将加入我们的专家，就各种主题进行鼓舞人心的演讲。随时了解最新的业务管理趋势技术‎"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__specific_user_account
msgid "Specific User Account"
msgstr "具体用户账号"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
msgid "Specify a search term."
msgstr "指定搜索词。"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_default_field_id
msgid ""
"Specify the field which will contain meta and custom form fields datas."
msgstr "指定包含元数据和自定义表单字段数据的字段。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Speed"
msgstr "速度"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_shadow_widgets
msgid "Spread"
msgstr "扩展"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Spring collection has arrived!"
msgstr "春季系列到货了！"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Square"
msgstr "方形"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
msgid "Squared Miniatures"
msgstr "平方缩影"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Stacked"
msgstr "堆叠"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_gallery_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Standard"
msgstr "标准"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Start After"
msgstr "开始于"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
msgid "Start Button"
msgstr "开始按钮"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/views/theme_preview.xml:0
#, python-format
msgid "Start Now"
msgstr "现在开始"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid "Start Now <span class=\"fa fa-angle-right ms-2\"/>"
msgstr "立即开始<span class=\"fa fa-angle-right ms-2\"/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Start now"
msgstr "立即使用"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Start selling your products and services today."
msgstr "今天开始销售您的产品和服务。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_tabs
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid "Start with the customer – find out what they want and give it to them."
msgstr "从客户开始 - 找出他们想要的东西并将其给他们。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Start your journey"
msgstr "开始您的旅程"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Starter"
msgstr "入门"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Status Colors"
msgstr "状态颜色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid ""
"Stay informed of our latest news and discover what will happen in the next "
"weeks."
msgstr "随时了解我们的最新消息，并了解未来几周会发生什么。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "Stay tuned for an exciting new online presence."
msgstr "敬请期待，全新网页即将上线。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_s_three_columns
msgid ""
"Step into our past and witness the transformation of a simple idea into an "
"innovative force. Our journey, born in a garage, reflects the power of "
"passion and hard work."
msgstr "走进我们的过去，见证从一个简单的想法到创新力量的转变。我们在车库中诞生的历程，体现了激情和勤奋的力量。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
#: model_terms:ir.ui.view,arch_db:website.step_wizard
msgid "Steps"
msgstr "步骤"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
msgid "Sticky"
msgstr "黏住"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Storage"
msgstr "存储"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_stores_locator
msgid "Stores Locator"
msgstr "商店定位器"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid "Storytelling is powerful.<br/> It draws readers in and engages them."
msgstr "故事的力量很强大。<br/>它吸引读者并让他们参与其中。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_process_steps_options
msgid "Straight arrow"
msgstr "直线箭头"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_image_text
msgid "Strategic Marketing Solutions"
msgstr "战略营销解决方案"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_0_s_three_columns
msgid "Strength Training:"
msgstr "力量训练："

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Stretch menu"
msgstr "拉伸菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Stretch to Equal Height"
msgstr "拉伸到等高"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Striped"
msgstr "条纹"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Stroke Width"
msgstr "行程宽度"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Structure"
msgstr "结构"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_searchbar/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Style"
msgstr "风格"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Sub Menus"
msgstr "子菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Subheading"
msgstr "副标题"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
#, python-format
msgid "Subject"
msgstr "主题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus
#: model_terms:ir.ui.view,arch_db:website.s_website_form
msgid "Submit"
msgstr "提交"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Submit sitemap to Google"
msgstr "向 Google 提交网站地图"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form.xml:0
#: code:addons/website/static/src/xml/website_form.xml:0
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#, python-format
msgid "Success"
msgstr "成功"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_success_stories
msgid "Success Stories"
msgstr "成功案例"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "Suggestions"
msgstr "建议"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Surrounded"
msgstr "环绕"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Google reCaptcha 检测到可疑活动。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Switch Theme"
msgstr "切换主题"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid "System Fonts"
msgstr "系统字体"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "T-shirts"
msgstr "T 恤"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
#, python-format
msgid "TIP: Once loaded, follow the"
msgstr "提示：加载后，按照"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Table of Content"
msgstr "目录"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_thumbnails
msgid "Tablets"
msgstr "平板电脑"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Tabs"
msgstr "页卡"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
msgid "Tabs color"
msgstr "标签颜色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Tada"
msgstr "哒哒"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_asset__target
msgid "Target"
msgstr "目标"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_groups
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Team"
msgstr "团队"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "Technical name:"
msgstr "技术名称："

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Telephone"
msgstr "电话"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "Televisions"
msgstr "电视"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Tell what's the value for the <br/>customer for this feature."
msgstr "告诉客户<br/>该功能的价值。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Template"
msgstr "模板"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
#, python-format
msgid "Template ID: %s"
msgstr "模板标识：%s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "Template fallback"
msgstr "模板回滚"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
msgid "Terms of Services"
msgstr "‎服务条款‎"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Terms of service"
msgstr "服务条款"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "Terrain"
msgstr "地形"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_edit_robots
msgid "Test your robots.txt with Google Search Console"
msgstr "使用 Google 搜索控制台测试您的robots.txt"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.grid_layout_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Text"
msgstr "文本"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Text - Image"
msgstr "文本 - 图片"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Text Alignment"
msgstr "文字对齐"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Text Color"
msgstr "文本颜色"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Text Cover"
msgstr "文字封面"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: model_terms:ir.ui.view,arch_db:website.s_text_highlight
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Text Highlight"
msgstr "文本突出显示"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_masonry_block_options
msgid "Text Image Text"
msgstr "文本 图像 文本"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Text Inline"
msgstr "文本内联"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list_options
msgid "Text Position"
msgstr "文字位置"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Text muted. Lorem <b>ipsum dolor sit amet</b>, consectetur."
msgstr "文本变浅. Lorem 1ipsum dolor sit amet1, consectetur."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "Thank You For Your Feedback"
msgstr "感谢您的反馈"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid "Thank You!"
msgstr "谢谢！"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_instagram_page/options.js:0
#, python-format
msgid "The Instagram page name is not valid"
msgstr "Instagram 页面名称无效"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "The chosen name already exists"
msgstr "所选名称已存在"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid ""
"The compact charging case offers convenient on-the-go charging with a "
"battery life that lasts up to 17h, you can enjoy your favorite tunes without"
" interruption."
msgstr "小巧的充电盒可提供方便的随身充电功能，电池寿命长达 17 小时，让您可以不间断地享受最喜爱的音乐。"

#. module: website
#. odoo-python
#: code:addons/website/models/res_company.py:0
#, python-format
msgid ""
"The company %(company_name)r cannot be archived because it has a linked website %(website_name)r.\n"
"Change that website's company first."
msgstr ""
"公司 %(company_name)r 不能存档，因为它有一个链接的网站 %(website_name)r.\n"
"首先改变网站的公司."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "The company this website belongs to"
msgstr "该网站所属的公司"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
#, python-format
msgid ""
"The description will be generated by search engines based on page content "
"unless you specify one."
msgstr "除非您指定一个描述，否则搜索引擎描述会根据网页内容自动生成。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
#, python-format
msgid ""
"The description will be generated by social media based on page content "
"unless you specify one."
msgstr "除非您指定一个描述，否则描述会通过社交媒体基于网页内容自动生成。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_showcase
msgid ""
"The earbuds fit seamlessly into your lifestyle, while the charging case's "
"pocket-friendly size ensures you can carry your audio experience wherever "
"you roam."
msgstr "这款耳塞能完美融入您的生活方式，而充电盒的袖珍尺寸则能确保您随身携带，随时随地享受音频体验。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "The field '%s' is mandatory for the action '%s'."
msgstr "对于操作 \"%s\"，字段 \"%s\" 为必填项。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form.xml:0
#, python-format
msgid "The form has been sent successfully."
msgstr "表单已经被成功发送。"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "The form's specified model does not exist"
msgstr "表单指定的模型不存在"

#. module: website
#: model:ir.model.fields,help:website.field_res_partner__website_url
#: model:ir.model.fields,help:website.field_res_users__website_url
#: model:ir.model.fields,help:website.field_website_controller_page__website_url
#: model:ir.model.fields,help:website.field_website_page__website_url
#: model:ir.model.fields,help:website.field_website_published_mixin__website_url
#: model:ir.model.fields,help:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,help:website.field_website_snippet_filter__website_url
msgid "The full URL to access the document through the website."
msgstr "通过网站访问文档的完整网址。"

#. module: website
#: model:ir.model.fields,help:website.field_ir_actions_server__website_url
#: model:ir.model.fields,help:website.field_ir_cron__website_url
msgid "The full URL to access the server action through the website."
msgstr "通过网站访问单据的完整URL。"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#, python-format
msgid "The homepage URL should be relative and start with '/'."
msgstr "主页 URL 应为相对 URL，以\"/\"开头。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/new_content.js:0
#, python-format
msgid "The installation of an App is already in progress."
msgstr "应用程序正在安装中."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "The language of the keyword and related keywords."
msgstr "关键字和相关关键字的语言。"

#. module: website
#: model:ir.model.fields,help:website.field_website_snippet_filter__limit
msgid "The limit is the maximum number of records retrieved"
msgstr "检索到的最大记录数"

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "The limit must be between 1 and 16."
msgstr "必须在 1 到 16 之间."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "The logo is too large. Please upload a logo smaller than 2.5 MB."
msgstr "徽标太大。请上传小于 2.5 MB 的徽标。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "The maximum number of files that can be uploaded."
msgstr "上载文档数目上限。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "The maximum size (in MB) an uploaded file can have."
msgstr "每个上载文档的最大大小（单位 MB）。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "The message will be visible once the countdown ends"
msgstr "倒计时结束后将显示该消息"

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__page_name
msgid ""
"The name is used to generate the URL and is shown in the browser title bar"
msgstr "该名称用于生成 URL，并显示在浏览器标题栏中"

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__name_slugified
msgid "The name of the page usable in a URL"
msgstr "可在 URL 中使用的页面名称"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "The selected templates will be reset to their factory settings."
msgstr "选择的模板将会重置为其出厂模式。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_little_icons
msgid "The team"
msgstr "团队"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "The title will take a default value unless you specify one."
msgstr "指定一个标题，否则该标题将是默认值。"

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__page_type
msgid ""
"The type of the page. If set, it indicates whether the page displays a list "
"of records or a single record"
msgstr "页面类型。如果设置，则表示页面显示的是记录列表还是单条记录"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"The website will not work properly if you reject or discard those cookies."
msgstr "如果您拒绝或丢弃这些 cookie，该网站将无法正常运行。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "The website will still work if you reject or discard those cookies."
msgstr "如果您拒绝或丢弃这些 cookie，该网站仍然可以运行。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#: model:ir.model.fields,field_description:website.field_website__theme_id
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.theme_view_search
#, python-format
msgid "Theme"
msgstr "主题"

#. module: website
#: model:ir.model,name:website.model_theme_ir_asset
msgid "Theme Asset"
msgstr "主题资产"

#. module: website
#: model:ir.model,name:website.model_theme_ir_attachment
msgid "Theme Attachments"
msgstr "主题附件"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "Theme Colors"
msgstr "主题颜色"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_asset__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__theme_template_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__theme_template_id
#: model:ir.model.fields,field_description:website.field_product_document__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_controller_page__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_menu__theme_template_id
#: model:ir.model.fields,field_description:website.field_website_page__theme_template_id
msgid "Theme Template"
msgstr "主题模板"

#. module: website
#: model:ir.model,name:website.model_theme_ir_ui_view
msgid "Theme UI View"
msgstr "主题 UI 视图"

#. module: website
#: model:ir.model,name:website.model_theme_utils
msgid "Theme Utils"
msgstr "主题用途"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "There are currently no pages for this website."
msgstr "该网站目前没有网页。"

#. module: website
#. odoo-python
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "There are no contact and/or no email linked to this visitor."
msgstr "没有与该访问者相关的联系方式和/或电子邮件。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "There is no field available for this option."
msgstr "此选项没有可用的字段。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"These terms of service (\"Terms\", \"Agreement\") are an agreement between "
"the website (\"Website operator\", \"us\", \"we\" or \"our\") and you "
"(\"User\", \"you\" or \"your\"). This Agreement sets forth the general terms"
" and conditions of your use of this website and any of its products or "
"services (collectively, \"Website\" or \"Services\")."
msgstr ""
"这些服务条款（“条款”，“协议”）是网站（“网站运营商”，“我们”，“我们”或“我们的”）与您（“用户”，“您”或“您的”之间的协议“）。本协议规定了您使用本网站及其任何产品或服务（统称为“网站”或“服务”）的一般期限和条件。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_big_icons_subtitles
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_images_subtitles
msgid "They trust us since years"
msgstr "他们多年来一直信任我们"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thick"
msgstr "厚"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thickness"
msgstr "厚度"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "Thin"
msgstr "细"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid "Third Feature"
msgstr "第三功能"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_multi_menus
msgid "Third Menu"
msgstr "第三个菜单"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "This URL is contained in the “%(field)s” of the following “%(model)s”"
msgstr "这个URL包含在模块%(model)s的%(field)s字段中。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_0_s_three_columns
msgid ""
"This coaching program offers specialized strength-focused workouts, "
"nutrition guidance, and expert coaching. Elevate your fitness level and "
"achieve feats you never thought possible."
msgstr "该辅导计划提供专门的力量训练、营养指导和专家辅导。提升你的健身水平，实现你从未想过的壮举。"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__favicon
#: model:ir.model.fields,help:website.field_website__favicon
msgid "This field holds the image used to display a favicon on the website."
msgstr "该字段保存用于在网站上显示图标的图像。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid ""
"This field is mandatory for this action. You cannot remove it. Try hiding it"
" with the 'Visibility' option instead and add it a default value."
msgstr "此字段是此操作的必填字段，不可删除。请尝试使用 \"可见性\" 选项将其隐藏，并为它设定一个默认值。"

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__arch_base
#: model:ir.model.fields,help:website.field_website_page__arch_base
msgid "This field is the same as `arch` field without translations"
msgstr "该字段与没有翻译的`arch`字段相同"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__website_default_lang_code
msgid "This field is used to set/get locales for user"
msgstr "此字段用于为用户设定或获取地区设置"

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__arch
#: model:ir.model.fields,help:website.field_website_page__arch
msgid ""
"This field should be used when accessing view arch. It will use translation.\n"
"                               Note that it will read `arch_db` or `arch_fs` if in dev-xml mode."
msgstr ""
"访问视图arch时应使用此字段。它将使用翻译。\n"
"                               如果在dev-xml模式中，它将读取arch_db或arch_fs。"

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__arch_db
#: model:ir.model.fields,help:website.field_website_page__arch_db
msgid "This field stores the view arch."
msgstr "该字段存储视图拱。"

#. module: website
#: model:ir.model.fields,help:website.field_website_controller_page__arch_prev
#: model:ir.model.fields,help:website.field_website_page__arch_prev
msgid ""
"This field will save the current `arch_db` before writing on it.\n"
"                                                                         Useful to (soft) reset a broken view."
msgstr ""
"该字段将在写入之前保存当前的“arch_db”。\n"
"                                                                         用于（软）重置断开的视图。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#, python-format
msgid ""
"This font already exists, you can only add it as a local font to replace the"
" server version."
msgstr "该字体已存在，只能添加为本地字体，以替换服务器版本。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/editor/snippets.options.js:0
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "This font is hosted and served to your visitors by Google servers"
msgstr "此字体由谷歌服务器托管并提供给您的访客"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "This is a \""
msgstr "这是一个‘"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"This is a paragraph. Ambitioni dedisse scripsisse iudicaretur. Nihilne te "
"nocturnum praesidium Palati, nihil urbis vigiliae. Unam incolunt Belgae, "
"aliam Aquitani, tertiam. Integer legentibus erat a ante historiarum dapibus."
" Phasellus laoreet lorem vel dolor tempus vehicula."
msgstr ""
"This is a paragraph. Ambitioni dedisse scripsisse iudicaretur. Nihilne te "
"nocturnum praesidium Palati, nihil urbis vigiliae. Unam incolunt Belgae, "
"aliam Aquitani, tertiam. Integer legentibus erat a ante historiarum dapibus."
" Phasellus laoreet lorem vel dolor tempus vehicula."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_banner
msgid ""
"This is a simple hero unit, a simple jumbotron-style component for calling "
"extra attention to featured content or information."
msgstr "这是一个简单的英雄单位，一个简单的大型超大型组件，用于引起对特色内容或信息的额外关注。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid ""
"This is a wider card with supporting text below as a natural lead-in to "
"additional content. This content is a little bit longer."
msgstr "这是一张较宽的卡片，下面有辅助文字，自然引出其他内容。该内容稍长。"

#. module: website
#: model:ir.ui.view,website_meta_description:website.contactus
msgid "This is the contact us page of the website"
msgstr "这是网站的 \"联系我们\" 页面"

#. module: website
#: model:ir.ui.view,website_meta_description:website.homepage
msgid "This is the homepage of the website"
msgstr "这是网站主页"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "This message has been posted on your website!"
msgstr "此消息已发布在您的网站上！"

#. module: website
#. odoo-python
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "This operator is not supported"
msgstr "不支持此运算符"

#. module: website
#: model:ir.ui.menu,name:website.menu_current_page
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "This page"
msgstr "此网页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.page_404
msgid ""
"This page does not exist, but you can create it as you are editor of this "
"site."
msgstr "此网页不存在，但您可以作为本网站的编辑，您可以创建它。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
#, python-format
msgid "This translation is not editable."
msgstr "此翻译不可编辑。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
#, python-format
msgid ""
"This value will be escaped to be compliant with all major browsers and used "
"in url. Keep it empty to use the default name of the record."
msgstr "该值将被转义以与所有主要浏览器兼容并在 url 中使用。将其留空以使用记录的默认名称。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/backend/view_hierarchy/view_hierarchy.xml:0
#, python-format
msgid "This view arch has been modified"
msgstr "此视图结构已修改"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/website_switcher.js:0
#, python-format
msgid "This website does not have a domain configured."
msgstr "该网站未设置网域名称。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/website_switcher.js:0
#, python-format
msgid ""
"This website does not have a domain configured. To avoid unexpected behaviours during website edition, we recommend closing (or refreshing) other browser tabs.\n"
"To remove this message please set a domain in your website settings"
msgstr ""
"网站未设置域名。为避免在编辑网站过程中出现意外行为，我们建议关闭（或刷新）其他浏览器标签。\n"
"要删除此信息，请在网站设置中设置域名"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Thumbnails"
msgstr "缩略图"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_social_media
#, python-format
msgid "TikTok"
msgstr "TikTok"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_tiktok
msgid "TikTok Account"
msgstr "TikTok 账号"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__time_since_last_action
msgid "Time since last page view. E.g.: 2 minutes ago"
msgstr "上次浏览的时间。例如：2分钟前"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "Time's up! You can now visit"
msgstr "时间到了！您现在可以访问了"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_facebook_page_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Timeline"
msgstr "时间线"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__timezone
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Timezone"
msgstr "时区"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_4
#: model_terms:digest.tip,tip_description:website.digest_tip_website_4
msgid "Tip: Add shapes to energize your Website"
msgstr "提示：添加形状可以激活您的网站"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_0
#: model_terms:digest.tip,tip_description:website.digest_tip_website_0
msgid "Tip: Engage with visitors to convert them into leads"
msgstr "提示：与访客互动，将他们转化为潜在客户"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_2
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid "Tip: Search Engine Optimization (SEO)"
msgstr "提示：搜索引擎优化 (SEO)"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_3
#: model_terms:digest.tip,tip_description:website.digest_tip_website_3
msgid "Tip: Use illustrations to spice up your website"
msgstr "提示：使用插图为您的网站增添趣味"

#. module: website
#: model:digest.tip,name:website.digest_tip_website_1
#: model_terms:digest.tip,tip_description:website.digest_tip_website_1
msgid "Tip: Use royalty-free photos"
msgstr "提示：使用免版税照片"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_map_s_text_block_h1
#: model_terms:ir.ui.view,arch_db:website.s_text_block_h1
#: model_terms:ir.ui.view,arch_db:website.s_text_block_h2
#: model_terms:ir.ui.view,arch_db:website.snippets
#, python-format
msgid "Title"
msgstr "称谓"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Title Position"
msgstr "标题位置"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns
msgid ""
"To add a fourth column, reduce the size of these three columns using the "
"right icon of each block. Then, duplicate one of the columns to create a new"
" one as a copy."
msgstr "要添加第四列，请使用每个模块的右图标减小这三列的大小。然后，复制其中一列以创建新列作为副本。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "To be successful your content needs to be useful to your readers."
msgstr "要想取得成功，您的内容必须对读者有用。"

#. module: website
#: model_terms:digest.tip,tip_description:website.digest_tip_website_2
msgid ""
"To get more visitors, you should target keywords that are often searched in "
"Google. With the built-in SEO tool, once you define a few keywords, Odoo "
"will recommend you the best keywords to target. Then adapt your title and "
"description accordingly to boost your traffic."
msgstr ""
"为了获得更多访客，您应该定位在 Google 中经常搜索的关键字。借助内置的 SEO 工具，一旦您定义了几个关键字，ERP "
"就会向您推荐最佳的目标关键字。然后相应地调整您的标题和描述以增加您的流量."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr "要在B2B模式下发送邀请，请在列表视图中打开联系人或选择多个联系人，然后单击下拉菜单中的“门户访问管理”选项*操作。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Toggle"
msgstr "切换"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.template_header_mobile
msgid "Toggle navigation"
msgstr "切换导航"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_0_s_three_columns
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_image_text
#: model_terms:ir.ui.view,arch_db:website.new_page_template_team_s_media_list
#: model_terms:ir.ui.view,arch_db:website.s_company_team
msgid "Tony Fred, CEO"
msgstr "Tony Fred，首席执行官"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
msgid "Tooltip"
msgstr "工具提示"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_four
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_one
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_three
#: model_terms:ir.ui.view,arch_db:website.template_header_sales_two
#: model_terms:ir.ui.view,arch_db:website.template_header_search
msgid "Top"
msgstr "顶部"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#, python-format
msgid "Top Menu for Website %s"
msgstr "网站顶级菜单 %s"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
msgid "Top to Bottom"
msgstr "从上到下"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Tops"
msgstr "顶部"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__page_count
msgid "Total number of tracked page visited"
msgstr "访问的跟踪网页总数"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__visitor_page_count
msgid "Total number of visits on tracked pages"
msgstr "跟踪网页的总访问次数"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__track
#: model:ir.model.fields,field_description:website.field_website_controller_page__track
#: model:ir.model.fields,field_description:website.field_website_page__track
msgid "Track"
msgstr "追踪"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Track visits using Google Analytics"
msgstr "使用谷歌分析跟踪访问"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Tracked"
msgstr "已跟踪"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_image
msgid "Transform Your Brand"
msgstr "改造您的品牌"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Transition"
msgstr "翻译"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/translate_website.xml:0
#, python-format
msgid "Translate"
msgstr "翻译"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
#, python-format
msgid "Translate Attribute"
msgstr "翻译属性"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
#, python-format
msgid "Translate Selection Option"
msgstr "翻译选择选项"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.js:0
#, python-format
msgid "Translate header in the text. Menu is generated automatically."
msgstr "翻译文本中的标题。菜单会自动生成。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
#, python-format
msgid "Translated content"
msgstr "已翻译的内容"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Trigger"
msgstr "触发器"

#. module: website
#: model:ir.model.fields,help:website.field_website__configurator_done
msgid "True if configurator has been completed or ignored"
msgstr "真表示如果配置器已完成或被忽略"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog
msgid "Tuna and Salmon Burger"
msgstr "金枪鱼和鲑鱼汉堡"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "Turn every feature into a benefit for your reader."
msgstr "将每个功能转化为读者的利益。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.header_social_links
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_share
#: model_terms:ir.ui.view,arch_db:website.s_social_media
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
#, python-format
msgid "Twitter"
msgstr "推特"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_twitter
msgid "Twitter Account"
msgstr "Twitter账号"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.external_snippets
msgid "Twitter Scroller"
msgstr "Twitter 滚动条"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/fields.xml:0
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__type
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.s_chart_options
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#, python-format
msgid "Type"
msgstr "类型"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/web_editor.xml:0
#, python-format
msgid "Type '"
msgstr "类型 '"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid ""
"Type '<i class=\"confirm_word\">yes</i>' in the box below if you want to "
"confirm."
msgstr "如果您想确认，在下面的框中键入‘<i class=\"confirm_word\">是</i>‘。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#, python-format
msgid "Type in text here..."
msgstr "在此输入文字..."

#. module: website
#: model:ir.model.fields,help:website.field_website_rewrite__redirect_type
msgid ""
"Type of redirect/Rewrite:\n"
"\n"
"        301 Moved permanently: The browser will keep in cache the new url.\n"
"        302 Moved temporarily: The browser will not keep in cache the new url and ask again the next time the new url.\n"
"        404 Not Found: If you want remove a specific page/controller (e.g. Ecommerce is installed, but you don't want /shop on a specific website)\n"
"        308 Redirect / Rewrite: If you want rename a controller with a new url. (Eg: /shop -> /garden - Both url will be accessible but /shop will automatically be redirected to /garden)\n"
"    "
msgstr ""
"重定向/重写的类型：\n"
"\n"
"        301 永久移动：浏览器将保留新 URL 的缓存。\n"
"        302 暂时移动：浏览器不会保留缓存新 URL，下次再次询问新 URL。\n"
"        404 未找到：如果您想要删除特定网页/控制器（例如，安装了电子商务，但您不希望/在特定网站上购物）\n"
"        308 重定向/重写：如果您想要使用新 URL 重命名控制器。（例如： /shop -> /花园 - 两个网址都可访问，但 /shop 将自动重定向到 /garden）\n"
"    "

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__name_slugified
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid "URL"
msgstr "网址"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_from
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "URL from"
msgstr "URL 来自"

#. module: website
#: model:ir.model.fields,help:website.field_res_config_settings__cdn_filters
#: model:ir.model.fields,help:website.field_website__cdn_filters
msgid "URL matching those filters will be rewritten using the CDN Base URL"
msgstr "网址匹配这些筛选将被使用CDN基本网址重写"

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "URL must not start with '#'."
msgstr ""

#. module: website
#: model:ir.model.fields,field_description:website.field_website_rewrite__url_to
msgid "URL to"
msgstr "URL 到"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "UTM Campaign"
msgstr "UTM营销"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "UTM Medium"
msgstr "UTM媒介"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "UTM Source"
msgstr "UTM来源"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Unalterable unique identifier"
msgstr "不可更改的唯一标识符"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Underline On Hover"
msgstr "悬停时下划线"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Understand how visitors engage with our website, via Google Analytics.\n"
"                                                Learn more about"
msgstr ""
"通过 Google Analytics 了解访问者如何与我们的网站互动。\n"
"                                                学习更多关于"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/utils.js:0
#, python-format
msgid "Unexpected "
msgstr "意外 "

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited CRM power and support"
msgstr "无限制享用 CRM 和技术支持"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_comparisons
msgid "Unlimited customization"
msgstr "随时随地使用定制功能"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/publish_button.xml:0
#: code:addons/website/static/src/components/views/page_list.js:0
#, python-format
msgid "Unpublish"
msgstr "取消发布"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/publish_button.xml:0
#: code:addons/website/static/src/components/fields/redirect_field.js:0
#: code:addons/website/static/src/systray_items/publish.js:0
#: model_terms:ir.ui.view,arch_db:website.publish_management
#, python-format
msgid "Unpublished"
msgstr "未发布"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
msgid "Unregistered"
msgstr "未注册"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Update theme"
msgstr "更新主题"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "Upload"
msgstr "上传"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/000.js:0
#, python-format
msgid "Uploaded file is too large."
msgstr "上传的文件太大。"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_attachment__url
#: model:ir.model.fields,field_description:website.field_theme_website_menu__url
#: model:ir.model.fields,field_description:website.field_theme_website_page__url
#: model:ir.model.fields,field_description:website.field_website_menu__url
#: model:ir.model.fields,field_description:website.field_website_track__url
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Url"
msgstr "Url"

#. module: website
#: model:ir.model.fields,help:website.field_website_visitor__country_flag
msgid "Url of static flag image"
msgstr "静态标志图像的 Url"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/edit_menu.xml:0
#, python-format
msgid "Url or Email"
msgstr "URL或电子邮件"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
msgid "Urls & Pages"
msgstr "网址和网页"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "Use Google Map on your website (Contact Us page, snippets, etc)."
msgstr "在您的网站上使用 Google 地图（联系我们网页、摘要等）。"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_website_menu__use_main_menu_as_parent
msgid "Use Main Menu As Parent"
msgstr "使用主菜单作为父类"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid ""
"Use Plausible.io, Simple and privacy-friendly Google Analytics alternative"
msgstr "使用Plausible接口，简单和隐私友好的谷歌分析替代方案"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Use a CDN to optimize the availability of your website's content"
msgstr "使用 CDN 优化网站内容的可用性"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__has_default_share_image
msgid "Use a image by default for sharing"
msgstr "默认情况下使用图像进行共享"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/fields.js:0
#, python-format
msgid "Use an array to list the images to use in the radio selection."
msgstr "使用数组列出单选中要使用的图像。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "Use as Homepage"
msgstr "用作主页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid "Use of Cookies"
msgstr "Cookies 的使用"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this component for creating a list of featured elements to which you "
"want to bring attention."
msgstr "使用此组件可以创建要引起注意的特色元素列表。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_media_list
msgid ""
"Use this snippet to build various types of components that feature a left- "
"or right-aligned image alongside textual content. Duplicate the element to "
"create a list that fits your needs."
msgstr "使用此代码段构建各种类型的组件，这些组件具有左对齐或右对齐的图像以及文本内容。复制元素以创建适合您需要的列表。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_carousel
msgid ""
"Use this snippet to presents your content in a slideshow-like format. Don't "
"write about products or services here, write about solutions."
msgstr "使用此片段以类似幻灯片的格式呈现您的内容。不要在这里写产品或服务，写解决方案。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/add_page_dialog.xml:0
#, python-format
msgid "Use this template"
msgstr "使用此模板"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.theme_view_kanban
msgid "Use this theme"
msgstr "使用此主题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid ""
"Use this timeline as a part of your resume, to show your visitors what "
"you've done in the past."
msgstr "使用此时间线作为简历的一部分，向访问者展示您过去所做的。"

#. module: website
#: model:ir.model.fields,help:website.field_ir_model__website_form_key
msgid "Used in FormBuilder Registry"
msgstr "在表单生成器注册表中使用"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Used in page content"
msgstr "用于网页内容"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Used in page description"
msgstr "用于网页描述"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Used in page first level heading"
msgstr "用于网页第一级标题"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Used in page second level heading"
msgstr "用于网页第二级标题"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "Used in page title"
msgstr "用于网页标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to collect information about your interactions with the website, the pages you've seen,\n"
"                                                and any specific marketing campaign that brought you to the website."
msgstr ""
"用于收集有关您与网站的互动、\n"
"您浏览过的网页以及将您带到网站的任何特定营销活动的信息。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Used to make advertising more engaging to users and more valuable to publishers and advertisers,\n"
"                                                such as providing more relevant ads when you visit other websites that display ads or to improve reporting on ad campaign performance."
msgstr ""
"用于使广告对用户更具吸引力，对发布商和广告商更有价值，\n"
"例如在您访问其他展示广告的网站时提供更相关的广告或改进广告活动绩效报告。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid "Useful Links"
msgstr "友情链接"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_numbers
msgid "Useful options"
msgstr "有用的选项"

#. module: website
#: model:ir.model,name:website.model_res_users
msgid "User"
msgstr "用户"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Users"
msgstr "用户"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "Utilities &amp; Typography"
msgstr "工具和排版"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar_options
msgid "Value"
msgstr "值"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_three_columns_menu
msgid "Vegetable Salad, Beef Burger and Mango Ice Cream"
msgstr "蔬菜沙拉、牛肉汉堡和芒果冰淇淋"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vert. Alignment"
msgstr "垂直. 对齐"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs_options
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical"
msgstr "垂直"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Vertical Alignment"
msgstr "垂直对齐"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_video
#: model_terms:ir.ui.view,arch_db:website.snippet_options_background_options
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "Video"
msgstr "视频"

#. module: website
#: model:ir.model,name:website.model_ir_ui_view
#: model:ir.model.fields,field_description:website.field_theme_website_page__view_id
#: model:ir.model.fields,field_description:website.field_website_controller_page__view_id
#: model:ir.model.fields,field_description:website.field_website_page__view_id
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid "View"
msgstr "视图"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__arch
#: model:ir.model.fields,field_description:website.field_website_page__arch
msgid "View Architecture"
msgstr "视图结构"

#. module: website
#: model:ir.actions.client,name:website.action_website_view_hierarchy
msgid "View Hierarchy"
msgstr "查看层级"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__name
#: model:ir.model.fields,field_description:website.field_website_page__name
msgid "View Name"
msgstr "视图名称"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__type
#: model:ir.model.fields,field_description:website.field_website_page__type
msgid "View Type"
msgstr "视图类型"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__mode
#: model:ir.model.fields,field_description:website.field_website_page__mode
msgid "View inheritance mode"
msgstr "视图继承模式"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor.js:0
#, python-format
msgid "Views and Assets bundles"
msgstr "视图和资产包"

#. module: website
#: model:ir.model.fields,field_description:website.field_theme_ir_ui_view__copy_ids
msgid "Views using a copy of me"
msgstr "使用我的副本视图"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_controller_page__inherit_children_ids
#: model:ir.model.fields,field_description:website.field_website_page__inherit_children_ids
msgid "Views which inherit from this one"
msgstr "继承于此的视图"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility
#: model:ir.model.fields,field_description:website.field_website_controller_page__visibility
#: model:ir.model.fields,field_description:website.field_website_page__visibility
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visibility"
msgstr "可见性"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password
#: model:ir.model.fields,field_description:website.field_website_controller_page__visibility_password
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password
#: model_terms:ir.ui.view,arch_db:website.view_view_form_extend
msgid "Visibility Password"
msgstr "可见性密码"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__visibility_password_display
#: model:ir.model.fields,field_description:website.field_website_controller_page__visibility_password_display
#: model:ir.model.fields,field_description:website.field_website_page__visibility_password_display
msgid "Visibility Password Display"
msgstr "显示密码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options_conditional_visibility
msgid "Visible for"
msgstr "可见于"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Everyone"
msgstr "对所有人可见"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged In"
msgstr "登录后显示"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Visible for Logged Out"
msgstr "注销后显示"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__website_published
#: model:ir.model.fields,field_description:website.field_res_users__website_published
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_published
#: model:ir.model.fields,field_description:website.field_website_page__website_published
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_published
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_published
msgid "Visible on current website"
msgstr "在当前网站显示"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "Visible only if"
msgstr "仅在以下情况下可见"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visit_datetime
msgid "Visit Date"
msgstr "访问日期"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/options.xml:0
#, python-format
msgid "Visit our Facebook page to know if you are one of the lucky winners."
msgstr "访问我们的 Facebook 网页，了解您是否是幸运获奖者之一。"

#. module: website
#: model:ir.model,name:website.model_website_track
#: model:ir.model.fields,field_description:website.field_website_visitor__page_ids
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visited Pages"
msgstr "访问过的网页"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_visitor__website_track_ids
msgid "Visited Pages History"
msgstr "访问过的网页历史"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_track__visitor_id
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visitor"
msgstr "访问者"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_graph
msgid "Visitor Page Views"
msgstr "访问者网页浏览量"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_page_view_tree
msgid "Visitor Page Views History"
msgstr "访客网页浏览历史"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_graph
msgid "Visitor Views"
msgstr "游客意见"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_track_view_tree
msgid "Visitor Views History"
msgstr "访客浏览历史"

#. module: website
#: model:ir.actions.act_window,name:website.website_visitors_action
#: model:ir.model.fields,field_description:website.field_res_partner__visitor_ids
#: model:ir.model.fields,field_description:website.field_res_users__visitor_ids
#: model:ir.ui.menu,name:website.website_visitor_menu
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_graph
msgid "Visitors"
msgstr "访客"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_kanban
msgid "Visits"
msgstr "访问"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitor_view_action
msgid ""
"Wait for visitors to come to your website to see the pages they viewed."
msgstr "等待访问者访问您的网站来视图他们浏览的网页。"

#. module: website
#: model_terms:ir.actions.act_window,help:website.website_visitors_action
msgid ""
"Wait for visitors to come to your website to see their history and engage "
"with them."
msgstr "等待访问者访问您的网站以查看他们的历史并与他们互动。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_alert_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Warning"
msgstr "警告"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Watches"
msgstr "手表"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_4_s_cover
msgid "We Are Coming Soon"
msgstr "我们即将到来"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_5_s_banner
msgid "We Are Down for Maintenance"
msgstr "停机维护"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life through disruptive products. We build great products to solve your business problems.\n"
"                            <br/><br/>Our products are designed for small to medium size companies willing to optimize their performance."
msgstr ""
"我们是一支充满激情朝气磅礴的团队！我们的愿景是试图通过构建一个具有颠覆意义的软件系统，\n"
"这样一个具有颠覆意义的产品！以此提升各位在座的每一家中小企业\n"
"                            <br/><br/>的产品品质， 来解决各位在商业活动中遇到的诸多经营问题."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid ""
"We are a team of passionate people whose goal is to improve everyone's life "
"through disruptive products. We build great products to solve your business "
"problems. Our products are designed for small to medium size companies "
"willing to optimize their performance."
msgstr ""
"我们是一支充满激情的团队，我们的目标是通过颠覆性的产品改善每个人的生活。我们打造优秀的产品，解决您的业务问题。我们的产品专为希望优化业绩的中小型公司而设计。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid ""
"We are a team of passionate people whose goal is to improve everyone's "
"life.<br/>Our services are designed for small to medium size companies."
msgstr ""
"我们是一支充满激情朝气磅礴的团队！我们的愿景是试图通过构建一个具有颠覆意义的软件系统，.<br/>这样一个具有颠覆意义的产品！以此提升各位在座的每一家中小企业的产品品质，"
" 来解决各位在商业活动中遇到的诸多经营问题."

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_progress_bar
msgid "We are almost done!"
msgstr "我们差不多完成了！"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_references
msgid "We are in good company."
msgstr "我们相处得很好。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_text_image
msgid ""
"We believe that every fitness journey is unique. Our approach begins with "
"understanding your fitness aspirations, your current lifestyle, and any "
"challenges you face."
msgstr "我们相信，每一段健身旅程都是独一无二的。我们的方法首先是了解您的健身愿望、您目前的生活方式以及您面临的任何挑战。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_facebook_page/options.js:0
#, python-format
msgid "We couldn't find the Facebook page"
msgstr "未能找到该 Facebook 页面"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We do not currently support Do Not Track signals, as there is no industry "
"standard for compliance."
msgstr "我们目前不支持不跟踪信号，因为没有符合行业标准。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "We found these ones:"
msgstr "我们找到了这些："

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"We may not be able to provide the best service to you if you reject those "
"cookies, but the website will work."
msgstr "如果您拒绝这些 cookie，我们可能无法为您提供最佳服务，但该网站将正常运行。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_2_s_three_columns
msgid ""
"We monitor your progress meticulously, adjusting your plan as needed to "
"ensure continuous improvement and results."
msgstr "我们会认真监测您的进展，根据需要调整计划，以确保不断改进并取得成果。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_cards
msgid "We offer tailor-made products according to your needs and your budget."
msgstr "我们根据您的需求和预算提供量身定制的产品。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid ""
"We use cookies to provide improved experience on this website. You can learn"
" more about our cookies and how we use them in our"
msgstr "我们使用cookies来改善本网站的体验。 你可在此了解更多有关我们的cookies，以及我们如何使用它们"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#, python-format
msgid ""
"We use cookies to provide you a better user experience on this website."
msgstr "我们使用cookie来为您提供在这网站上的更好的用户体验。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.contactus_thanks_ir_ui_view
msgid "We will get back to you shortly."
msgstr "我们将很快给您回复。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "We'll set you up and running in"
msgstr "我们将为您设置并运行"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_full_s_text_image
msgid ""
"We're driven by the aspiration to redefine industry standards, to exceed the"
" expectations of our clients, and to foster a culture of continuous growth."
msgstr "我们渴望重新定义行业标准，超越客户的期望，并培养一种持续增长的文化。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_tree
msgid "Web Visitors"
msgstr "网络访问者"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/fields/redirect_field.xml:0
#: code:addons/website/static/src/components/views/page_list.xml:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/components/wysiwyg_adapter/wysiwyg_adapter.js:0
#: code:addons/website/static/src/js/editor/snippets.editor.js:0
#: model:ir.actions.act_url,name:website.action_website
#: model:ir.model,name:website.model_website
#: model:ir.model.fields,field_description:website.field_ir_asset__website_id
#: model:ir.model.fields,field_description:website.field_ir_attachment__website_id
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_id
#: model:ir.model.fields,field_description:website.field_product_document__website_id
#: model:ir.model.fields,field_description:website.field_res_company__website_id
#: model:ir.model.fields,field_description:website.field_res_partner__website_id
#: model:ir.model.fields,field_description:website.field_res_users__website_id
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_id
#: model:ir.model.fields,field_description:website.field_website_menu__website_id
#: model:ir.model.fields,field_description:website.field_website_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_page__website_id
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_id
#: model:ir.model.fields,field_description:website.field_website_rewrite__website_id
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_id
#: model:ir.model.fields,field_description:website.field_website_visitor__website_id
#: model:ir.ui.menu,name:website.menu_website_configuration
#: model_terms:ir.ui.view,arch_db:website.menu_search
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website.view_server_action_search_website
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_search
#, python-format
msgid "Website"
msgstr "网站"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_company_id
msgid "Website Company"
msgstr "网站公司"

#. module: website
#: model:ir.model.fields,field_description:website.field_website_configurator_feature__website_config_preselection
msgid "Website Config Preselection"
msgstr "网站预设配置"

#. module: website
#: model:ir.actions.client,name:website.website_configurator
msgid "Website Configurator"
msgstr "网站配置器"

#. module: website
#: model:ir.model,name:website.model_website_configurator_feature
msgid "Website Configurator Feature"
msgstr "网站配置器特性"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_domain
#: model:ir.model.fields,field_description:website.field_website__domain
msgid "Website Domain"
msgstr "网站域名"

#. module: website
#: model:ir.model.constraint,message:website.constraint_website_domain_unique
msgid "Website Domain should be unique."
msgstr "网站域必须是唯一的"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__favicon
msgid "Website Favicon"
msgstr "网站图标"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_model__website_form_key
msgid "Website Form Key"
msgstr "网站表单密钥"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.ir_model_view
msgid "Website Forms"
msgstr "网站表单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Info"
msgstr "网站信息"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.cookies_bar.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_logo
#: model:ir.model.fields,field_description:website.field_website__logo
#, python-format
msgid "Website Logo"
msgstr "网站标志"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_menu
#: model:ir.model,name:website.model_website_menu
msgid "Website Menu"
msgstr "网站菜单"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_menus_form_view
msgid "Website Menus Settings"
msgstr "网站菜单设置"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_controller_pages_form_view
msgid "Website Model Page Settings"
msgstr "网站模型页面设置"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_controller_pages_list
msgid "Website Model Pages"
msgstr "网站模型页面"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_name
#: model:ir.model.fields,field_description:website.field_website__name
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "Website Name"
msgstr "网站名称"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__first_page_id
#: model:ir.model.fields,field_description:website.field_website_controller_page__first_page_id
#: model:ir.model.fields,field_description:website.field_website_page__first_page_id
msgid "Website Page"
msgstr "网站网页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_pages_form_view
msgid "Website Page Settings"
msgstr "网站网页设置"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_pages_list
#: model_terms:ir.ui.view,arch_db:website.website_pages_view_search
msgid "Website Pages"
msgstr "网站网页"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_path
#: model:ir.model.fields,field_description:website.field_ir_cron__website_path
msgid "Website Path"
msgstr "网站路径"

#. module: website
#: model:ir.actions.client,name:website.website_preview
msgid "Website Preview"
msgstr "网站预览"

#. module: website
#: model:ir.model,name:website.model_website_published_mixin
msgid "Website Published Mixin"
msgstr "网站发布Mixin"

#. module: website
#: model:ir.model,name:website.model_website_searchable_mixin
msgid "Website Searchable Mixin"
msgstr "混合网站搜索"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_form
msgid "Website Settings"
msgstr "网站设置"

#. module: website
#: model:ir.model,name:website.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "网站片段筛选"

#. module: website
#: model:ir.model,name:website.model_theme_website_menu
msgid "Website Theme Menu"
msgstr "网站主题菜单"

#. module: website
#: model:ir.model,name:website.model_theme_website_page
msgid "Website Theme Page"
msgstr "网站主题网页"

#. module: website
#: model:ir.model.fields,field_description:website.field_res_partner__website_url
#: model:ir.model.fields,field_description:website.field_res_users__website_url
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_url
#: model:ir.model.fields,field_description:website.field_website_page__website_url
#: model:ir.model.fields,field_description:website.field_website_published_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_published_multi_mixin__website_url
#: model:ir.model.fields,field_description:website.field_website_snippet_filter__website_url
msgid "Website URL"
msgstr "网站网址"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_actions_server__website_url
#: model:ir.model.fields,field_description:website.field_ir_cron__website_url
msgid "Website Url"
msgstr "网站网址"

#. module: website
#: model:ir.model,name:website.model_website_visitor
#: model_terms:ir.ui.view,arch_db:website.website_visitor_view_form
msgid "Website Visitor"
msgstr "网页访问者"

#. module: website
#. odoo-python
#: code:addons/website/models/website_visitor.py:0
#, python-format
msgid "Website Visitor #%s"
msgstr "网站访问者 #%s"

#. module: website
#: model:ir.actions.server,name:website.website_visitor_cron_ir_actions_server
msgid "Website Visitor : clean inactive visitors"
msgstr "网站访问者：清理非活跃访客"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"Website may use cookies to personalize and facilitate maximum navigation of "
"the User by this site. The User may configure his / her browser to notify "
"and reject the installation of the cookies sent by us."
msgstr "网站可以使用cookie来个性化并促进本网站对用户的最大导航。用户可以配置他/她的浏览器以通知和拒绝我们发送的cookie的安装。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.menu_tree
msgid "Website menu"
msgstr "网站菜单"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_description
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_description
msgid "Website meta description"
msgstr "网站原说明"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_keywords
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_keywords
msgid "Website meta keywords"
msgstr "网站meta关键词"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_title
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_title
msgid "Website meta title"
msgstr "网站标题meta元素"

#. module: website
#: model:ir.model.fields,field_description:website.field_ir_ui_view__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_controller_page__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_page__website_meta_og_img
#: model:ir.model.fields,field_description:website.field_website_seo_metadata__website_meta_og_img
msgid "Website opengraph image"
msgstr "网站opengraph图像"

#. module: website
#: model:ir.model,name:website.model_website_rewrite
msgid "Website rewrite"
msgstr "网站重写"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.view_website_rewrite_form
msgid "Website rewrite Settings"
msgstr "网站重写设置"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.action_website_rewrite_tree
msgid "Website rewrites"
msgstr "网站重写"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_analytics
msgid "Website: Analytics"
msgstr "网站：分析"

#. module: website
#: model:ir.actions.server,name:website.ir_actions_server_website_dashboard
msgid "Website: Dashboard"
msgstr "网站：仪表板"

#. module: website
#: model:ir.actions.act_window,name:website.action_website_list
#: model:ir.ui.menu,name:website.menu_website_websites_list
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_personal_s_numbers
#: model_terms:ir.ui.view,arch_db:website.view_website_tree
msgid "Websites"
msgstr "网站"

#. module: website
#: model:ir.model.fields,field_description:website.field_base_language_install__website_ids
msgid "Websites to translate"
msgstr "要翻译的网站"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_0_s_three_columns
msgid "Weight Loss Transformation"
msgstr "减肥转型"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_s_text_block_2nd
msgid ""
"Welcome to our comprehensive range of Tailored Fitness Coaching Services, "
"with personalized workouts, customized nutrition plans, and unwavering "
"support, we're committed to helping you achieve lasting results that align "
"with your aspirations."
msgstr "欢迎使用我们全方位的定制健身指导服务，通过个性化锻炼、定制营养计划和坚定不移的支持，我们致力于帮助您实现与您的愿望相一致的持久效果。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#, python-format
msgid "Welcome to your"
msgstr "欢迎进入您的主页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_0_s_three_columns
msgid "Wellness Coaching"
msgstr "健康辅导"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_about_personal_s_text_block_h2
msgid "What Makes Me Proud"
msgstr "让我自豪的是"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_table_of_content
msgid "What you see is what you get"
msgstr "所见即所得"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_share
msgid "WhatsApp"
msgstr "WhatsApp"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_hr_options
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Width"
msgstr "宽度"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup
msgid "Win $20"
msgstr "赢取 20 美元"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_3_s_three_columns
msgid "Wireless Freedom"
msgstr "无线自由"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__ir_ui_view__visibility__password
msgid "With Password"
msgstr "带密码"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_services_0_s_three_columns
msgid ""
"With personalized fitness plans, tailored nutrition guidance, and consistent"
" support, you'll shed unwanted pounds while building healthy habits that "
"last."
msgstr "通过个性化的健身计划、量身定制的营养指导和始终如一的支持，您将减掉多余的体重，同时养成持久的健康习惯。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_menus_logos
msgid "Women"
msgstr "女士"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid ""
"Would you like to save before being redirected? Unsaved changes will be "
"discarded."
msgstr "您想在被重定向之前保存吗？未保存的更改将被丢弃。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_blockquote
#: model_terms:ir.ui.view,arch_db:website.s_quotes_carousel
msgid ""
"Write a quote here from one of your customers. Quotes are a great way to "
"build confidence in your products or services."
msgstr "在这里写一个客户的好评。好评是建立客户对你的产品或服务的信心的好方法。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_tabs
msgid "Write one or two paragraphs describing your product or services."
msgstr "写一两段描述您的产品或服务。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_image_text
#: model_terms:ir.ui.view,arch_db:website.s_text_image
msgid ""
"Write one or two paragraphs describing your product or services. To be "
"successful your content needs to be useful to your readers."
msgstr "写一两段描述你的产品或服务。为了取得成功，您的内容需要对您的读者有用。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_text_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature. To be successful your content needs to be useful to your readers."
msgstr "写一两段文字描述您的产品、服务或特定功能。要想取得成效，内容必须对读者有用。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_cover
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br/> To be successful your content needs to be useful to your "
"readers."
msgstr "写一两段描述您的产品、服务或具体功能。<br/>要获得成功，您的内容需要对您的读者有用。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features
msgid ""
"Write what the customer would like to know, <br/>not what you want to show."
msgstr "写下客户想知道的内容, <br/>而不是您想要展示的内容。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline_options
msgid "Year"
msgstr "年"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.js:0
#, python-format
msgid ""
"You are about to be redirected to the domain configured for your website ( "
"%s ). This is necessary to edit or view your website from the Website app. "
"You might need to log back in."
msgstr "即将重定向到为您的网站配置的域名（%s）。若要使用 “网站” 应用程序编辑或查看您的网站，此操作为必需。您可能需要重新登录。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/translator/translator.xml:0
#, python-format
msgid "You are about to enter the translation mode."
msgstr "您将进入翻译模式。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"You can choose to have your computer warn you each time a cookie is being sent, or you can choose to turn off all cookies.\n"
"                            Each browser is a little different, so look at your browser's Help menu to learn the correct way to modify your cookies."
msgstr ""
"您可以选择让您的计算机在每次发送 cookie 时向您发出警告，或者您可以选择关闭所有 cookie。\n"
"每个浏览器都略有不同，因此请查看浏览器的“帮助”菜单以了解修改 cookie 的正确方法。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_features_grid
msgid "You can edit colors and backgrounds to highlight features."
msgstr "您可以编辑颜色和背景以突出显示功能。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "You can edit, duplicate..."
msgstr "您可以编辑、复制..."

#. module: website
#. odoo-python
#: code:addons/website/models/res_users.py:0
#: model:ir.model.constraint,message:website.constraint_res_users_login_key
#, python-format
msgid "You can not have two users with the same login!"
msgstr "您不能有两个用户使用相同的登录信息！"

#. module: website
#. odoo-python
#: code:addons/website/models/website_snippet_filter.py:0
#, python-format
msgid "You can only use template prefixed by dynamic_filter_template_ "
msgstr "您只能使用以 dynamic_filter_template_ 为前缀的模板"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't duplicate the submit button of the form."
msgstr "您不能复制表单的提交按钮。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You can't remove the submit button of the form"
msgstr "您无法删除表单的提交按钮"

#. module: website
#. odoo-python
#: code:addons/website/models/website.py:0
#, python-format
msgid ""
"You cannot delete default website %s. Try to change its settings instead"
msgstr "无法删除默认网站%s。尝试更改默认网站的设置"

#. module: website
#. odoo-python
#: code:addons/website/models/website_menu.py:0
#, python-format
msgid ""
"You cannot delete this website menu as this serves as the default parent "
"menu for new websites (e.g., /shop, /event, ...)."
msgstr "不能删除此网站菜单，因为它用作新网站（例如 /shop、/event...）的默认上级菜单。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_website_form/options.js:0
#, python-format
msgid "You cannot duplicate this field."
msgstr "该字段不可重复。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_facebook_page/options.js:0
#, python-format
msgid "You didn't provide a valid Facebook link"
msgstr "您未有提供有效的 Facebook 连结"

#. module: website
#. odoo-python
#: code:addons/website/models/mixins.py:0
#, python-format
msgid "You do not have the rights to publish/unpublish"
msgstr "您没有发布/取消发布的权限"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid "You don't have permissions to edit this record."
msgstr "您没有权限编辑此记录。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.xml:0
#, python-format
msgid ""
"You have hidden this page from search results. It won't be indexed by search"
" engines."
msgstr "您已从搜索结果中隐藏此网页。它不会被搜索引擎索引。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "You may opt-out of a third-party's use of cookies by visiting the"
msgstr "您可以选择退出第三方使用 cookie，方法是访问"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_faq_collapse
msgid ""
"You should carefully review the legal statements and other conditions of use"
" of any website which you access through a link from this Website. Your "
"linking to any other off-site pages or other websites is at your own risk."
msgstr "您应仔细阅读通过本网站链接访问的任何网站的法律声明和其他使用条件。您链接到任何其他网站外网页或其他网站的风险由您自行承担。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_searchbar
msgid "You will get results from blog posts, products, etc"
msgstr "您将从博文、产品等中获得结果"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "You'll be able to create your pages later on."
msgstr "稍后您将能够创建您的网页。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_social_media/options.js:0
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
#: model_terms:ir.ui.view,arch_db:website.s_social_media
#, python-format
msgid "YouTube"
msgstr "Youtube"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
#, python-format
msgid "Your Company"
msgstr "您的公司"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_dynamic_snippet_template
msgid ""
"Your Dynamic Snippet will be displayed here... This message is displayed "
"because you did not provided both a filter and a template to use.<br/>"
msgstr "您的动态片段将显示在此处... 显示此消息是因为您没有同时提供要使用的筛选和模板。<br/>"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
#, python-format
msgid "Your Email"
msgstr "您的电子邮件"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_embed_code/000.js:0
#, python-format
msgid ""
"Your Embed Code snippet doesn't have anything to display. Click on Edit to "
"modify it."
msgstr "您的嵌入代码片段没有任何要显示的内容。单击编辑来修改它."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
#, python-format
msgid "Your Name"
msgstr "您的姓名"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/js/send_mail_form.js:0
#, python-format
msgid "Your Question"
msgstr "您的问题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_title
msgid "Your Site Title"
msgstr "您的网站标题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.new_page_template_landing_s_features
msgid ""
"Your brand is your story. We help you tell it through cohesive visual "
"identity and messaging that resonates with your audience."
msgstr "您的品牌就是您的故事。我们帮助您通过具有凝聚力的视觉识别和信息传递来讲述您的故事，引起受众的共鸣。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/resource_editor/resource_editor_warning.xml:0
#, python-format
msgid "Your changes might be lost during future Odoo upgrade."
msgstr "在将来的 Odoo 升级中，您的更改可能会丢失。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
#, python-format
msgid "Your description looks too long."
msgstr "您的描述太长了。"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/seo.js:0
#, python-format
msgid "Your description looks too short."
msgstr "您的描述太简短了。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"Your experience may be degraded if you discard those cookies, but the "
"website will still work."
msgstr "如果您丢弃这些 cookie，您的体验可能会下降，但该网站仍然可以工作运行。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_instagram_page_options
msgid ""
"Your instagram page must be public to be integrated into an Odoo website."
msgstr "您的 Instagram 页面必须是公共的，才能集成到 ERP 网站中."

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/editor/editor.js:0
#, python-format
msgid "Your modifications were saved to apply this option."
msgstr "您的修改已保存以应用此选项。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.list_hybrid
#: model_terms:ir.ui.view,arch_db:website.list_website_public_pages
msgid "Your search '"
msgstr "您的搜索”"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_timeline
msgid "Your title"
msgstr "您的标题"

#. module: website
#: model:ir.model.fields,field_description:website.field_website__social_youtube
msgid "Youtube Account"
msgstr "Youtube账号"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "Zoom"
msgstr "缩放"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom In"
msgstr "放大"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippet_options
msgid "Zoom Out"
msgstr "缩小"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"
msgstr ""
"__gads (Google)<br/>\n"
"                                            __gac (Google)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"
msgstr ""
"_ga (Google)<br/>\n"
"                                            _gat (Google)<br/>\n"
"                                            _gid (Google)<br/>\n"
"                                            _gac_* (Google)"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "a blog"
msgstr "一个博客"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "a business website"
msgstr "一个商业网站"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "a new image"
msgstr "一张新的图像"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "a pre-made Palette"
msgstr "预制调色板"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "an elearning platform"
msgstr "一个在线学习平台"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "an event website"
msgstr "一个活动网站"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "an online store"
msgstr "一个线上商店"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "and"
msgstr "and"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "and copy paste the address of the font page here."
msgstr "并在此处复制粘贴字体网页的地址。"

#. module: website
#. odoo-python
#: code:addons/website/models/website_rewrite.py:0
#, python-format
msgid "base URL of 'URL to' should not be same as 'URL from'."
msgstr ""

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "big"
msgstr "大的"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "blog"
msgstr "博客"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "breadcrumb"
msgstr "浏览路径"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "btn-outline-primary"
msgstr "btn-outline-primary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "btn-outline-secondary"
msgstr "btn-outline-secondary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "btn-primary"
msgstr "btn-primary"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "btn-secondary"
msgstr "btn-secondary"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "business"
msgstr "商业"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "button, action"
msgstr "按钮、动作"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "celebration, launch"
msgstr "庆祝,发射"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "chart, table, diagram, pie"
msgstr "柱状图, 表格, 折线图, 饼图"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "cite"
msgstr "引证"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "columns, description"
msgstr "列.描述"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "common answers, common questions"
msgstr "常见的答案，常见的问题"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "content"
msgstr "内容"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "customers, clients"
msgstr "客户,委托人"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "days"
msgstr "天数"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "develop the brand"
msgstr "发展品牌"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "e-learning platform"
msgstr "在线学习平台"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_countdown_options
msgid "e.g. /my-awesome-page"
msgstr "例如 /my-awesome-page"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
#: model_terms:ir.ui.view,arch_db:website.s_map_options
msgid "e.g. De Brouckere, Brussels, Belgium"
msgstr "例如:De Brouckere, Brussels, Belgium"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_page_properties_view_form
msgid "e.g. Home Page"
msgstr "例如 主页"

#. module: website
#: model:ir.ui.menu,name:website.menu_website_dashboard
msgid "eCommerce"
msgstr "电子商务"

#. module: website
#: model:website.configurator.feature,name:website.feature_module_elearning
msgid "eLearning"
msgstr "在线学习"

#. module: website
#. odoo-python
#: code:addons/website/controllers/form.py:0
#, python-format
msgid "email"
msgstr "电子邮件"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "evolution, growth"
msgstr "进化, 成长"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "fonts.google.com"
msgstr "fonts.google.com"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "for my"
msgstr "for my"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "forum"
msgstr "论坛"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/dialog/page_properties.xml:0
#, python-format
msgid "found(s)"
msgstr "找到"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.website_search_box
msgid "found)"
msgstr "已找到）"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.brand_promotion
msgid "free website"
msgstr "免费的网站"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "from Logo"
msgstr "从Logo"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "frontend_lang (Odoo)"
msgstr "frontend_lang (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "gallery, carousel"
msgstr "画廊，旋转木马"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "get leads"
msgstr "获得线索"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "google1234567890123456.html"
msgstr "google1234567890123456.html"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "heading, h1"
msgstr "标题, h1"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_contact
#: model_terms:ir.ui.view,arch_db:website.template_footer_descriptive
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "hero, jumbotron"
msgstr "英雄，巨无霸"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website.editor.xml:0
#, python-format
msgid "https://fonts.google.com/specimen/Roboto"
msgstr "https://fonts.google.com/specimen/Roboto"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.res_config_settings_view_form
msgid "https://www.odoo.com"
msgstr "https://www.odoo.com"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_mega_menu_odoo_menu
msgid "iPhone"
msgstr "iPhone"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"
msgstr ""
"im_livechat_previous_operator_pid (Odoo)<br/>\n"
"                                            utm_campaign (Odoo)<br/>\n"
"                                            utm_source (Odoo)<br/>\n"
"                                            utm_medium (Odoo)"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "image, media, illustration"
msgstr "图像, 媒体, 插图"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "image, picture, photo"
msgstr "图像、图片、照片"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#, python-format
msgid "in the top right corner to start designing."
msgstr "在右上角开始设计。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.template_footer_links
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.footer_custom
#: model_terms:ir.ui.view,arch_db:website.template_footer_call_to_action
#: model_terms:ir.ui.view,arch_db:website.template_footer_centered
#: model_terms:ir.ui.view,arch_db:website.template_footer_headline
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "inform customers"
msgstr "通知客户"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.show_website_info
msgid "instance of Odoo, the"
msgstr "odoo实例，该"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__ir_ui_view
msgid "ir.ui.view"
msgstr "ir.ui.view"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.bs_debug_view
msgid "link"
msgstr "链接"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "masonry, grid"
msgstr "砖石, 网格"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "menu, pricing"
msgstr "菜单，单价"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#, python-format
msgid "no value"
msgstr "无值"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "o-color-"
msgstr "o-color-"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_instagram_page_options
msgid "odoo.official"
msgstr "odoo.official"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.step_wizard
msgid "of"
msgstr "的"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "online appointment system"
msgstr "网上预约系统"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "online store"
msgstr "在线商店"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/systray_items/edit_website.js:0
#, python-format
msgid "or edit master"
msgstr "或編輯母版"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "organization, structure"
msgstr "组织, 结构"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "perfect website?"
msgstr "完美的网站？"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/website_preview/website_preview.xml:0
#: code:addons/website/static/src/components/fields/widget_iframe.xml:0
#, python-format
msgid "phone"
msgstr "电话"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "placeholder"
msgstr "占位符"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.xml:0
#, python-format
msgid "pointer to build the perfect page in 7 steps."
msgstr "分 7 步构建完美网页的指南。"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "pricing"
msgstr "定价"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "promotion, characteristic, quality"
msgstr "促销推广、特色、质量"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/components/website_loader/website_loader.js:0
#, python-format
msgid "recruitment platform"
msgstr "招聘平台"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.searchbar_input_snippet_options
msgid "results"
msgstr "结果"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_website_form_options
msgid "rows"
msgstr "行"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "schedule appointments"
msgstr "安排约会"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.js:0
#, python-format
msgid "sell more"
msgstr "销售更多"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "separator, divider"
msgstr "分隔符, 分隔符"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.cookie_policy
msgid "session_id (Odoo)<br/>"
msgstr "session_id (Odoo)<br/>"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "social media, ig, feed"
msgstr "社交媒体、IG、Feed"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "statistics, stats, KPI"
msgstr "统计数据、统计数据、KPI"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "testimonials"
msgstr "证明信"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.color_combinations_debug_view
msgid "text link"
msgstr "文本链接"

#. module: website
#: model:ir.model.fields.selection,name:website.selection__theme_ir_ui_view__inherit_id__theme_ir_ui_view
msgid "theme.ir.ui.view"
msgstr "theme.ir.ui.view"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/snippets/s_countdown/000.xml:0
#, python-format
msgid "this page"
msgstr "此页"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "valuation, rank"
msgstr "估值、排名"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.snippets
msgid "video, youtube, vimeo, dailymotion, youku"
msgstr "视频、YouTube、VIMEO、Dailymotion、优酷"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/xml/website_form_editor.xml:0
#: model:ir.model.fields,field_description:website.field_res_config_settings__website_id
#, python-format
msgid "website"
msgstr "网站"

#. module: website
#. odoo-javascript
#: code:addons/website/static/src/client_actions/configurator/configurator.xml:0
#, python-format
msgid "with the main objective to"
msgstr "主要目标是"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.qweb_500
msgid "yes"
msgstr "是"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Active"
msgstr "⌙ 有效"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_popup_options
msgid "⌙ Delay"
msgstr "⌙ 延迟"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_rating_options
msgid "⌙ Inactive"
msgstr "⌙ 停用"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_product_catalog_options
msgid "⌙ Separator"
msgstr "⌙ 九月"

#. module: website
#: model_terms:ir.ui.view,arch_db:website.s_google_map_options
msgid "⌙ Style"
msgstr "⌙ 样式"
