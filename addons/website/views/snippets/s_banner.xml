<?xml version="1.0" encoding="utf-8"?>
<odoo>

<template id="s_banner" name="Banner">
    <section class="s_banner pt96 pb96">
        <div class="container">
            <div class="row s_nb_column_fixed o_grid_mode" data-row-count="11">
                <div class="justify-content-center o_grid_item g-height-8 g-col-lg-5 col-lg-5" data-name="Box" style="z-index: 1; grid-area: 2 / 1 / 10 / 6;">
                    <h1 class="display-3">Sell Online. <strong>Easily.</strong></h1>
                    <p class="lead">This is a simple hero unit, a simple jumbotron-style component for calling extra attention to featured content or information.</p>
                    <div class="s_hr text-start pb32 pt4" data-snippet="s_hr" data-name="Separator">
                        <hr class="w-100 mx-auto"/>
                    </div>
                    <p>
                        <a t-att-href="cta_btn_href" class="btn btn-lg btn-primary">Start Now <span class="fa fa-angle-right ms-2"/></a>
                    </p>
                </div>
                <div class="o_grid_item o_grid_item_image g-col-lg-4 g-height-10 col-lg-4" style="z-index: 2; grid-area: 1 / 8 / 11 / 12;">
                    <img class="img img-fluid mx-auto shadow rounded" src="/web/image/website.s_banner_default_image_2" alt=""/>
                </div>
                <div class="o_grid_item o_grid_item_image g-col-lg-2 g-height-5 col-lg-2 d-lg-block d-none o_snippet_mobile_invisible" style="z-index: 3; grid-area: 2 / 11 / 7 / 13;">
                    <img class="img img-fluid mx-auto shadow rounded" src="/web/image/website.s_banner_default_image_3" alt=""/>
                </div>
                <div class="o_grid_item o_grid_item_image g-col-lg-3 g-height-5 col-lg-3 d-lg-block d-none o_snippet_mobile_invisible" style="z-index: 4; grid-area: 7 / 7 / 12 / 10;">
                    <img class="img img-fluid mx-auto shadow rounded" src="/web/image/website.s_banner_default_image" alt=""/>
                </div>
            </div>
        </div>
    </section>
</template>

</odoo>
