<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="82" height="60" viewBox="0 0 82 60">
  <defs>
    <rect id="path-1" width="82" height="60" x="0" y="0"/>
    <linearGradient id="linearGradient-3" x1="72.875%" x2="40.332%" y1="44.674%" y2="25.975%">
      <stop offset="0%" stop-color="#008374"/>
      <stop offset="100%" stop-color="#006A59"/>
    </linearGradient>
    <linearGradient id="linearGradient-4" x1="88.517%" x2="50%" y1="38.481%" y2="50%">
      <stop offset="0%" stop-color="#00AA89"/>
      <stop offset="100%" stop-color="#009989"/>
    </linearGradient>
    <path id="path-5" d="M13.5 34.998a.367.367 0 0 1-.13.277L7.3 40.88A.429.429 0 0 1 7 41a.429.429 0 0 1-.3-.12L.63 35.275a.367.367 0 0 1-.13-.277c0-.104.043-.196.13-.276l.652-.602a.429.429 0 0 1 .3-.12.43.43 0 0 1 .299.12L7 38.847l5.12-4.727a.429.429 0 0 1 .299-.12.43.43 0 0 1 .3.12l.65.602c.088.08.131.172.131.276z"/>
    <path id="path-7" d="M13.5.998a.367.367 0 0 1-.13.277L7.3 6.88A.429.429 0 0 1 7 7a.429.429 0 0 1-.3-.12L.63 1.275A.367.367 0 0 1 .5.998C.5.894.543.802.63.722L1.282.12a.429.429 0 0 1 .3-.12.43.43 0 0 1 .299.12L7 4.847 12.12.12a.429.429 0 0 1 .299-.12.43.43 0 0 1 .3.12l.65.602c.088.08.131.172.131.276z"/>
    <linearGradient id="linearGradient-9" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#FFF"/>
      <stop offset="32.744%" stop-color="#F0F0F0"/>
      <stop offset="100%" stop-color="#F3F3F3"/>
    </linearGradient>
    <linearGradient id="linearGradient-10" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#757575"/>
      <stop offset="100%" stop-color="#414141"/>
    </linearGradient>
  </defs>
  <g fill="none" fill-rule="evenodd" class="snippets_thumbs">
    <g class="s_parallax">
      <rect width="82" height="60" class="bg"/>
      <g class="group" opacity=".5">
        <g class="oval___oval_mask">
          <mask id="mask-2" fill="#fff">
            <use xlink:href="#path-1"/>
          </mask>
          <use fill="#79D1F2" class="mask" xlink:href="#path-1"/>
          <circle cx="65.5" cy="11.5" r="7.5" fill="#F3EC60" class="oval" mask="url(#mask-2)"/>
          <ellipse cx="68.5" cy="62" fill="url(#linearGradient-3)" class="oval" mask="url(#mask-2)" rx="26.5" ry="20"/>
          <ellipse cx="18" cy="67" fill="url(#linearGradient-4)" class="oval" mask="url(#mask-2)" rx="51" ry="32"/>
        </g>
      </g>
      <g class="center_group" transform="translate(34 10)">
        <mask id="mask-6" fill="#fff">
          <use xlink:href="#path-5"/>
        </mask>
        <use fill="#FFF" fill-rule="nonzero" class="angle_down" xlink:href="#path-5"/>
        <mask id="mask-8" fill="#fff">
          <use xlink:href="#path-7"/>
        </mask>
        <use fill="#FFF" fill-rule="nonzero" class="angle_down" transform="matrix(1 0 0 -1 0 7)" xlink:href="#path-7"/>
        <rect width="13" height="20" x=".5" y="10.5" fill="url(#linearGradient-9)" stroke="#E4E4E4" class="rectangle" rx="5"/>
        <rect width="2" height="5" x="6" y="12" fill="url(#linearGradient-10)" class="rectangle" rx="1"/>
      </g>
    </g>
  </g>
</svg>
