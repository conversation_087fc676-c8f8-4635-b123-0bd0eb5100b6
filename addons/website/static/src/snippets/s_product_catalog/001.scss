.s_product_catalog[data-vcss='001'] {
    .s_product_catalog_dish {
        // Title
        .s_product_catalog_dish_title {
            line-height: $headings-line-height;
        }
        // Description
        .s_product_catalog_dish_description {
            margin-bottom: $spacer;
        }
        &:last-child {
            .s_product_catalog_dish_description {
                margin-bottom: 0;
            }
        }
        // Dot Leaders
        .s_product_catalog_dish_dot_leaders {
            display: flex;
            flex-grow: 1;
            align-items: center;
            &::after {
                content: '';
                margin-left: $spacer/2;
                flex: 1 0 auto;
                border-bottom: 1px dotted;
            }
        }
    }
}
