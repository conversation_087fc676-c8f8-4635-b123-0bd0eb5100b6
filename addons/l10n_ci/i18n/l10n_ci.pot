# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ci
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-15 14:48+0000\n"
"PO-Revision-Date: 2024-04-15 14:48+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_operations
msgid "02. Operations realised"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_taxable_turnover
msgid "03. Taxable turnover"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_adjustment
msgid "04. Adjustment of previously deducted vat to be repaid"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_gross
msgid "05. Total gross VAT"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_deduction
msgid "06. Deductions"
msgstr ""

#. module: l10n_ci
#: model:ir.model,name:l10n_ci.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_ci
#: model:account.report.column,name:l10n_ci.account_tax_report_ci_base
msgid "Base"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_exempt_conventional
msgid "Conventional exempt operations"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_asked_reimbursement
msgid "Credit asked to be reimbursed"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_to_report
msgid "Credit to report"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_deductible
msgid "Deductible VAT"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_exportation
msgid "Exportations"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_tva_credit
msgid "Last month's credit reported"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_exempt_legal
msgid "Legal exempt operations"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_net_to_pay
msgid "Net VAT to pay (05 - 06)"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_self_delivery_18
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_taxable_turnover_18
msgid "Normal rate"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_non_taxed
msgid "Other non taxed operations"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_self_delivery_9
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_taxable_turnover_9
msgid "Reduced rate"
msgstr ""

#. module: l10n_ci
#. odoo-python
#: code:addons/l10n_ci/models/template_ci_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr ""

#. module: l10n_ci
#. odoo-python
#: code:addons/l10n_ci/models/template_ci.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_self_delivery
msgid "Self delivery or service"
msgstr ""

#. module: l10n_ci
#: model:account.report.column,name:l10n_ci.account_tax_report_ci_tax
msgid "Tax"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_taxable_18
msgid "Taxable - normal rate"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_taxable_9
msgid "Taxable - reduced rate"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_taxable
msgid "Taxable operations"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_operations_total
msgid "Total amount of operations"
msgstr ""

#. module: l10n_ci
#: model:account.report,name:l10n_ci.account_tax_report_ci
msgid "VAT Report"
msgstr ""
