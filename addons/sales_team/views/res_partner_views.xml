<?xml version="1.0"?>
<odoo>
    <record id="res_partner_view_team" model="ir.ui.view">
        <field name="name">res.partner.view.team</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form" />
        <field name="arch" type="xml">
            <xpath expr="//page[@name='sales_purchases']//field[@name='user_id']" position="after">
                <field name="team_id" invisible="1"/>
                <field name="team_id" groups="base.group_no_one" context="{'kanban_view_ref': 'sales_team.crm_team_view_kanban'}"/>
            </xpath>
            <field name="parent_id" position="attributes">
                <attribute name="context">{'default_is_company': True, 'show_vat': True, 'default_user_id': user_id, 'default_team_id': team_id}</attribute>
            </field>
        </field>
    </record>
</odoo>
