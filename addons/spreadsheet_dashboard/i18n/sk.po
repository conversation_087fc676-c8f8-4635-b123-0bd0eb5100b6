# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 12:32+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kópia)"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__access_token
msgid "Access Token"
msgstr "Prístupový token"

#. module: spreadsheet_dashboard
#: model:res.groups,name:spreadsheet_dashboard.group_dashboard_manager
msgid "Admin"
msgstr "Admin"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
#, python-format
msgid "An error occured while loading the dashboard"
msgstr ""

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_search_panel/mobile_search_panel.xml:0
#, python-format
msgid "BACK"
msgstr ""

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_search_panel/mobile_search_panel.js:0
#, python-format
msgid "Choose a dashboard...."
msgstr ""

#. module: spreadsheet_dashboard
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_configuration
msgid "Configuration"
msgstr "Konfigurácia"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard_share
msgid "Copy of a shared dashboard"
msgstr ""

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__create_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__create_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__create_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__create_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__dashboard_ids
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__dashboard_id
#: model:ir.module.category,name:spreadsheet_dashboard.dashboard_management
msgid "Dashboard"
msgstr "Riadiaci panel"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__dashboard_group_id
msgid "Dashboard Group"
msgstr ""

#. module: spreadsheet_dashboard
#: model:ir.actions.act_window,name:spreadsheet_dashboard.spreadsheet_dashboard_action_configuration_dashboards
#: model:ir.actions.client,name:spreadsheet_dashboard.ir_actions_dashboard_action
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_configuration_dashboards
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_dashboard
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_root
#: model_terms:ir.ui.view,arch_db:spreadsheet_dashboard.spreadsheet_dashboard_container_view_list
msgid "Dashboards"
msgstr "Riadiace panely"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__display_name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__display_name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__excel_export
msgid "Excel Export"
msgstr ""

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_finance
msgid "Finance"
msgstr "Financie"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__group_ids
msgid "Group"
msgstr "Skupina"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard_group
msgid "Group of dashboards"
msgstr ""

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_hr
msgid "Human Resources"
msgstr "Ľudské zdroje"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__id
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__id
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__id
msgid "ID"
msgstr "ID"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__write_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__write_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__write_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__write_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
#, python-format
msgid "Loading..."
msgstr "Nahrávanie..."

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_logistics
msgid "Logistics"
msgstr "Logistika"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__name
msgid "Name"
msgstr "Meno"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
#, python-format
msgid "No available dashboard"
msgstr ""

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_figure_container/mobile_figure_container.xml:0
#, python-format
msgid ""
"Only chart figures are displayed in small screens but this dashboard doesn't"
" contain any"
msgstr ""

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_sales
msgid "Sales"
msgstr "Predaj"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__sequence
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__sequence
msgid "Sequence"
msgstr "Postupnosť"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_project
msgid "Services"
msgstr "Služby"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard
msgid "Spreadsheet Dashboard"
msgstr ""

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__spreadsheet_data
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__spreadsheet_data
msgid "Spreadsheet Data"
msgstr ""

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__spreadsheet_binary_data
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__spreadsheet_binary_data
msgid "Spreadsheet file"
msgstr ""

#. module: spreadsheet_dashboard
#: model_terms:ir.ui.view,arch_db:spreadsheet_dashboard.spreadsheet_dashboard_container_view_form
msgid "Spreadsheets"
msgstr ""

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__thumbnail
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__thumbnail
msgid "Thumbnail"
msgstr "MIniatúra"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__full_url
msgid "URL"
msgstr "URL"

#. module: spreadsheet_dashboard
#: model:ir.module.category,description:spreadsheet_dashboard.dashboard_management
msgid "User access level for Dashboard module"
msgstr ""

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_website
msgid "Website"
msgstr "Webstránka"

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard_group.py:0
#, python-format
msgid "You cannot delete %s as it is used in another module."
msgstr ""

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard_share.py:0
#, python-format
msgid "You don't have access to this dashboard. "
msgstr ""
