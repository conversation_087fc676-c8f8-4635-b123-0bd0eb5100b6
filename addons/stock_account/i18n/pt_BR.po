# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_account
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid " Product cost updated from %(previous)s to %(new_cost)s."
msgstr " Custo do produto atualizado de %(previous)s para %(new_cost)s."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_quant.py:0
#, python-format
msgid " [Accounted on %s]"
msgstr " [Contabilizado em %s]"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "%(user)s changed cost from %(previous)s to %(new_price)s - %(product)s"
msgstr ""
"%(user)s alterou o custo de %(previous)s para %(new_price)s - %(product)s"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid ""
"%(user)s changed stock valuation from  %(previous)s to %(new_value)s - "
"%(product)s"
msgstr ""
"%(user)s alterou a valoração de estoque de  %(previous)s para %(new_value)s "
"- %(product)s"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid ""
")\n"
"                            <small class=\"mx-2 fst-italic\">Use a negative added value to record a decrease in the product value</small>"
msgstr ""
")\n"
"                            <small class=\"mx-2 fst-italic\">Usar um valor agregado negativo para registrar uma redução no valor do produto</small>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "6.00"
msgstr "6,00"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "<b>Set other input/output accounts on specific </b>"
msgstr "<b>Definir outras contas de entrada/saída especificamente em </b>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_picking
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr "<span class=\"o_stat_text\">Valoração</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Product</span>"
msgstr "<span>Produto</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Quantity</span>"
msgstr "<span>Quantidade</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>SN/LN</span>"
msgstr "<span>NS/NL</span>"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modelo de plano de contas"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__account_move_ids
msgid "Account Move"
msgstr "Movimentação contábil"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Account Stock Properties"
msgstr "Propriedades da conta de estoque"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_request_count__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__date
msgid "Accounting Date"
msgstr "Data contábil"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_form_inherit
msgid "Accounting Entries"
msgstr "Lançamentos contábeis"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_location_form_inherit
msgid "Accounting Information"
msgstr "Informações financeiras"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Add Manual Valuation"
msgstr "Adicionar valoração manual"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Add additional cost (transport, customs, ...) in the value of the product."
msgstr ""
"Adicionar custo adicional (transporte, alfândega...) no valor do produto"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Added Value"
msgstr "Valor adicionado"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__added_value
msgid "Added value"
msgstr "Valor adicionado"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_res_config_settings__module_stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Affect landed costs on reception operations and split them among products to"
" update their cost price."
msgstr ""
"Impacta os custos adicionais nas operações de recebimento e dividi-los entre"
" os produtos para atualizar seu preço de custo."

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_analytic_account
msgid "Analytic Account"
msgstr "Conta analítica"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__analytic_account_line_ids
msgid "Analytic Account Line"
msgstr "Linha de contas analíticas"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_analytic_plan
msgid "Analytic Plans"
msgstr "Planos analíticos"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__real_time
msgid "Automated"
msgstr "Automatizado"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__group_stock_accounting_automatic
msgid "Automatic Stock Accounting"
msgstr "Contabilidade de estoque automática"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__avg_cost
msgid "Average Cost"
msgstr "Custo médio"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__average
msgid "Average Cost (AVCO)"
msgstr "Custo médio (AVCO)"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "BC46282798"
msgstr "BC46282798"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "Bacon"
msgstr "Bacon"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Cancel"
msgstr "Cancelar"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Cannot find a stock input account for the product %s. You must define one on"
" the product category, or on the location, before processing this operation."
msgstr ""
"Não é possível encontrar uma conta de entrada de estoque para o produto %s. "
"Você deve definir uma na categoria de produto ou no local antes de processar"
" esta operação."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Cannot find a stock output account for the product %s. You must define one "
"on the product category, or on the location, before processing this "
"operation."
msgstr ""
"Não é possível encontrar uma conta de saída de estoque para o produto %s. "
"Você deve definir uma na categoria de produto ou no local antes de processar"
" esta operação."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"Changing your cost method is an important change that will impact your "
"inventory valuation. Are you sure you want to make that change?"
msgstr ""
"Mudar seu método de custeio é uma mudança importante que terá impacto na "
"valoração de seu inventário. Tem certeza de que deseja fazer essa alteração?"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quantity_history_inherit_stock_account
msgid "Choose a date to get the valuation at that date"
msgstr "Escolha uma data para obter a valoração nessa data"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_account_move_line__cogs_origin_id
msgid "Cogs Origin"
msgstr "Origem dos custos de mercadorias vendidas"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__company_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__company_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Company"
msgstr "Empresa"

#. module: stock_account
#: model:ir.model,name:stock_account.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"Configuration error. Please configure the price difference account on the "
"product or its category to process this operation."
msgstr ""
"Erro de configuração. Configure a conta de diferença de preço no produto ou "
"em sua categoria para processar essa operação."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product__cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template__cost_method
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__cost_method
msgid "Costing Method"
msgstr "Método de custeio"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Costing method change for product category %s: from %s to %s."
msgstr ""
"Mudança de método de custeio para categoria de produto %s: de %s para %s."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_id
msgid "Counterpart Account"
msgstr "Conta de contrapartida"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_input_categ_id
msgid ""
"Counterpart journal items for all incoming stock moves will be posted in this account, unless there is a specific valuation account\n"
"                set on the source location. This is the default value for all products in this category. It can also directly be set on each product."
msgstr ""
"Os itens do diário de contrapartida para todas as movimentações de entrada de estoque serão lançados nesta conta, a menos que haja uma conta de valoração específica\n"
"                definida no local de origem. Este é o valor padrão para todos os produtos desta categoria. Também pode ser definido diretamente em cada produto."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_picking__country_code
msgid "Country Code"
msgstr "Código do país"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_date
msgid "Created on"
msgstr "Criado em"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__currency_id
msgid "Currency"
msgstr "Moeda"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_quantity_svl
msgid "Current Quantity"
msgstr "Quantidade atual"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_value_svl
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Current Value"
msgstr "Valor atual"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Date"
msgstr "Data"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_quant__accounting_date
msgid ""
"Date at which the accounting entries will be created in case of automated "
"inventory valuation. If empty, the inventory date will be used."
msgstr ""
"Data em que os lançamentos contábeis serão criados no caso de valoração "
"automática de estoque. Se estiver vazio, a data do inventário será usada."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Unidade de medida padrão usada para todas as operações de estoque."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__description
msgid "Description"
msgstr "Descrição"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__group_lot_on_invoice
msgid "Display Lots & Serial Numbers on Invoices"
msgstr "Exibir número de série e lote em faturas"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__display_name
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: stock_account
#: model:res.groups,name:stock_account.group_lot_on_invoice
msgid "Display Serial & Lot Number on Invoices"
msgstr "Exibir número de série e lote em faturas"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"Due to a change of product category (from %s to %s), the costing method has "
"changed for product template %s: from %s to %s."
msgstr ""
"Devido a uma mudança de categoria de produto (de %s para %s), o método de "
"cálculo de custos mudou para o modelo de produto %s: de %s para %s."

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__fifo
msgid "First In First Out (FIFO)"
msgstr "Primeiro a entrar, primeiro a sair (PEPS)"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Group by..."
msgstr "Agrupar por..."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Has Remaining Qty"
msgstr "Possui qtd. restante"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__id
msgid "ID"
msgstr "ID"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Incoming"
msgstr "Entrada"

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.inventory_aging_action
#: model:ir.ui.menu,name:stock_account.menu_inventory_aging
msgid "Inventory Aging"
msgstr "Envelhecimento de estoque"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_location
msgid "Inventory Locations"
msgstr "Locais de inventário"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/account_chart_template.py:0
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_product__valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template__valuation
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__property_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form_stock
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
#, python-format
msgid "Inventory Valuation"
msgstr "Valoração de inventário"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__account_move_line_id
msgid "Invoice Line"
msgstr "Linha da fatura"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__price_diff_value
msgid "Invoice value correction with invoice currency"
msgstr "Correção do valor da fatura com a moeda da fatura"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_journal_id
msgid "Journal"
msgstr "Diário"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__account_move_id
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Journal Entry"
msgstr "Lançamento de diário"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move_line
msgid "Journal Item"
msgstr "Item de diário"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__module_stock_landed_costs
msgid "Landed Costs"
msgstr "Custos adicionais"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_id
msgid "Linked To"
msgstr "Vinculado a"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Lots & Serial numbers will appear on the invoice"
msgstr "Os números de lote e de série aparecerão na fatura"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__manual_periodic
msgid "Manual"
msgstr "Manual"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Manual Stock Valuation: %s."
msgstr "Valoração de estoque manual: %s."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Manual Stock Valuation: No Reason Given."
msgstr "Valoração de estoque manual: nenhuma razão fornecida."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,help:stock_account.field_product_product__valuation
#: model:ir.model.fields,help:stock_account.field_product_template__valuation
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr ""
"Manual: os lançamentos contábeis para avaliar o estoque não são lançados automaticamente.\n"
"        Automatizado: um lançamento contábil é criado automaticamente para avaliar o estoque quando um produto entra ou sai da empresa.\n"
"        "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Moved Quantity"
msgstr "Quantidade movida"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value
msgid "New value"
msgstr "Novo valor"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value_by_qty
msgid "New value by quantity"
msgstr "Novo valor por quantidade"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
msgid "Other Info"
msgstr "Outras informações"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Outgoing"
msgstr "Saída"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_template
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Product"
msgstr "Produto"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_category
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__categ_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Product Category"
msgstr "Categoria de produtos"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Movimentações do produto (linha da movimentação de estoque)"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
#, python-format
msgid "Product Revaluation"
msgstr "Revaloração de produto"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_tmpl_id
msgid "Product Template"
msgstr "Modelo de produto"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_product
msgid "Product Variant"
msgstr "Variante do produto"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Product value manually modified (from %s to %s)"
msgstr "Valor do produto modificado manualmente (de %s para %s)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__quantity
msgid "Quantity"
msgstr "Quantidade"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__quantity_svl
msgid "Quantity Svl"
msgstr "Quantidade Svl"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quant
msgid "Quants"
msgstr "Quants"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason"
msgstr "Motivo"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason of the revaluation"
msgstr "Razão da revaloração"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__warehouse_id
msgid "Receipt WH"
msgstr "Recibo WH"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__reference
msgid "Reference"
msgstr "Referência"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_id
msgid "Related product"
msgstr "Produto relacionado"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_qty
msgid "Remaining Qty"
msgstr "Quantidade restante"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_value
msgid "Remaining Value"
msgstr "Valor restante"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking
msgid "Return Picking"
msgstr "Separação de devolução"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "Linha de coleta de devolução"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "Revaluation of %s"
msgstr "Revaloração de %s"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Revalue"
msgstr "Revaloração"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__standard
msgid "Standard Price"
msgstr "Preço padrão"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,help:stock_account.field_product_product__cost_method
#: model:ir.model.fields,help:stock_account.field_product_template__cost_method
#: model:ir.model.fields,help:stock_account.field_stock_quant__cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr ""
"Preço padrão: os produtos são avaliados pelo custo padrão definido no produto.\n"
"Custo médio: os produtos são avaliados pelo custo médio ponderado.\n"
"Primeiro a entrar, primeiro a sair (PEPS): os produtos são avaliados supondo-se que aqueles que entrarem primeiro na empresa também sairão primeiro."

#. module: stock_account
#: model:res.groups,name:stock_account.group_stock_accounting_automatic
msgid "Stock Accounting Automatic"
msgstr "Contabilidade de estoque automática"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr "Conta de entrada de estoque"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_journal
msgid "Stock Journal"
msgstr "Diário de estoque"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_account_payment__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_move_id
msgid "Stock Move"
msgstr "Movimentação de estoque"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr "Conta de saída do estoque"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "Histórico da quantidade em estoque"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Relatório de reposição de estoque"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "Estoque - solicitar uma contagem de estoque"

#. module: stock_account
#. odoo-javascript
#: code:addons/stock_account/static/src/stock_account_forecasted/forecasted_header.js:0
#: model:ir.actions.act_window,name:stock_account.stock_valuation_layer_action
#: model:ir.actions.act_window,name:stock_account.stock_valuation_layer_report_action
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quant_tree_editable_inherit
#, python-format
msgid "Stock Valuation"
msgstr "Valoração de estoque"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "Conta de valoração de estoque"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_in_account_id
msgid "Stock Valuation Account (Incoming)"
msgstr "Conta de valoração de estoque (entrada)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_out_account_id
msgid "Stock Valuation Account (Outgoing)"
msgstr "Conta de valoração de estoque (saída)"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_move_line__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_payment__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_product_product__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr "Camada de valoração de estoque"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product__company_currency_id
msgid ""
"Technical field to correctly show the currently selected company's currency "
"that corresponds to the totaled value of the product's valuation layers"
msgstr ""
"Campo técnico para mostrar corretamente a moeda da empresa atualmente "
"selecionada que corresponde ao valor total das camadas de valoração do "
"produto"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_picking__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"O código ISO do país de dois caracteres. \n"
"Use este campo para fazer uma busca."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"The Stock Input and/or Output accounts cannot be the same as the Stock "
"Valuation account."
msgstr ""
"As contas de entrada e/ou saída de estoque não podem ser iguais à conta de "
"valoração de estoque."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"The action leads to the creation of a journal entry, for which you don't "
"have the access rights."
msgstr ""
"A ação leva à criação de um lançamento de diário para o qual você não tem "
"direitos de acesso."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "The added value doesn't have any impact on the stock valuation"
msgstr "O valor agregado não tem impacto na valoração do estoque"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent state: some are entering and other "
"are leaving the company."
msgstr ""
"As linhas de movimentação não estão em um estado consistente: algumas estão "
"entrando e outras saindo da empresa."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent states: they are doing an "
"intercompany in a single step while they should go through the intercompany "
"transit location."
msgstr ""
"As linhas de movimentação não estão em estados consistentes: elas estão "
"fazendo um intercompany em uma única etapa, enquanto deveriam passar pelo "
"local de trânsito intercompany."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"The move lines are not in a consistent states: they do not share the same "
"origin or destination company."
msgstr ""
"As linhas de movimentação não estão em estados consistentes: elas não "
"compartilham a mesma origem ou empresa de destino."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid ""
"The value of a stock valuation layer cannot be negative. Landed cost could "
"be use to correct a specific transfer."
msgstr ""
"O valor de uma valoração de estoque não pode ser negativo. Custos adicionais"
" podem ser usados para corrigir uma transferência específica."

#. module: stock_account
#: model_terms:ir.actions.act_window,help:stock_account.inventory_aging_action
#: model_terms:ir.actions.act_window,help:stock_account.stock_valuation_layer_action
#: model_terms:ir.actions.act_window,help:stock_account.stock_valuation_layer_report_action
msgid ""
"There are no valuation layers. Valuation layers are created when there are "
"product moves that impact the valuation of the stock."
msgstr ""
"Não há camadas de valoração. As camadas de valoração são criadas quando há "
"movimentações de produtos que afetam a valoração do estoque."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Moved Quantity"
msgstr "Quantidade total movida"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_report_tree
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Remaining Quantity"
msgstr "Quantidade total restante"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_report_tree
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Remaining Value"
msgstr "Valor total restante"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__total_value
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__value
#: model_terms:ir.ui.view,arch_db:stock_account.product_product_stock_tree_inherit_stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quant_tree_editable_inherit
msgid "Total Value"
msgstr "Valor total"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_picking
msgid "Transfer"
msgstr "Transferência"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,help:stock_account.field_stock_return_picking_line__to_refund
msgid ""
"Trigger a decrease of the delivered/received quantity in the associated Sale"
" Order/Purchase Order"
msgstr ""
"Disparar uma diminuição da quantidade entregue/recebida no pedido de "
"venda/pedido de compra associado"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_product_stock_tree_inherit_stock_account
msgid "Unit Cost"
msgstr "Custo unitário"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__unit_cost
msgid "Unit Value"
msgstr "Valor unitário"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__uom_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_uom_name
msgid "Unit of Measure"
msgstr "Unidade de medida"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,field_description:stock_account.field_stock_return_picking_line__to_refund
msgid "Update quantities on SO/PO"
msgstr "Atualizar quantidades em PV/PC"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_in_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved from an internal location into this location, instead of the "
"generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"Usado para valoração do estoque em tempo real. Quando aparece em um local "
"virtual (tipo não interno), esta conta será usada para manter o valor de "
"produtos que estão sendo movidos de um local interno para este local, em vez"
" da conta genérica de saída de estoque definida no produto. Isto não tem "
"efeito para locais internos."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_out_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved out of this location and into an internal location, instead of "
"the generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"Usado para valoração do estoque em tempo real. Quando aparece em um local "
"virtual (do tipo não interno), esta conta será usada para manter o valor de "
"produtos que estão sendo movidos para fora do local e em um local interno, "
"em vez da conta genérica de saída de estoque definida no produto. Isto não "
"tem efeito para locais internos."

#. module: stock_account
#: model:ir.ui.menu,name:stock_account.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quant_tree_editable_inherit
msgid "Valuation"
msgstr "Valoração"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__company_currency_id
msgid "Valuation Currency"
msgstr "Moeda de valoração"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_product_stock_tree_inherit_stock_account
msgid "Valuation Report"
msgstr "Relatório de valoração"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quantity_history_inherit_stock_account
msgid "Valuation at Date"
msgstr "Valoração na data"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Valuation method change for product category %s: from %s to %s."
msgstr ""
"Método de valoração alterado para a categoria do produto %s: de %s para %s."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__value
msgid "Value"
msgstr "Valor"

#. module: stock_account
#. odoo-javascript
#: code:addons/stock_account/static/src/stock_account_forecasted/forecasted_header.xml:0
#, python-format
msgid "Value On Hand:"
msgstr "Valor em mãos:"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__value_svl
msgid "Value Svl"
msgstr "Valor Svl"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "Warning"
msgstr "Aviso"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_valuation_account_id
msgid ""
"When automated inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""
"Quando a valoração de estoque automatizada é habilitada em um produto, essa "
"conta manterá o valor atual dos produtos."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_output_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the destination location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""
"Ao fazer a valoração de estoque automatizada, itens de diário de contrapartida para todas as movimentações de estoque de saída serão lançados nesta conta,\n"
"                a menos que haja uma conta de valoração específica definida no local de destino. Este é o valor padrão para todos os produtos nesta categoria.\n"
"                Também pode ser definida diretamente em cada produto."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_journal
msgid ""
"When doing automated inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""
"Ao fazer a valoração de estoque automatizada, este é o diário financeiro no "
"qual os lançamentos serão feitos automaticamente quando as movimentações de "
"estoque forem processadas."

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer_revaluation
msgid "Wizard model to reavaluate a stock inventory for a product"
msgstr "Modelo de assistente para revalorar o estoque de um produto"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "You cannot revalue a product with a standard cost method."
msgstr "Você não pode revalorar um produto com um método de custeio padrão."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
#, python-format
msgid "You cannot revalue a product with an empty or negative stock."
msgstr "Você não pode revalorar um produto com estoque vazio ou negativo."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You cannot update the cost of a product in automated valuation as it leads "
"to the creation of a journal entry, for which you don't have the access "
"rights."
msgstr ""
"Você não pode atualizar o custo de um produto na revaloração automática, "
"pois isso leva à criação de um lançamento de diário, para o qual você não "
"tem direitos de acesso."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You don't have any input valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""
"Você não tem nenhuma conta de valoração de entrada definida em sua categoria"
" de produto. Você deve definir uma antes de processar esta operação."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You don't have any output valuation account defined on your product "
"category. You must define one before processing this operation."
msgstr ""
"Você não tem nenhuma conta de valoração de saída definida em sua categoria "
"de produto. Você deve definir uma antes de processar esta operação."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid ""
"You don't have any stock input account defined on your product category. You"
" must define one before processing this operation."
msgstr ""
"Você não tem nenhuma conta de entrada de estoque definida em sua categoria "
"de produto. É necessário definir uma antes de executar esta operação."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"You don't have any stock journal defined on your product category, check if "
"you have installed a chart of accounts."
msgstr ""
"Você não tem nenhum diário de estoque definido em sua categoria de produto. "
"Verifique se você instalou um plano de contas."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/stock_move.py:0
#, python-format
msgid ""
"You don't have any stock valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""
"Você não tem nenhuma conta de valoração de estoque definida em sua categoria"
" de produto. Você deve definir uma antes de processar esta operação."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#, python-format
msgid "You must set a counterpart account on your product category."
msgstr ""
"Você deve definir uma conta de contrapartida em sua categoria de produto."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "by"
msgstr "por"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "for"
msgstr "para"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "locations"
msgstr "locais"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "units"
msgstr "unidades"
