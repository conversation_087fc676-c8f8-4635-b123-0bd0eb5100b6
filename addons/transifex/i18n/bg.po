# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* transifex
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2023
# KeyVillage, 2023
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: transifex
#: model:ir.model,name:transifex.model_base
msgid "Base"
msgstr "Основа"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__source
msgid "Code"
msgstr "Код"

#. module: transifex
#: model:ir.model,name:transifex.model_transifex_code_translation
msgid "Code Translation"
msgstr ""

#. module: transifex
#. odoo-javascript
#: code:addons/transifex/static/src/views/fields/translation_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_tree_view
#, python-format
msgid "Contribute"
msgstr ""

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__display_name
msgid "Display Name"
msgstr "Име за Показване"

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_view_search
msgid "Group By"
msgstr "Групиране по"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__id
msgid "ID"
msgstr "ID"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__lang
msgid "Language"
msgstr "Език"

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__module
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_view_search
msgid "Module"
msgstr "Модул"

#. module: transifex
#: model:ir.model.fields,help:transifex.field_transifex_code_translation__module
msgid "Module this term belongs to"
msgstr "Модул, към който принадлежи този срок"

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_view_search
msgid "Not Translated"
msgstr ""

#. module: transifex
#: model:ir.model.fields,help:transifex.field_transifex_code_translation__transifex_url
msgid "Propose a modification in the official version of Odoo"
msgstr ""

#. module: transifex
#. odoo-javascript
#: code:addons/transifex/static/src/views/reload_code_translations_views.xml:0
#, python-format
msgid "Reload"
msgstr "Презаредете"

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_view_search
msgid "Search Code Translations"
msgstr ""

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_tree_view
msgid "Transifex"
msgstr "Transifex"

#. module: transifex
#: model_terms:ir.ui.view,arch_db:transifex.transifex_code_translation_tree_view
msgid "Transifex Code Translation"
msgstr ""

#. module: transifex
#: model:ir.actions.server,name:transifex.action_code_translations
#: model:ir.ui.menu,name:transifex.menu_transifex_code_translations
msgid "Transifex Code Translations"
msgstr ""

#. module: transifex
#: model:ir.model,name:transifex.model_transifex_translation
msgid "Transifex Translation"
msgstr ""

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__transifex_url
msgid "Transifex URL"
msgstr "Transifex URL"

#. module: transifex
#: model:ir.actions.server,name:transifex.transifex_code_translation_reload_ir_actions_server
msgid "Transifex: Reload code translations"
msgstr ""

#. module: transifex
#: model:ir.model.fields,field_description:transifex.field_transifex_code_translation__value
msgid "Translation Value"
msgstr "Стойност на превод"
