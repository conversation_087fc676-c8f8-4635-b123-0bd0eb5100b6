# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_pt
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.1alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-11-17 12:51+0000\n"
"PO-Revision-Date: 2022-11-17 13:53+0100\n"
"Last-Translator: <PERSON> (rigr-odoo) <<EMAIL>>\n"
"Language-Team: \n"
"Language: pt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_base_section_1
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_1
msgid "1 - Transmissions of goods and services on which you paid tax"
msgstr "1 - Transmissões de bens e prestações de serviço em que liquidou imposto"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_base_10
msgid "2 - [10] Intra-community acquisitions of goods and assimilated operations"
msgstr "2 - [10] Acquisições intracomunitátiras de bens et operações assimiladas"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_11
msgid "2 - [11] Intra-community acquisitions of goods and assimilated operations"
msgstr "2 - [11] Acquisições intracomunitátiras de bens et operações assimiladas"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_base_16_tag
#: model:account.report.line,name:l10n_pt.trp_base_16
msgid "3 - [16] Services rendered by taxable persons from other Member States, for which the tax was paid by the declarant"
msgstr "3 - [16] Prestações de serviços efetuadas por sujeitos passivos de outrus Estados Membros, cujo imposto foi liquidado pelo declarante"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_17_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_17
msgid "3 - [17] Services rendered by taxable persons from other Member States, for which the tax was paid by the declarant"
msgstr "3 - [17] Prestações de serviços efetuadas por sujeitos passivos de outrus Estados Membros, cujo imposto foi liquidado pelo declarante"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_base_18_tag
#: model:account.report.line,name:l10n_pt.trp_base_18
msgid "4 - [18] Imports of goods for which the tax was assessed by the declarant (No. 8 of Article 27 of CIVA)"
msgstr "4 - [18] Importações de bens cujo imposto foi liquidado pelo declarante (n°8 do artigo 27.° do CIVA)"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_19_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_19
msgid "4 - [19] Imports of goods on which tax has been assessed by the declarant (n°8 of article 27.° of the CIVA)"
msgstr "4 - [19] Importações de bens cujo imposto foi liquidado pelo declarante (n°8 do artigo 27.° do CIVA)"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_tax_favor_company5
msgid "5 - Tax deductible"
msgstr "5 - Imposto dedutível"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_company_40_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_company_40
msgid "6 - [40] Monthly, quarterly and yearly regularizations"
msgstr "6 - [40] Regularizações mensais, trimestrais e anuais"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_41_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_41
msgid "6 - [41] Monthly, quarterly and yearly regularizations"
msgstr "6 - [41] Regularizações mensais, trimestrais e anuais"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_company_61_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_company_61
msgid "7 - [61] Excess to carry forward from the previous period (Field 96 dof previous declaration - n.°4 of article 22.°)"
msgstr "7 - [61] Excesso a reportar do período anterior (Campo 96 da declaração anterior - n.°4 do artigo 22.°)"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_company_65_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_company_65
msgid "8 - [65] Attachment - (see Table 03)"
msgstr "8 - [65] Anexo - (ver Quadro 03)"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_66_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_66
msgid "8 - [66] Attachment - (see Table 03)"
msgstr "8 - [66] Anexo - (ver Quadro 03)"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_company_67_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_company_67
msgid "9 - [67] Attachment - (see Table 03)"
msgstr "9 - [67] Anexo - (ver Quadro 03)"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_68_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_68
msgid "9 - [68] Attachment - (see Table 03)"
msgstr "9 - [68] Anexo - (ver Quadro 03)"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_quadro_06A_A
msgid "A - Operations located in Portugal where, as the acquirer, you have paid the VAT due"
msgstr "A - Operações localizadas em Portugal em que, na qualidade de adquirente, liquidou o IVA devido"

#. module: l10n_pt
#: model:ir.model,name:l10n_pt.model_account_account
msgid "Account"
msgstr "Conta"

#. module: l10n_pt
#: model:ir.model,name:l10n_pt.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modelo de Plano de Contas"

#. module: l10n_pt
#: model_terms:ir.ui.view,arch_db:l10n_pt.l10n_pt_view_account_form
msgid "Account code"
msgstr "Código da conta"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_quadro_06A_B
msgid "B - Transactions in which you assessed the VAT due by applying the reverse charge rule"
msgstr "B - Operações em que liquidou o IVA devido por aplicação da regra de inversão do sujeito passivo"

#. module: l10n_pt
#: model:account.report.column,name:l10n_pt.tax_report_balance
msgid "Balance"
msgstr "Balanço"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_base
msgid "Base"
msgstr "Base tributável"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_103_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_103
msgid "C - [103] Operations referred to in subparagraphs f) and g) of N.°3 of the article 3.°E subparagraph a) and b) of N.°2 of the article 4.° of the CIVA"
msgstr "C - [103] Operações referidas nas alíneas f) et g) do N.°3 do artigo 3.°E alíneas a) e b) do N.°2 do artigo 4.° do CIVA"

#. module: l10n_pt
#: model:ir.model.fields,field_description:l10n_pt.field_account_account__account_fiscal_country_id
msgid "Country Code"
msgstr "Código do país"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_104_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_104
msgid "D - [104] Operations referred to in subparagraphs a), b) and c) of the article 42.° of the CIVA"
msgstr "D - [104] Operações referidas nas alíneas a), b) e c) do artigo 42.° do CIVA"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_quadro_06A
msgid "Table 06A"
msgstr "Quadro 06A"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state
msgid "Tax in favor of the State"
msgstr "Imposto a favor do Estado"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_tax_favor_company
msgid "Tax in favor of the taxable entity"
msgstr "Imposto a favor do sujeito passivo"

#. module: l10n_pt
#: model:account.report,name:l10n_pt.tax_report_pt
msgid "Tax report"
msgstr "Declaração Periódica de IVA"

#. module: l10n_pt
#: model:ir.model.fields,field_description:l10n_pt.field_account_account__l10n_pt_taxonomy_code
#: model:ir.model.fields,field_description:l10n_pt.field_account_account_template__l10n_pt_taxonomy_code
#: model_terms:ir.ui.view,arch_db:l10n_pt.l10n_pt_view_account_form
msgid "Taxonomy code"
msgstr "Código da Taxonomia"

#. module: l10n_pt
#: model:ir.model,name:l10n_pt.model_account_account_template
msgid "Templates for Accounts"
msgstr "Modelos de contas"

#. module: l10n_pt
#: model:ir.model.fields,help:l10n_pt.field_account_account__account_fiscal_country_id
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_totais
msgid "Totals"
msgstr "Totais"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_100_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_100
msgid "[100] Property acquisition with waiver of the exemption (Decree-Law 21/2007)"
msgstr "[100] Aquiosoção de imóveis com renúncia à isenção (Decreto-Lei 21/2007)"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_101_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_101
msgid "[101] Metal [Subparagraph i) of n.°1 of the article 2.° of the CIVA]"
msgstr "[101] Sucatas [Alínea i) do n.°1 do artigo 2.° do CIVA]"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_102_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_102
msgid "[102] Construction services[Subparagraph j) of n.°1 of the article 2.° of the CIVA]"
msgstr "[102] Serviços de construção civil [Alínea j) do n.°1 do artigo 2.° do CIVA]"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_105_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_105
msgid "[105] Greenhouse gas emissions [Subparagraph l) of n.°1 of the article 2.° of the CIVA]"
msgstr "[105] Emissão de gases com efeito de estufa [Alínea l) do n.°1 do artigo 2.° do CIVA]"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_total_106
msgid "[106] Sum of the table 06A"
msgstr "[106] Soma do quadro 06A"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_107_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_107
msgid "[107] Acquisition of cork and other forestry products [Subparagraph m) of n.°1 of the article 2.° of the CIVA"
msgstr "[107] Aquisição de cortiça e outros produtos de origem silvícola [Alínea m) do n.°1 do artigo 2.° do CIVA]"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_base_12_tag
#: model:account.report.line,name:l10n_pt.trp_base_12
msgid "[12] Whose tax was assessed by the declarant"
msgstr "[12] Cujo imposto foi liquidado pelo declarante"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_13_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_13
msgid "[13] Whose tax was assessed by the declarant"
msgstr "[13] Cujo imposto foi liquidado pelo declarante"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_base_14_tag
#: model:account.report.line,name:l10n_pt.trp_base_14
msgid "[14] Covered by the articles 15.° of the CIVA or the RITI"
msgstr "[14] Abrangidas pelos artigos 15.° do CIVA ou do RITI"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_base_15_tag
#: model:account.report.line,name:l10n_pt.trp_base_15
msgid "[15] Covered by the n.°s 3, 4 e 5 of the article 22.° of the RITI"
msgstr "[15] Abrangidas pelos n.°s 3, 4 e 5 do artigo 22.° do RITI"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_base_1_tag
#: model:account.report.line,name:l10n_pt.trp_base_1
msgid "[1] Reduced rate"
msgstr "[1] Taxa reduzida"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_company_20_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_company_20
msgid "[20] Non-current assets (fixed assets)"
msgstr "[20] Ativos não correntes (imobilizado)"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_company_21_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_company_21
msgid "[21] Inventories at reduced rate"
msgstr "[21] Inventários à taxa reduzida"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_company_22_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_company_22
msgid "[22] Inventories at the normal rate"
msgstr "[22] Inventários à taxa normal"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_company_23_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_company_23
msgid "[23] Inventories at the intermediate rate"
msgstr "[23] Inventários à taxa intermédia"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_company_24_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_company_24
msgid "[24] Other goods and services"
msgstr "[24] Outros bens e serviços"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_2_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_2
msgid "[2] Reduced rate"
msgstr "[2] Taxa reduzida"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_base_3_tag
#: model:account.report.line,name:l10n_pt.trp_base_3
msgid "[3] Normal rate"
msgstr "[3] Taxa normal"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_4_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_4
msgid "[4] Normal rate"
msgstr "[4] Taxa normal"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_base_5_tag
#: model:account.report.line,name:l10n_pt.trp_base_5
msgid "[5] Intermediate rate"
msgstr "[5] Taxa intermédia"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_6_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_6
msgid "[6] Intermediate rate"
msgstr "[6] Taxa intermédia"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_base_7_tag
#: model:account.report.line,name:l10n_pt.trp_base_7
msgid "[7] Exempt or non-taxed - Intra-community transfers of goods and supplies of services mentioned in the recapitulative statements"
msgstr "[7] Isentas ou não tributadas - Transmissões intracomunitárias de bens e prestações de serviços mencionadas nas declarações recapitulativas"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_base_8_tag
#: model:account.report.line,name:l10n_pt.trp_base_8
msgid "[8] Exempt or non-taxed - Entitled to deduction"
msgstr "[8] Isentas ou não tributadas - Que conferem direito à dedução"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_base_total_90
msgid "[90] BASE TOTAL"
msgstr "[90] TOTAL DA BASE TRIBUTÁVEL"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_tax_favor_company_total_91
msgid "[91] TOTAL TAX IN FAVOR OF THE TAXABLE ENTITY"
msgstr "[91] TOTAL DO IMPOSTO A FAVOR DO SUJEITO PASSIVO"

#. module: l10n_pt
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_total_92
msgid "[92] TOTAL TAX TO THE STATE"
msgstr "[92] TOTAL DO IMPOSTO A FAVOR DO ESTADO"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_total_93_formula
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_total_93
msgid "[93] Tax to be paid to the State"
msgstr "[93] Imposto a entragar ao Estado"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_total_94_formula
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_total_94
msgid "[94] Credit to be recovered"
msgstr "[94] Crédito a recuperar"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_97_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_97
msgid "[97] Made by entities resident in EU countries (does not include the operations mentioned in field 16)"
msgstr "[97] Efetuadas por entidades residentes em países comunitários (não inclui as operações mencionadas no campo 16)"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_98_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_98
msgid "[98] Made by entities resident in third countries or territories"
msgstr "[98] Efetuadas por entidades residentes em países ou territórios terceiros"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_tax_favor_state_99_tag
#: model:account.report.line,name:l10n_pt.trp_tax_favor_state_99
msgid "[99] Gold (Decree-Law 362/99)"
msgstr "[99] Ouro (Decreto-Lei 362/99)"

#. module: l10n_pt
#: model:account.report.expression,report_line_name:l10n_pt.trp_base_9_tag
#: model:account.report.line,name:l10n_pt.trp_base_9
msgid "[9] Exempt or non-taxed - Not entitled to deduction"
msgstr "[9] Isentas ou não tributadas - Que não conferem direito à dedução"
