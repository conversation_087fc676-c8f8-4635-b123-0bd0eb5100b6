# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pos_discount
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:01+0000\n"
"PO-Revision-Date: 2015-08-25 10:21+0000\n"
"Last-Translator: <>\n"
"Language-Team: Basque (http://www.transifex.com/odoo/odoo-9/language/eu/)\n"
"Language: eu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_discount
#: code:addons/pos_discount/models/pos_config.py:0
#, python-format
msgid "A discount product is needed to use the Global Discount feature. Go to Point of Sale > Configuration > Settings to set it."
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__iface_discount
msgid "Allow the cashier to give discounts on the whole order."
msgstr ""

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/xml/DiscountButton.xml:0
#, python-format
msgid "Discount"
msgstr "Deskontua"

#. module: pos_discount
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount %"
msgstr ""

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_pc
#, python-format
msgid "Discount Percentage"
msgstr "Deskontua ehunekotan"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_product_id
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount Product"
msgstr "Deskontu produktua"

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "No discount product found"
msgstr ""

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "No tax"
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__iface_discount
msgid "Order Discounts"
msgstr ""

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_config
msgid "Point of Sale Configuration"
msgstr ""

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_session
msgid "Point of Sale Session"
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_product_id
msgid "Pos Discount Product"
msgstr ""

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "Tax: %s"
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,help:pos_discount.field_res_config_settings__pos_discount_pc
msgid "The default discount percentage when clicking on the Discount button"
msgstr ""

#. module: pos_discount
#. openerp-web
#: code:addons/pos_discount/static/src/js/DiscountButton.js:0
#, python-format
msgid "The discount product seems misconfigured. Make sure it is flagged as 'Can be Sold' and 'Available in Point of Sale'."
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_product_id
msgid "The product used to apply the discount on the ticket."
msgstr ""
