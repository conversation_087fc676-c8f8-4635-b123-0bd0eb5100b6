<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_product_replenish" model="ir.ui.view">
        <field name="name">Replenish</field>
        <field name="model">product.replenish</field>
        <field name="arch" type="xml">
            <form string="Replenish wizard">
                <p>
                    The forecasted quantity of
                    <field name="product_id"
                        domain="[('product_tmpl_id', '=', product_tmpl_id)]"
                        readonly="not product_has_variants"
                        options="{'no_open': 1, 'no_create': 1}" class="fw-bold"/> is
                    <field name="forecasted_quantity"/>
                    <field class="ps-1" name="forecast_uom_id" domain="[('category_id', '=', product_uom_category_id)]" groups="uom.group_uom" readonly="1"/>
                    <field name="forecast_uom_id" groups="!uom.group_uom" invisible="1"/>
                </p>
                <group>
                    <group>
                        <field name="product_tmpl_id" invisible="1"/>
                        <field name="product_has_variants" invisible="1"/>
                        <field name="product_uom_category_id" invisible="1"/>
                        <field name="allowed_route_ids" invisible="1"/>
                        <field name="company_id" invisible="1"/>
                        <label for="quantity"/>
                        <div class="o_row">
                            <field name="quantity" />
                            <field name="product_uom_id" groups="!uom.group_uom" invisible="1"/>
                            <field name="product_uom_id"
                                domain="[('category_id', '=', product_uom_category_id)]"
                                groups="uom.group_uom"
                                options="{'no_open': 1, 'no_create': 1}"/>
                        </div>
                        <field name="date_planned" widget="date"/>
                        <field name="warehouse_id"
                            groups="stock.group_stock_multi_warehouses"/>
                        <field name="route_id" domain="[('id', 'in', allowed_route_ids)]"/>
                    </group>
                </group>
                <footer>
                    <button name="launch_replenishment"
                        string="Confirm"
                        type="object"
                        data-hotkey="q"
                        class="btn-primary"/>
                    <button string="Discard"
                        class="btn-secondary"
                        special="cancel" data-hotkey="x" />
                </footer>
            </form>
        </field>
    </record>

    <record id="action_product_replenish" model="ir.actions.act_window">
        <field name="name">Low on stock? Let's replenish.</field>
        <field name="res_model">product.replenish</field>
        <!-- binding_model_id evaluated to False
        to remove it in existing db's as it was bug-prone -->
        <field name="binding_model_id" eval="False"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_product_replenish"/>
        <field name="target">new</field>
    </record>
</odoo>
