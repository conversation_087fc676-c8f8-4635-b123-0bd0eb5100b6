<?xml version="1.0" encoding="UTF-8" ?>
<templates id="template" xml:space="preserve">

    <t t-name="l10n_pe_pos.PartnerDetailsEdit" t-inherit="point_of_sale.PartnerDetailsEdit" t-inherit-mode="extension">
        <xpath expr="//div[input[@t-attf-name='{{item}}']]" position="attributes">
            <attribute name="t-if">!pos.isPeruvianCompany() || item != 'City'</attribute>
        </xpath>

        <xpath expr="//div[select[@name='country_id']]" position="after">
            <xpath expr="//div[select[@name='state_id']]" position="move" />

            <t t-if="pos.isPeruvianCompany()">
                <div class="partner-detail col">
                    <label class="form-label label" for="city">City</label>
                    <select
                        id="city"
                        name="city_id"
                        class="detail form-select"
                        t-model="changes.city_id"
                        t-att-class="{'border-danger': missingFields.includes('city_id')}"
                    >
                        <option value="">None</option>
                        <option
                            t-foreach="pos.cities"
                            t-as="city"
                            t-key="city.id"
                            t-if="changes.state_id == city.state_id[0] and changes.country_id == city.country_id[0]"
                            t-att-value="city.id"
                            t-out="city.name"
                        />
                    </select>
                </div>

                <div class="partner-detail col">
                    <label class="form-label label" for="district">District</label>
                    <select
                        id="district"
                        name="l10n_pe_district"
                        class="detail form-select"
                        t-model="changes.l10n_pe_district"
                        t-att-class="{'border-danger': missingFields.includes('l10n_pe_district')}"
                    >
                        <option value="">None</option>
                        <option
                            t-foreach="pos.l10n_pe_districts"
                            t-as="district"
                            t-key="district.id"
                            t-if="changes.city_id == district.city_id[0] and changes.state_id == district.state_id[0] and changes.country_id == district.country_id[0]"
                            t-att-value="district.id"
                            t-out="district.name"
                        />
                    </select>
                </div>

                <div class="partner-detail col">
                <label class="form-label label" for="identification_type">Identification Type</label>
                    <select
                        id="identification_type"
                        class="detail form-select"
                        name="l10n_latam_identification_type_id"
                        t-model="changes.l10n_latam_identification_type_id"
                        t-att-class="{'border-danger': missingFields.includes('l10n_latam_identification_type_id')}"
                    >
                        <option
                            t-foreach="pos.l10n_latam_identification_types"
                            t-as="l10n_latam_identification_type"
                            t-key="l10n_latam_identification_type.id"
                            t-att-value="l10n_latam_identification_type.id"
                            t-out="l10n_latam_identification_type.name"
                        />
                    </select>
                </div>
            </t>
        </xpath>
        <label for="vat" position="attributes">
            <attribute name="t-if">!pos.isPeruvianCompany()</attribute>
        </label>
        <label for="vat" position="after">
            <label t-if="pos.isPeruvianCompany()" class="form-label label" for="vat">Identification Number</label>
        </label>
    </t>

</templates>
