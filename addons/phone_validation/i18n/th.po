# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* phone_validation
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON> Lappiam, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/phone_blacklist.py:0
#: code:addons/phone_validation/models/phone_blacklist.py:0
#, python-format
msgid " Please correct the number and try again."
msgstr "โปรดแก้ไขหมายเลขแล้วลองอีกครั้ง"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_needaction
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_needaction
msgid "Action Needed"
msgstr "จำเป็นต้องดำเนินการ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: phone_validation
#: model_terms:ir.actions.act_window,help:phone_validation.phone_blacklist_action
msgid "Add a phone number in the blacklist"
msgstr "เพิ่มหมายเลขโทรศัพท์ในบัญชีแบล็คลิสต์"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_search
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/phone_blacklist.py:0
#, python-format
msgid "Are you sure you want to unblacklist this Phone Number?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการยกเลิกการแบล็คลิสต์หมายเลขโทรศัพท์นี้"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_attachment_count
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_attachment_count
msgid "Attachment Count"
msgstr "จำนวนสิ่งที่แนบมา"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_base
msgid "Base"
msgstr "ฐาน"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
msgid "Blacklist"
msgstr "บัญชีดำ"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_tree
msgid "Blacklist Date"
msgstr "วันที่ขึ้นบัญชีแบล็คลิสต์"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "โทรศัพท์ที่ถูกขึ้นบัญชีดำคือมือถือ"

#. module: phone_validation
#: model:ir.actions.act_window,name:phone_validation.phone_blacklist_action
msgid "Blacklisted Phone Numbers"
msgstr "หมายเลขโทรศัพท์ที่อยู่ในบัญชีแบล็คลิสต์"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "โทรศัพท์ที่ถูกขึ้นบัญชีดำคือโทรศัพท์"

#. module: phone_validation
#: model_terms:ir.actions.act_window,help:phone_validation.phone_blacklist_action
msgid "Blacklisted phone numbers won't receive SMS Mailings anymore."
msgstr "หมายเลขโทรศัพท์ที่ขึ้นบัญชีแบล็คลิสต์จะไม่ได้รับจดหมาย SMS อีกต่อไป"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/res_users.py:0
#, python-format
msgid ""
"Blocked by deletion of portal account %(portal_user_name)s by %(user_name)s "
"(#%(user_id)s)"
msgstr ""
"ถูกบล็อกโดยการลบบัญชีพอร์ทัล %(portal_user_name)s โดย %(user_name)s "
"(#%(user_id)s)"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Confirm"
msgstr "ยืนยัน"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__create_uid
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__create_date
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Discard"
msgstr "ละทิ้ง"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__display_name
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"ช่องที่ใช้เก็บหมายเลขโทรศัพท์ที่ถูกต้อง ช่วยเร่งการค้นหาและเปรียบเทียบ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_follower_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_partner_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__has_message
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__id
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__id
msgid "ID"
msgstr "ไอดี"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_needaction
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_needaction
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_has_error
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"หากหมายเลขโทรศัพท์ที่ถูกต้องอยู่ในบัญชีแบล็คลิสต์ ผู้ติดต่อจะไม่ได้รับ SMS "
"ทางเมลกลุ่มจากรายการใดๆ อีกต่อไป"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Impossible number %s: not a valid country prefix."
msgstr "หมายเลขที่เป็นไปไม่ได้ %s: ไม่ใช่คำนำหน้าประเทศที่ถูกต้อง"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Impossible number %s: not enough digits."
msgstr "หมายเลขที่เป็นไปไม่ได้ %s: มีตัวเลขไม่เพียงพอ"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Impossible number %s: probably invalid number of digits."
msgstr "หมายเลขที่เป็นไปไม่ได้ %s: จำนวนหลักอาจไม่ถูกต้อง"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
#: code:addons/phone_validation/tools/phone_validation.py:0
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Impossible number %s: too many digits."
msgstr "หมายเลขที่เป็นไปไม่ได้ %s: มีตัวเลขมากเกินไป"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"ระบุว่าหมายเลขโทรศัพท์ที่ถูกต้องในบัญชีแบล็คลิสต์เป็นหมายเลขโทรศัพท์มือถือหรือไม่"
" "
"ช่วยแยกแยะว่าหมายเลขใดถูกขึ้นบัญชีแบล็คลิสต์เมื่อมีทั้งฟิลด์มือถือและโทรศัพท์ในโมเดล"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"ระบุว่าหมายเลขโทรศัพท์ที่ถูกต้องในบัญชีแบล็คลิสต์เป็นหมายเลขโทรศัพท์หรือไม่ "
"ช่วยแยกแยะว่าหมายเลขที่ถูกขึ้นบัญชีแบล็คลิสต์เมื่อมีฟิลด์มือถือและโทรศัพท์ในโมเดล"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Invalid number %s: probably incorrect prefix."
msgstr "หมายเลข %s ไม่ถูกต้อง: คำนำหน้าอาจไม่ถูกต้อง"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#, python-format
msgid "Invalid primary phone field on model %s"
msgstr "ฟิลด์โทรศัพท์หลักในรุ่นไม่ถูกต้อง %s"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_is_follower
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__write_uid
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__write_date
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_has_error
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_has_error
msgid "Message Delivery error"
msgstr "เกิดข้อผิดพลาดในการส่งข้อความ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_ids
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#, python-format
msgid "Missing definition of phone fields."
msgstr "ไม่มีคำจำกัดความของฟิลด์โทรศัพท์"

#. module: phone_validation
#: model:ir.model.constraint,message:phone_validation.constraint_phone_blacklist_unique_number
msgid "Number already exists"
msgstr "มีหมายเลขอยู่แล้ว"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_needaction_counter
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__message_has_error_counter
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_needaction_counter
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "จำนวนข้อความที่ต้องดำเนินการ"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_mail_thread_phone__message_has_error_counter
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: phone_validation
#: model:ir.model.fields,help:phone_validation.field_phone_blacklist__number
msgid "Number should be E164 formatted"
msgstr "ตัวเลขควรอยู่ในรูปแบบ E164"

#. module: phone_validation
#: model:ir.ui.menu,name:phone_validation.phone_menu_main
msgid "Phone / SMS"
msgstr "โทรศัพท์ / SMS"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_phone_blacklist
#: model:ir.ui.menu,name:phone_validation.phone_blacklist_menu
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_tree
msgid "Phone Blacklist"
msgstr "แบล็คลิสต์โทรศัพท์"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_mail_thread_phone
msgid "Phone Blacklist Mixin"
msgstr "แบล็คลิสต์โทรศัพท์ Mixin"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "โทรศัพท์ขึ้นบัญชีดำ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist__number
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__phone
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Phone Number"
msgstr "หมายเลขโทรศัพท์"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_mobile_search
msgid "Phone/Mobile"
msgstr "โทรศัพท์/มือถือ"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/models/mail_thread_phone.py:0
#, python-format
msgid ""
"Please enter at least 3 characters when searching a Phone/Mobile number."
msgstr "กรุณากรอกอย่างน้อย 3 ตัวอักษรเมื่อค้นหาหมายเลขโทรศัพท์/โทรศัพท์มือถือ"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_phone_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "Reason"
msgstr "เหตุผล"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_phone_blacklist_remove
msgid "Remove phone from blacklist"
msgstr "ลบโทรศัพท์ออกจากบัญชีแบล็คลิสต์"

#. module: phone_validation
#: model:ir.model.fields,field_description:phone_validation.field_mail_thread_phone__phone_sanitized
msgid "Sanitized Number"
msgstr "หมายเลขที่ถูกต้อง"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/tools/phone_validation.py:0
#, python-format
msgid "Unable to parse %(phone)s: %(error)s"
msgstr "ไม่สามารถแยกวิเคราะห์ %(phone)s ได้: %(error)s"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_view_form
msgid "Unblacklist"
msgstr "ยกเลิกการแบล็คลิสต์"

#. module: phone_validation
#. odoo-python
#: code:addons/phone_validation/wizard/phone_blacklist_remove.py:0
#, python-format
msgid "Unblock Reason: %(reason)s"
msgstr "เหตุผลในการเลิกบล็อก: %(reason)s"

#. module: phone_validation
#: model:ir.model,name:phone_validation.model_res_users
msgid "User"
msgstr "ผู้ใช้"

#. module: phone_validation
#: model_terms:ir.ui.view,arch_db:phone_validation.phone_blacklist_remove_view_form
msgid "phone_blacklist_removal"
msgstr "phone_blacklist_removal"
