# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_online_payment
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# ievaput<PERSON>a <<EMAIL>>, 2023
# Will Sensors, 2025
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay
msgid "<strong>Error:</strong> The currency is missing or invalid."
msgstr "<strong>Kļūda:</strong> Iztrūkst valūta vai tā ir nederīga."

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid ""
"<strong>Error:</strong> There was a problem during the payment process."
msgstr "<strong>Kļūda:</strong> Radās problēma maksājuma procesa laikā."

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay
msgid ""
"<strong>No suitable payment method could be found.</strong>\n"
"                            <br/>\n"
"                            If you believe that it is an error, please contact the website administrator."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_config.py:0
#, python-format
msgid "A POS config cannot have more than one online payment method."
msgstr ""
"POS konfigurācijā nevar būt vairāk kā vienas tiešsaistes maksājumu metodes."

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid "A payment option must be specified."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid ""
"A validation payment cannot be used for a Point of Sale online payment."
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pos_payment_method_view_form_inherit_pos_online_payment
msgid "All available providers"
msgstr "Visi pieejamie maksājumu sniedzēji"

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_payment_method.py:0
#, python-format
msgid ""
"All payment providers configured for an online payment method must use the "
"same currency as the Sales Journal, or the company currency if that is not "
"set, of the POS config."
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_payment_method__online_payment_provider_ids
msgid "Allowed Providers"
msgstr "Atļautie maksājumu sniedzēji"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid "Amount"
msgstr "Summa"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/pos_online_payment/static/src/app/utils/online_payment_popup/online_payment_popup.xml:0
#, python-format
msgid "Amount:"
msgstr "Summa:"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/utils/online_payment_popup/online_payment_popup.xml:0
#, python-format
msgid "Cancel"
msgstr "Atcelt"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay
msgid "Cancel payment"
msgstr "Atcelt maksājumu"

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_payment.py:0
#: code:addons/pos_online_payment/models/pos_payment.py:0
#, python-format
msgid "Cannot create a POS online payment without an accounting payment."
msgstr "Nevar izveidot POS tiešsaistes maksājumu bez grāmatvedības maksājuma."

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_payment.py:0
#, python-format
msgid ""
"Cannot create a POS payment with a not online payment method and an online "
"accounting payment."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_payment.py:0
#, python-format
msgid "Cannot edit a POS online payment essential data."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_payment_method.py:0
#, python-format
msgid ""
"Could not create an online payment method (company_id=%d, pos_config_id=%d)"
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_payment_method__has_an_online_payment_provider
msgid "Has An Online Payment Provider"
msgstr "Ir tiešsaistes maksājumu pakalpojuma sniedzējs"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Invalid online payment"
msgstr "Nederīgs tiešsaistes maksājums"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Invalid online payments"
msgstr "Nederīgi tiešsaistes maksājumi"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/utils/online_payment_popup/online_payment_popup.xml:0
#, python-format
msgid "Invite your customer to scan the QR code to pay:"
msgstr "Ielūdziet Jūsu klientu, lai noskenēt QR kodu, lai veikt apmaksu:"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Invoice could not be generated"
msgstr "Rēķins nevarēja tikt ģenerēts"

#. module: pos_online_payment
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_order__next_online_payment_amount
msgid "Next online payment amount to pay"
msgstr "Nākošā tiešsaistes maksājuma summa apmaksai"

#. module: pos_online_payment
#: model:ir.model.fields.selection,name:pos_online_payment.selection__pos_payment_method__type__online
msgid "Online"
msgstr "Tiešsaistē"

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_payment_method.py:0
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_payment_method__is_online_payment
#, python-format
msgid "Online Payment"
msgstr "Tiešsaistes maksājums"

#. module: pos_online_payment
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_order__online_payment_method_id
msgid "Online Payment Method"
msgstr "Tiešsaites maksājuma metode"

#. module: pos_online_payment
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_payment__online_account_payment_id
msgid "Online accounting payment"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Online payment unavailable"
msgstr "Tiešsaistes maksājums nepieejams"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Online payments cannot have a negative amount (%s: %s)."
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid "Order ID"
msgstr "Pasūtījuma ID"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay
msgid "Order ID:"
msgstr "Pasūtījuma ID:"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid "Order Reference"
msgstr "Pasūtījuma atsauce"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/pos_online_payment/static/src/app/utils/online_payment_popup/online_payment_popup.xml:0
#, python-format
msgid "Order id:"
msgstr "Pasūtījuma ID:"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/pos_online_payment/static/src/app/utils/online_payment_popup/online_payment_popup.xml:0
#, python-format
msgid "Order reference:"
msgstr "Pasūtījuma atsauce:"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Order saving issue"
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/account_payment.py:0
#: code:addons/pos_online_payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:pos_online_payment.field_account_payment__pos_order_id
#: model:ir.model.fields,field_description:pos_online_payment.field_payment_transaction__pos_order_id
#: model_terms:ir.ui.view,arch_db:pos_online_payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:pos_online_payment.view_account_payment_form_inherit_pos_online_payment
#, python-format
msgid "POS Order"
msgstr "POS pasūtījums"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pos_payment_method_view_form_inherit_pos_online_payment
msgid "Payment Providers"
msgstr "Maksājumu sniedzēji"

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "Maksājuma darījums"

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_account_payment
msgid "Payments"
msgstr "Maksājumi"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/customer_display/customer_display_template.xml:0
#, python-format
msgid "Please scan the QR code to open the payment page"
msgstr ""

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Pārdošanas punkta konfigurācija"

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_pos_order
msgid "Point of Sale Orders"
msgstr "Pārdošanas punkta pasūtījumi"

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Pārdošanas punkta maksājumu metodes"

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Pārdošanas punkta maksājumi"

#. module: pos_online_payment
#: model:ir.model,name:pos_online_payment.model_pos_session
msgid "Point of Sale Session"
msgstr "Pārdošanas punkta sesija"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid "Processed by"
msgstr "Apstrādāja"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/customer_display/customer_display_template.xml:0
#: code:addons/pos_online_payment/static/src/app/utils/online_payment_popup/online_payment_popup.xml:0
#, python-format
msgid "QR Code to pay"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/utils/online_payment_popup/online_payment_popup.xml:0
#, python-format
msgid "Scan to Pay"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Server error"
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/payment_transaction.py:0
#, python-format
msgid "The POS online payment (tx.id=%d) could not be saved correctly"
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The POS online payment (tx.id=%d) could not be saved correctly because the "
"online payment method could not be found"
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid "The POS session is not opened."
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,help:pos_online_payment.field_payment_transaction__pos_order_id
msgid "The Point of Sale order linked to the payment transaction"
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,help:pos_online_payment.field_account_payment__pos_order_id
msgid "The Point of Sale order linked to this payment"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "The QR Code for paying could not be generated."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid "The amount to pay has changed. Please refresh the page."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid "The currency is invalid."
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "The invoice could not be generated."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid "The order has been canceled."
msgstr "Pasūtījums ir atcelts."

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "The order has not been saved correctly on the server."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_session.py:0
#, python-format
msgid "The partner of the POS online payment (id=%d) could not be found"
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid "The payment provider is invalid."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid ""
"The payment should either be direct, with redirection, or made by a token."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid "The payment token is invalid."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/payment_transaction.py:0
#, python-format
msgid "The payment transaction (%d) has a negative amount."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid "The provided order or access token is invalid."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid "The provided partner_id is different than expected."
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "The saved order could not be retrieved."
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"The total amount of remaining online payments to execute (%s) doesn't "
"correspond to the remaining unpaid amount of the order (%s)."
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "There are online payments that were missing in your view."
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"There is a problem with the server. The order cannot be saved and therefore "
"the online payment is not possible."
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"There is a problem with the server. The order online payment status cannot "
"be retrieved."
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"There is a problem with the server. The order online payment status cannot "
"be retrieved. Are you sure there is no online payment for this order ?"
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid ""
"There is no online payment method configured for this Point of Sale order."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid "There is nothing to pay for this order."
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay
msgid "There is nothing to pay."
msgstr "Nav par ko maksāt."

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay
msgid "To Pay"
msgstr "Apmaksai"

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/models/pos_config.py:0
#, python-format
msgid ""
"To use an online payment method in a POS config, it must have at least one "
"published payment provider supporting the currency of that POS config."
msgstr ""

#. module: pos_online_payment
#. odoo-python
#: code:addons/pos_online_payment/controllers/payment_portal.py:0
#, python-format
msgid "Tokenization is not available for logged out customers."
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid "Transaction Reference"
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pay_confirmation
msgid "Try again"
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,field_description:pos_online_payment.field_pos_payment_method__type
msgid "Type"
msgstr "Veids"

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Updated online payments"
msgstr ""

#. module: pos_online_payment
#: model:ir.model.fields,help:pos_online_payment.field_pos_payment_method__is_online_payment
msgid ""
"Use this payment method for online payments (payments made on a web page "
"with online payment providers)"
msgstr ""

#. module: pos_online_payment
#. odoo-javascript
#: code:addons/pos_online_payment/static/src/app/screens/payment_screen/payment_screen.js:0
#, python-format
msgid "Yes"
msgstr "Jā"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pos_payment_method_view_form_inherit_pos_online_payment
msgid "You have not activated any"
msgstr ""

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pos_payment_method_view_form_inherit_pos_online_payment
msgid "payment provider"
msgstr "maksājumu pakalpojumu sniedzējs"

#. module: pos_online_payment
#: model_terms:ir.ui.view,arch_db:pos_online_payment.pos_payment_method_view_form_inherit_pos_online_payment
msgid "to allow online payments."
msgstr "atļaut tiešsaistes maksājumus"
