<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_employee_cv">
        <t t-set="full_width" t-value="True"/>
        <t t-call="web.basic_layout">
        <t t-foreach="docs" t-as="o">
            <div class="o_employee_cv page">
                <t t-call="hr_skills.report_employee_cv_company"/>
                <t t-call="hr_skills.report_employee_cv_sidepanel"/>
                <t t-call="hr_skills.report_employee_cv_main_panel"/>
                <p class="o_new_page"/>
            </div>
        </t>
        </t>
    </template>

    <template id="report_employee_cv_company">
        <div class="o_company row">
            <div class="col-12 mb-4">
                <img t-if="o.company_id.logo" t-att-src="image_data_uri(o.company_id.logo)" style="max-height: 45px;" alt="Logo"/>
                <div class="oe_structure"></div>
            </div>
            <div class="col-12 mb-4" style="font-weight: bold;">
                <span t-out="o.company_id.partner_id.name">Demo Company Name</span>
            </div>
            <div class="col-12">
                <span t-field="o.company_id.partner_id" t-options='{"widget": "contact", "fields": ["address"], "no_marker": True}'>Demo Address</span>
            </div>
        </div>
    </template>

    <template id="report_employee_cv_sidepanel">
        <div class="oe_structure"></div>
        <div class="o_sidebar" t-attf-style="
            background: #{color_primary};
            background-color: #{color_primary};">
            <div class="o_profile">
                <img class="img img-fluid rounded-circle o_profile_image" t-attf-src="data:image/png;base64,#{o.image_128}" alt="" t-if="o.image_128"/>
                <h1 class="o_profile_name mt-2" t-field="o.name">Marc Demo</h1>
                <h3 class="o_profile_job mb-2" t-field="o.job_id">Software Developer</h3>
            </div>
            <div class="oe_structure"></div>
            <div class="o_sidebar_section" t-if="show_contact">
                <ul class="o_social">
                    <li class="email" t-if="o.company_id.email">
                        <i class="fa fa-solid fa-envelope pe-2"/>
                        <a t-attf-href="mailto: #{o.company_id.email}" t-field="o.company_id.email"><EMAIL></a>
                    </li>
                    <li class="phone" t-if="o.company_id.mobile">
                        <i class="fa fa-solid fa-phone pe-2"/>
                        <a t-attf-href="tel:#{o.company_id.mobile}" t-field="o.company_id.mobile">+1234567890</a>
                    </li>
                    <li class="website" t-if="o.company_id.website">
                        <i class="fa fa-solid fa-globe pe-2"/>
                        <a t-attf-href="tel:#{o.company_id.website}" t-field="o.company_id.website">www.demo.com</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="oe_structure"></div>
    </template>
    
    <template id="report_employee_cv_main_panel">
        <div class="oe_structure"></div>
        <t t-foreach="resume_lines[o]" t-as="line_type">
            <div class="o_main_panel">
                <div class="o_main_panel_section">
                    <h2 class="o_main_panel_title mt-4" t-attf-style="color: #{color_secondary};">
                        <span class="o_main_panel_icon" t-attf-style="background: #{color_secondary};">
                            <i class="fa fa-solid fa-briefcase"/>
                        </span>
                        <span t-out="line_type">Experience</span>
                    </h2>
                    <t t-foreach="resume_lines[o][line_type]" t-as="resume_line">
                        <div class="o_main_panel_resume_title ps-5">
                            <div class="o_main_panel_resume_title_job">
                                <h3 class="o_main_panel_resume_job" t-field="resume_line.name">Software Developer</h3>
                            </div>
                            <div class="o_main_panel_resume_title_dates">
                                <t t-set="present">Present</t>
                                <div class="o_main_panel_resume_year">
                                    <span t-out="resume_line.date_start.year">2022</span> - <span t-out="resume_line.date_end.year if resume_line.date_end else 'Present'">2023</span>
                                </div>
                            </div>
                        </div>
                        <div class="o_main_panel_resume_description ps-5 w-auto">
                            <p t-field="resume_line.description" data-oe-demo="Odoo India pvt. Ltd"/>
                        </div>
                    </t>
                </div>
            </div>
        </t>

        <div class="oe_structure"></div>
        <t t-set="has_languages" t-value="skill_type_language and any(skill_line.skill_type_id == skill_type_language for skill_line in o.employee_skill_ids)"/>
        <div class="o_main_panel mt-4" t-if="has_languages">
            <div class="o_main_panel_section" t-if="skill_type_language">
                <h2 class="o_main_panel_title" t-attf-style="color: #{color_secondary};">
                    <span class="o_main_panel_icon" t-attf-style="background: #{color_secondary};">
                        <i class="fa fa-solid fa-language"/>
                    </span>
                    Languages
                </h2>
                <t t-foreach="o.employee_skill_ids" t-as="skill_line">
                    <t t-if="skill_line.skill_type_id == skill_type_language">
                        <div class="o_main_panel_resume_title ps-5 pb-2">
                            <div class="o_main_panel_resume_title_job">
                                <h3 class="o_main_panel_resume_job"><span t-out="skill_line.skill_id.name">English</span> - <span t-out="skill_line.skill_level_id.name">Fluent</span></h3>
                            </div>
                        </div>
                    </t>
                </t>
            </div>
        </div>

        <div class="oe_structure"></div>
        <t t-set="has_skills" t-value="show_skills and any(skill_line.skill_type_id != skill_type_language for skill_line in o.employee_skill_ids)"/>
        <div class="o_main_panel mt-4" t-if="has_skills">
            <div class="o_main_panel_section">
                <h2 class="o_main_panel_title" t-attf-style="color: #{color_secondary};">
                    <span class="o_main_panel_icon" t-attf-style="background: #{color_secondary};">
                        <i class="fa fa-solid fa-rocket"/>
                    </span>
                    Skills
                </h2>
                <t t-set="valid_skills" t-value="[skill for skill in o.employee_skill_ids if skill.skill_type_id != skill_type_language]"/>
                <t t-foreach="[valid_skills[n:n+2] for n in range(0, len(valid_skills), 2)]" t-as="skills_pair">
                    <div class="o_main_panel_skills row ps-5">
                        <t t-foreach="skills_pair" t-as="skill_line">
                            <div class="o_main_panel_skill_line col-6 pe-4" t-if="skill_line.skill_type_id != skill_type_language">
                                <h3 class="o_main_panel_skill_name" t-field="skill_line.skill_id">Python</h3>
                                <div class="progress o_main_panel_skill_progress_bar">
                                    <div
                                        class="o_main_panel_skill_progress_bar_color"
                                        role="progressbar"
                                        t-attf-style="width: #{skill_line.level_progress}%; background: #{color_primary};"
                                        t-att-aria-valuenow="skill_line.level_progress"
                                        aria-valuemin="0"
                                        aria-label="Progress bar"
                                        aria-valuemax="100"/>
                                </div>
                            </div>
                        </t>
                    </div>
                </t>
            </div>
        </div>
        <div class="oe_structure"></div>
    </template>
</odoo>
