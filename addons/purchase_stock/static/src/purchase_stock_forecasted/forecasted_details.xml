<?xml version="1.0" encoding="utf-8"?>
<templates id="template" xml:space="preserve">
     <t t-name="purchase_stock.ForecastedDetails" t-inherit="stock.ForecastedDetails" t-inherit-mode="extension">
         <xpath expr="//tr[@name='draft_picking_in']" position="after">
            <tr t-if="props.docs.draft_purchase_qty" name="draft_po_in" t-attf-class="#{props.docs.draft_purchase_orders_matched and 'o_grid_match table-info'}">
                <td colspan="2">Requests for quotation</td>
                <td t-out="_formatFloat(props.docs.draft_purchase_qty)" class="text-end"/>
                <td>
                    <t t-foreach="props.docs.draft_purchase_orders" t-as="purchase_order" t-key="purchase_order_index">
                        <t t-if="purchase_order_index > 0"> | </t>
                        <a t-attf-href="#" t-out="purchase_order.name"
                            class="fw-bold"
                            t-on-click.prevent="() => this.props.openView('purchase.order', 'form', purchase_order.id)"/>
                    </t>
                </td>
            </tr>
        </xpath>
    </t>
</templates>
