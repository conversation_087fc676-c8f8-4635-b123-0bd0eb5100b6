# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_aps
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# Melih Melik Sonmez, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> Melik Sonmez, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_access_code
msgid "APS Access Code"
msgstr ""

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_merchant_identifier
msgid "APS Merchant Identifier"
msgstr ""

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_sha_request
msgid "APS SHA Request Phrase"
msgstr ""

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_sha_response
msgid "APS SHA Response Phrase"
msgstr ""

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "Access Code"
msgstr ""

#. module: payment_aps
#: model:ir.model.fields.selection,name:payment_aps.selection__payment_provider__code__aps
msgid "Amazon Payment Services"
msgstr "Amazon Ödeme Hizmetleri"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__code
msgid "Code"
msgstr "Kod"

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "Merchant Identifier"
msgstr "Satıcı Tanımlayıcı"

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "Referans %s eşleşen bir işlem bulunamadı."

#. module: payment_aps
#: model:ir.model,name:payment_aps.model_payment_provider
msgid "Payment Provider"
msgstr "Ödeme Sağlayıcı"

#. module: payment_aps
#: model:ir.model,name:payment_aps.model_payment_transaction
msgid "Payment Transaction"
msgstr "Ödeme İşlemi"

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing payment state."
msgstr "Ödeme durumu eksik olan veriler alındı."

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference %(ref)s."
msgstr ""

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
#, python-format
msgid ""
"Received invalid transaction status %(status)s and reason '%(reason)s'."
msgstr "Geçersiz %(status)sstatüsü ve'%(reason)s' sebepli kayıt alındı."

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "SHA Request Phrase"
msgstr ""

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "SHA Response Phrase"
msgstr ""

#. module: payment_aps
#: model:ir.model.fields,help:payment_aps.field_payment_provider__aps_access_code
msgid "The access code associated with the merchant account."
msgstr ""

#. module: payment_aps
#: model:ir.model.fields,help:payment_aps.field_payment_provider__aps_merchant_identifier
msgid "The code of the merchant account to use with this provider."
msgstr "Bu Sağlayıcı ile kullanılan Üye işyeri hesabına ait kod."

#. module: payment_aps
#: model:ir.model.fields,help:payment_aps.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Bu ödeme sağlayıcısının teknik kodu."
