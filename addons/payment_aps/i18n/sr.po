# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_aps
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON> <dragan.v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2023
# コフスタジオ, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: コフスタジオ, 2024\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_access_code
msgid "APS Access Code"
msgstr ""

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_merchant_identifier
msgid "APS Merchant Identifier"
msgstr ""

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_sha_request
msgid "APS SHA Request Phrase"
msgstr ""

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__aps_sha_response
msgid "APS SHA Response Phrase"
msgstr ""

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "Access Code"
msgstr ""

#. module: payment_aps
#: model:ir.model.fields.selection,name:payment_aps.selection__payment_provider__code__aps
msgid "Amazon Payment Services"
msgstr "Amazon Payment Services"

#. module: payment_aps
#: model:ir.model.fields,field_description:payment_aps.field_payment_provider__code
msgid "Code"
msgstr "Kod"

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "Merchant Identifier"
msgstr ""

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
#, python-format
msgid "No transaction found matching reference %s."
msgstr "No transaction found matching reference %s."

#. module: payment_aps
#: model:ir.model,name:payment_aps.model_payment_provider
msgid "Payment Provider"
msgstr "Provajder plaćanja"

#. module: payment_aps
#: model:ir.model,name:payment_aps.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transakcija plaćanja"

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing payment state."
msgstr ""

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
#, python-format
msgid "Received data with missing reference %(ref)s."
msgstr ""

#. module: payment_aps
#. odoo-python
#: code:addons/payment_aps/models/payment_transaction.py:0
#, python-format
msgid ""
"Received invalid transaction status %(status)s and reason '%(reason)s'."
msgstr ""

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "SHA Request Phrase"
msgstr ""

#. module: payment_aps
#: model_terms:ir.ui.view,arch_db:payment_aps.payment_provider_form
msgid "SHA Response Phrase"
msgstr ""

#. module: payment_aps
#: model:ir.model.fields,help:payment_aps.field_payment_provider__aps_access_code
msgid "The access code associated with the merchant account."
msgstr ""

#. module: payment_aps
#: model:ir.model.fields,help:payment_aps.field_payment_provider__aps_merchant_identifier
msgid "The code of the merchant account to use with this provider."
msgstr ""

#. module: payment_aps
#: model:ir.model.fields,help:payment_aps.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "The technical code of this payment provider."
