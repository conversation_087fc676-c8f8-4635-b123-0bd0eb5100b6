# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_mercury
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.res_config_settings_view_form_inherit_pos_mercury
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Buy a card reader"
msgstr "<i class=\"oi oi-fw oi-arrow-right\"/>Comprar um leitor de cartão"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"<i>Vantiv Configurations</i> define what Vantiv account will be used when\n"
"                                processing credit card transactions in the Point Of Sale. Setting up a Vantiv\n"
"                                configuration will enable you to allow payments with various credit cards\n"
"                                (eg. Visa, MasterCard, Discovery, American Express, ...). After setting up this\n"
"                                configuration you should associate it with a Point Of Sale payment method."
msgstr ""
"<i>As configurações do Vantiv</i> definem qual conta Vantiv será utilizada\n"
"                                ao processar transações de cartão de crédito no ponto de venda. A criação de uma configuração\n"
"                                do Vantiv possibilitará pagamentos com vários cartões de crédito\n"
"                                (ex.: Visa, MasterCard, Discovery, American Express...). Após realizar esta\n"
"                                configuração, associe-a com uma forma de pagamento do ponto de venda."

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "ABOVE AMOUNT PURSUANT"
msgstr "A QUANTIA ACIMA DE ACORDO COM"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "APPROVAL CODE:"
msgstr "CÓDIGO DE APROVAÇÃO:"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_barcode_rule
msgid "Barcode Rule"
msgstr "Regra de código de barras"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "CARDHOLDER WILL PAY CARD ISSUER"
msgstr "O TITULAR DO CARTÃO PAGARÁ AO EMISSOR DO CARTÃO"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_brand
msgid "Card Brand"
msgstr "Bandeira do cartão"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_number
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_order
msgid "Card Number"
msgstr "Número do cartão"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_prefixed_card_number
msgid "Card Number Prefix"
msgstr "Prefixo do número do cartão"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_card_owner_name
msgid "Card Owner Name"
msgstr "Nome do titular do cartão"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_tree
msgid "Card Reader"
msgstr "Leitor de cartão"

#. module: pos_mercury
#: model_terms:ir.actions.act_window,help:pos_mercury.action_configuration_form
msgid "Configure your card reader"
msgstr "Configure o leitor do cartão"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "Could not read card"
msgstr "Não foi possível ler o cartão"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__create_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__create_date
msgid "Created on"
msgstr "Criado em"

#. module: pos_mercury
#: model:ir.model.fields.selection,name:pos_mercury.selection__barcode_rule__type__credit
msgid "Credit Card"
msgstr "Cartão de crédito"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"Credit card refunds are not supported. Instead select your credit card "
"payment method, click 'Validate' and refund the original charge manually "
"through the Vantiv backend."
msgstr ""
"Não é possível realizar reembolsos por cartão de crédito. Em vez disso, "
"selecione a forma de pagamento com cartão de crédito, clique em 'Validar' e "
"reembolse a cobrança original manualmente por meio do back-end da Vantiv."

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__display_name
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"For quickly handling orders: just swiping a credit card when on the payment screen\n"
"                                (without having pressed anything else) will charge the full amount of the order to\n"
"                                the card."
msgstr ""
"Para processar pedidos rapidamente: passar o cartão de crédito quando está na tela de pagamento\n"
"                                (sem ter pressionado nada) cobrará o valor total do pedido\n"
"                                no cartão."

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/product_screen/product_screen.js:0
#, python-format
msgid "Go to payment screen to use cards"
msgstr "Acesse a tela de pagamento para utilizar cartões"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "Handling transaction..."
msgstr "Processando a transação..."

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__id
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__id
msgid "ID"
msgstr "ID"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "ID of the merchant to authenticate him on the payment provider server"
msgstr ""
"ID do comerciante para autenticá-lo no servidor do provedor de pagamentos"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"If you don't already have a Vantiv account, contact Vantiv at +****************\n"
"                                to create one."
msgstr ""
"Se você ainda não tem uma conta Vantiv, contate a Vantiv através do número +****************\n"
"                                para criar uma conta."

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_invoice_no
msgid "Invoice number from Vantiv Pay"
msgstr "Número da fatura da Vantiv Pay"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_uid
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__write_date
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_mercury_transaction__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_id
msgid "Merchant ID"
msgstr "ID do comerciante"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid "Merchant Password"
msgstr "Senha do comerciante"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_mercury_configuration__name
msgid "Name"
msgstr "Nome"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__name
msgid "Name of this Vantiv configuration"
msgstr "Nome dessa configuração Vantiv"

#. module: pos_mercury
#. odoo-python
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:0
#, python-format
msgid "No Vantiv configuration associated with the payment method."
msgstr "Nenhuma configuração Vantiv associada à forma de pagamento."

#. module: pos_mercury
#. odoo-python
#: code:addons/pos_mercury/models/pos_mercury_transaction.py:0
#, python-format
msgid "No opened point of sale session for user %s found."
msgstr ""
"Nenhuma sessão aberta de ponto de venda para o usuário %s foi encontrada."

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "No response from Vantiv (Vantiv down?)"
msgstr "Sem resposta da Vantiv (está fora do ar?)"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "No response from server (connected to network?)"
msgstr "Sem resposta do servidor (está conectado à rede?)"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "Odoo error while processing transaction."
msgstr "Erro do Odoo ao processar a transação."

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/app/payment_transaction_popup/payment_transaction_popup.js:0
#, python-format
msgid "Ok"
msgstr "Ok"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/app/payment_transaction_popup/payment_transaction_popup.js:0
#, python-format
msgid "Online Payment"
msgstr "Pagamento online"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "Partially approved"
msgstr "Aprovado parcialmente"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_mercury_configuration__merchant_pwd
msgid ""
"Password of the merchant to authenticate him on the payment provider server"
msgstr ""
"Senha do comerciante para autenticá-lo no servidor do provedor de pagamentos"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "Pay with: "
msgstr "Pagar com:"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_record_no
msgid "Payment record number from Vantiv Pay"
msgstr "Número do registro do pagamento da Vantiv Pay"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_ref_no
msgid "Payment reference number from Vantiv Pay"
msgstr "Número de referência do pagamento da Vantiv Pay"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "Please setup your Vantiv merchant account."
msgstr "Configure a sua conta de comerciante na Vantiv."

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_order
msgid "Point of Sale Orders"
msgstr "Pedidos do ponto de venda"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Métodos de pagamento do ponto de venda"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Pagamentos de ponto de venda"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_session
msgid "Point of Sale Session"
msgstr "Sessão do ponto de venda"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_configuration
msgid "Point of Sale Vantiv Configuration"
msgstr "Configuração do Vantiv no ponto de venda"

#. module: pos_mercury
#: model:ir.model,name:pos_mercury.model_pos_mercury_mercury_transaction
msgid "Point of Sale Vantiv Transaction"
msgstr "Transação Vantiv no ponto de venda"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "Refunds not supported"
msgstr "Não há suporte para reembolsos"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "Reversal failed, sending VoidSale..."
msgstr "O estorno falhou, enviando VoidSale..."

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "Reversal succeeded"
msgstr "O estorno foi bem-sucedido"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "Sending reversal..."
msgstr "Enviando estorno..."

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "TO CARDHOLDER AGREEMENT"
msgstr "O CONTRATO DO TITULAR DO CARTÃO"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_brand
msgid "The brand of the payment card (e.g. Visa, AMEX, ...)"
msgstr "A bandeira do cartão de pagamento (ex., Visa, AMEX...)"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_prefixed_card_number
msgid "The card number used for the payment."
msgstr "Numero do cartão utilizado para o pagamento."

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment_method__pos_mercury_config_id
msgid "The configuration of Vantiv used for this journal"
msgstr "A configuração Vantiv utilizada para este diário"

#. module: pos_mercury
#: model:ir.model.fields,help:pos_mercury.field_pos_payment__mercury_card_number
msgid "The last 4 numbers of the card used to pay"
msgstr "Os 4 últimos dígitos do cartão utilizado na transação. "

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid ""
"This can be caused by a badly executed swipe or by not having your keyboard "
"layout set to US QWERTY (not US International)."
msgstr ""
"Isto pode ser causado se o cartão for passado incorretamente ou caso o "
"layout do teclado não esteja definido como US QWERTY (não US International)."

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "Transaction approved"
msgstr "Transação aprovada"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_barcode_rule__type
msgid "Type"
msgstr "Tipo"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"Using the Vantiv integration in the Point Of Sale is easy: just press the\n"
"                                associated payment method. After that the amount can be adjusted (eg. for cashback)\n"
"                                just like on any other payment line. Whenever the payment line is set up, a card\n"
"                                can be swiped through the card reader device."
msgstr ""
"Utilizar a integração Vantiv no ponto de venda é fácil: basta selecionar\n"
"                                a forma de pagamento associada. Depois disso, a quantia poderá ser ajustada (ex., para reembolso)\n"
"                                assim como qualquer outra linha de pagamento. Quando a linha de pagamento estiver configurada, o cartão\n"
"                                poderá ser lido pelo dispositivo."

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.res_config_settings_view_form_inherit_pos_mercury
msgid "Vantiv Accounts"
msgstr "Contas Vantiv"

#. module: pos_mercury
#: model:ir.actions.act_window,name:pos_mercury.action_configuration_form
#: model:ir.ui.menu,name:pos_mercury.menu_pos_pos_mercury_config
msgid "Vantiv Configurations"
msgstr "Configurações da Vantiv"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment_method__pos_mercury_config_id
msgid "Vantiv Credentials"
msgstr "Credenciais da Vantiv"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_invoice_no
msgid "Vantiv invoice number"
msgstr "Número da fatura Vantiv"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_record_no
msgid "Vantiv record number"
msgstr "Número de registro Vantiv"

#. module: pos_mercury
#: model:ir.model.fields,field_description:pos_mercury.field_pos_payment__mercury_ref_no
msgid "Vantiv reference number"
msgstr "Número de referência Vantiv"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen/payment_screen.js:0
#, python-format
msgid "VoidSale succeeded"
msgstr "VoidSale foi bem-sucedido"

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/overrides/components/payment_screen_payment_lines/payment_screen_payment_lines.xml:0
#, python-format
msgid "WAITING FOR SWIPE"
msgstr "AGUARDANDO PASSAR O CARTÃO"

#. module: pos_mercury
#: model_terms:ir.ui.view,arch_db:pos_mercury.view_pos_mercury_configuration_form
msgid ""
"We currently support the MagTek Dynamag card reader device. It can be connected\n"
"                                directly to the Point Of Sale device or it can be connected to the IoTBox."
msgstr ""
"Atualmente temos suporte para o dispositivo leitor de cartão MagTek Dynamag. Ele pode ser conectado\n"
"                                diretamente ao dispositivo do ponto de venda ou ao IoT Box."

#. module: pos_mercury
#. odoo-javascript
#: code:addons/pos_mercury/static/src/app/screens/receipt_screen/receipt/order_receipt.xml:0
#, python-format
msgid "X______________________________"
msgstr "X______________________________"
