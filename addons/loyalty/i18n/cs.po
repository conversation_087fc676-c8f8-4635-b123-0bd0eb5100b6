# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* loyalty
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON>, 2023
# ka<PERSON><PERSON><PERSON> schustero<PERSON> <karol<PERSON>.schustero<PERSON>@vdp.sk>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-30 18:36+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__trigger
msgid ""
"\n"
"        Automatic: Customers will be eligible for a reward automatically in their cart.\n"
"        Use a code: Customers will be eligible for a reward if they enter a code.\n"
"        "
msgstr ""
"\n"
"        Automaticky: Zákazníci budou mít nárok na odměnu automaticky ve svém košíku.\n"
"        Použijte kód: Zákazníci budou mít nárok na odměnu, pokud zadají kód.\n"
"        "

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__available_on
msgid ""
"\n"
"        Manage where your program should be available for use.\n"
"        "
msgstr ""
"\n"
"        Spravujte, kde by měl být váš program dostupný pro použití.\n"
"        "

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__portal_visible
msgid ""
"\n"
"        Show in web portal, PoS customer ticket, eCommerce checkout, the number of points available and used by reward.\n"
"        "
msgstr ""
"\n"
"        Zobrazit na webovém portálu, zákaznický tiket PoS, pokladna e-shopu, počet bodů, které jsou k dispozici a využité odměnou.\n"
"        "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid " (Max %s)"
msgstr " (Max %s)"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "$100"
msgstr "100€"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%g%% on "
msgstr "%g%% na "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%s per order"
msgstr "%s na objednávku"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%s per order on "
msgstr "%s na objednávku na"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%s per point"
msgstr "%s za bod"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "%s per point on "
msgstr "%s za bod na"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "( Max"
msgstr "( Max"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "(if at least"
msgstr "(nejméně"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "(tax excluded)"
msgstr "(bez daně)"

#. module: loyalty
#: model:loyalty.program,name:loyalty.10_percent_coupon
msgid "10% Discount Coupons"
msgstr "10% slevové kupóny"

#. module: loyalty
#: model:loyalty.reward,description:loyalty.10_percent_coupon_reward
#: model:loyalty.reward,description:loyalty.10_percent_with_code_reward
msgid "10% on your order"
msgstr "10% na vaši objednávku"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "1000"
msgstr "1000"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "2+1 Free"
msgstr "2+1 zdarma"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "2023-08-20"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "2023-12-31"
msgstr "2023-12-31"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid ""
"<br/>\n"
"                                        <br/>\n"
"\n"
"                                        <span class=\"fw-bold text-decoration-underline\">Applied to:</span>"
msgstr ""
"<br/>\n"
"                                        <br/>\n"
"\n"
"                                        <span class=\"fw-bold text-decoration-underline\">Použito na:</span>"

#. module: loyalty
#: model:mail.template,body_html:loyalty.mail_template_gift_card
msgid ""
"<div style=\"background: #ffffff\">\n"
"                <div style=\"margin:0px; font-size:24px; font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:36px; color:#333333; text-align: center\">\n"
"                    Here is your gift card!\n"
"                </div>\n"
"                <div style=\"padding-top:20px; padding-bottom:20px\">\n"
"                    <img src=\"/loyalty/static/img/gift_card.png\" style=\"display:block; border:0; outline:none; text-decoration:none; margin:auto;\" width=\"300\">\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:48px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:40px; font-style:normal; font-weight:normal; color:#333333; text-align:center\">\n"
"                        <strong t-out=\"format_amount(object.points, object.currency_id) or ''\">$ 150.00</strong></h3>\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; background-color:#efefef; text-align:center;\">\n"
"                    <p style=\"margin:0px; font-size:14px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:21px; color:#333333\">\n"
"                        <strong>Gift Card Code</strong>\n"
"                    </p>\n"
"                    <p style=\"margin:0px; font-size:25px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:38px; color:#A9A9A9\" t-out=\"object.code or ''\">4f10-15d6-41b7-b04c-7b3e</p>\n"
"                </div>\n"
"                <div t-if=\"object.expiration_date\" style=\"padding:0; margin:0px; padding-top:10px; padding-bottom:10px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:17px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:14px; font-style:normal; font-weight:normal; color:#A9A9A9; text-align:center\">Card expires <t t-out=\"format_date(object.expiration_date) or ''\">05/05/2021</t></h3>\n"
"                </div>\n"
"                <div style=\"padding:20px; margin:0px; text-align:center;\">\n"
"                    <span style=\"background-color:#999999; display:inline-block; width:auto; border-radius:5px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop\" target=\"_blank\" style=\"text-decoration:none; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:22px; color:#FFFFFF; border-style:solid; border-color:#999999; border-width:20px 30px; display:inline-block; background-color:#999999; border-radius:5px; font-weight:bold; font-style:normal; line-height:26px; width:auto; text-align:center\">Use it right now!</a>\n"
"                    </span>\n"
"                </div>\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"background: #ffffff\">\n"
"                <div style=\"margin:0px; font-size:24px; font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:36px; color:#333333; text-align: center\">\n"
"                    Zde je vaše dárková karta!\n"
"                </div>\n"
"                <div style=\"padding-top:20px; padding-bottom:20px\">\n"
"                    <img src=\"/loyalty/static/img/gift_card.png\" style=\"display:block; border:0; outline:none; text-decoration:none; margin:auto;\" width=\"300\">\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:48px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:40px; font-style:normal; font-weight:normal; color:#333333; text-align:center\">\n"
"                        <strong t-out=\"format_amount(object.points, object.currency_id) or ''\">$ 150.00</strong></h3>\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; background-color:#efefef; text-align:center;\">\n"
"                    <p style=\"margin:0px; font-size:14px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:21px; color:#333333\">\n"
"                        <strong>Kód dárkové karty</strong>\n"
"                    </p>\n"
"                    <p style=\"margin:0px; font-size:25px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:38px; color:#A9A9A9\" t-out=\"object.code or ''\">4f10-15d6-41b7-b04c-7b3e</p>\n"
"                </div>\n"
"                <div t-if=\"object.expiration_date\" style=\"padding:0; margin:0px; padding-top:10px; padding-bottom:10px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:17px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:14px; font-style:normal; font-weight:normal; color:#A9A9A9; text-align:center\">Karta vyprší <t t-out=\"format_date(object.expiration_date) or ''\">05/05/2021</t></h3>\n"
"                </div>\n"
"                <div style=\"padding:20px; margin:0px; text-align:center;\">\n"
"                    <span style=\"background-color:#999999; display:inline-block; width:auto; border-radius:5px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop\" target=\"_blank\" style=\"text-decoration:none; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:22px; color:#FFFFFF; border-style:solid; border-color:#999999; border-width:20px 30px; display:inline-block; background-color:#999999; border-radius:5px; font-weight:bold; font-style:normal; line-height:26px; width:auto; text-align:center\">Využijte ji hned teď!</a>\n"
"                    </span>\n"
"                </div>\n"
"            </div>\n"
"        "

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "<i class=\"fa fa-cube fa-fw\" title=\"Product Domain\"/>"
msgstr "<i class=\"fa fa-cube fa-fw\" title=\"Product Domain\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-cube fa-fw\" title=\"Products\"/>"
msgstr "<i class=\"fa fa-cube fa-fw\" title=\"Products\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-cube fa-fw\" title=\"Products\"/><span>All Products</span>"
msgstr ""
"<i class=\"fa fa-cube fa-fw\" title=\"Products\"/><span>Všechny "
"produkty</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-cubes fa-fw\" title=\"Product Categories\"/>"
msgstr "<i class=\"fa fa-cubes fa-fw\" title=\"Product Categories\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-search fa-fw\" title=\"Product Domain\"/>"
msgstr "<i class=\"fa fa-search fa-fw\" title=\"Product Domain\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-tags fa-fw\" title=\"Product Tags\"/>"
msgstr "<i class=\"fa fa-tags fa-fw\" title=\"Product Tags\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid ""
"<span class=\"fw-bold text-decoration-underline\">Among:</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"fw-bold text-decoration-underline\">Mezi:</span>\n"
"                                    <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid ""
"<span class=\"fw-bold text-decoration-underline\">Grant</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"fw-bold text-decoration-underline\">Povolení</span>\n"
"                                    <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid ""
"<span class=\"fw-bold text-decoration-underline\">Grant</span>\n"
"                                    <br/>\n"
"                                    the value of the coupon"
msgstr ""
"<span class=\"fw-bold text-decoration-underline\">Povolení</span>\n"
"                                    <br/>\n"
"                                    hodnoty kupónu"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid ""
"<span class=\"fw-bold text-decoration-underline\">In exchange of</span>\n"
"                                    <br/>"
msgstr ""
"<span class=\"fw-bold text-decoration-underline\">Výměnou za</span>\n"
"                                    <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"program_type not in ('coupons', 'next_order_coupons')\">Coupons</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'loyalty'\">Loyalty Cards</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type not in ('promotion', 'buy_x_get_y')\">Promos</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'promo_code'\">Discount</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'gift_card'\">Gift Cards</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'ewallet'\">eWallets</span>"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid ""
"<span class=\"text-center\">OR</span>\n"
"                                        <br/>"
msgstr ""
"<span class=\"text-center\">NEBO</span>\n"
"                                        <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid ""
"<span colspan=\"2\" invisible=\"program_type != 'coupons'\">Grant the amount"
" of coupon points defined as the coupon value</span>"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "<span invisible=\"not clear_wallet\"> (or more)</span>"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid ""
"<span invisible=\"not will_send_mail\">\n"
"                            Generate and Send \n"
"                        </span>\n"
"                        <span invisible=\"will_send_mail\">\n"
"                            Generate \n"
"                        </span>"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "<span> x </span>"
msgstr "<span> x </span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "<span>Minimum purchase of</span>"
msgstr "<span>Minimální nákup</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "<span>Valid for purchase above</span>"
msgstr "<span>Platí pro nákup výše</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "<span>on</span>"
msgstr "<span>na</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "<span>products</span>"
msgstr "<span>produkty</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "<span>tax</span>"
msgstr "<span>daň</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "<strong>Gift Card Code</strong>"
msgstr "<strong>Kód dárkového poukazu</strong>"

#. module: loyalty
#: model:mail.template,body_html:loyalty.mail_template_loyalty_card
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto; background:#ffffff; color:#333333;\"><tbody>\n"
"<tr>\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object._get_mail_partner().name\">\n"
"            Congratulations <t t-out=\"object._get_mail_partner().name or ''\">Brandon Freeman</t>,<br>\n"
"        </t>\n"
"\n"
"        Here is your reward from <t t-out=\"object.program_id.company_id.name or ''\">YourCompany</t>.<br>\n"
"\n"
"        <t t-foreach=\"object.program_id.reward_ids\" t-as=\"reward\">\n"
"            <t t-if=\"reward.required_points &lt;= object.points\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-esc=\"reward.description\">Reward Description</span>\n"
"                <br>\n"
"            </t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"<tr style=\"margin-top: 16px\">\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        Use this promo code\n"
"        <t t-if=\"object.expiration_date\">\n"
"            before <t t-out=\"object.expiration_date or ''\">2021-06-16</t>\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">15637502648479132902</strong>\n"
"        </p>\n"
"        <t t-foreach=\"object.program_id.rule_ids\" t-as=\"rule\">\n"
"            <t t-if=\"rule.minimum_qty not in [0, 1]\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    Minimum purchase of <t t-out=\"rule.minimum_qty or ''\">10</t> products\n"
"                </span><br>\n"
"            </t>\n"
"            <t t-if=\"rule.minimum_amount != 0.00\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    Valid for purchase above <t t-out=\"rule.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(rule.minimum_amount) or ''\">10.00</t>\n"
"                </span><br>\n"
"            </t>\n"
"        </t>\n"
"        <br>\n"
"        Thank you,\n"
"        <t t-if=\"object._get_signature()\">\n"
"            <br>\n"
"            <t t-out=\"object._get_signature() or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"</tbody></table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto; background:#ffffff; color:#333333;\"><tbody>\n"
"<tr>\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object._get_mail_partner().name\">\n"
"            Gratulujeme <t t-out=\"object._get_mail_partner().name or ''\">Brandone Freemane</t>,<br>\n"
"        </t>\n"
"\n"
"        Zde je vaše odměna od <t t-out=\"object.program_id.company_id.name or ''\">VašíSpolečnosti</t>.<br>\n"
"\n"
"        <t t-foreach=\"object.program_id.reward_ids\" t-as=\"reward\">\n"
"            <t t-if=\"reward.required_points &lt;= object.points\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-esc=\"reward.description\">Popis odměny</span>\n"
"                <br>\n"
"            </t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"<tr style=\"margin-top: 16px\">\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        Využijte tento promo kód\n"
"        <t t-if=\"object.expiration_date\">\n"
"            před <t t-out=\"object.expiration_date or ''\">2021-06-16</t>\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">15637502648479132902</strong>\n"
"        </p>\n"
"        <t t-foreach=\"object.program_id.rule_ids\" t-as=\"rule\">\n"
"            <t t-if=\"rule.minimum_qty not in [0, 1]\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    Minimální odběr <t t-out=\"rule.minimum_qty or ''\">10</t> produktů\n"
"                </span><br>\n"
"            </t>\n"
"            <t t-if=\"rule.minimum_amount != 0.00\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    Platí pro nákup nad <t t-out=\"rule.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(rule.minimum_amount) or ''\">10.00</t>\n"
"                </span><br>\n"
"            </t>\n"
"        </t>\n"
"        <br>\n"
"        Děkujeme,\n"
"        <t t-if=\"object._get_signature()\">\n"
"            <br>\n"
"            <t t-out=\"object._get_signature() or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"</tbody></table>\n"
"        "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "A coupon with the same code was found."
msgstr "Byl nalezen kupón se stejným kódem."

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_card_card_code_unique
msgid "A coupon/loyalty card must have a unique code."
msgstr "Kupon/věrnostní karta musí mít unikátní kód."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "A program must have at least one reward."
msgstr "Program musí mít alespoň jednu odměnu."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_card.py:0
#, python-format
msgid "A trigger with the same code as one of your coupon already exists."
msgstr "Spouštěč se stejným kódem jako jeden z vašich kuponů již existuje."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "ABCDE12345"
msgstr ""

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_needaction
msgid "Action Needed"
msgstr "Vyžadována akce"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__active
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__active
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__active
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__active
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_search
msgid "Active"
msgstr "Aktivní"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_res_partner__loyalty_card_count
#: model:ir.model.fields,field_description:loyalty.field_res_users__loyalty_card_count
msgid "Active loyalty cards"
msgstr ""

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
#, python-format
msgid "Add"
msgstr "Přidat"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Add a reward"
msgstr "Přidat odměnu"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Add a rule"
msgstr "Přidat pravidlo"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__all_discount_product_ids
msgid "All Discount Product"
msgstr "Všechny produkty se slevou"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Among"
msgstr "Mezi"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_generate_wizard__mode__anonymous
msgid "Anonymous Customers"
msgstr "Anonymní zákazníci"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__mode
msgid "Application"
msgstr "Aplikace"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__applies_on
msgid "Applies On"
msgstr "Platí dne"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_search
msgid "Archived"
msgstr "Archivováno"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_mail__trigger__create
msgid "At Creation"
msgstr "U stvoření"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_attachment_count
msgid "Attachment Count"
msgstr "Počet příloh"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__trigger__auto
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__mode__auto
msgid "Automatic"
msgstr "Automaticky"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Automatic promo: 10% off on orders higher than $50"
msgstr ""

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__available_on
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Available On"
msgstr "K dispozici dne"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_tree
msgid "Balance"
msgstr "Zůstatek"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "Barcode"
msgstr "Čárové kódy"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Buy 10 products to get 10$ off on the 11th one"
msgstr ""

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Buy 2 products and get a third one for free"
msgstr ""

#. module: loyalty
#: model:loyalty.program,name:loyalty.3_cabinets_plus_1_free
msgid "Buy 3 large cabinets, get one for free"
msgstr "Kupte si 3 velké skříňky, jednu získáte zdarma"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__buy_x_get_y
#, python-format
msgid "Buy X Get Y"
msgstr "Kupte X a dostanete Y"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/wizard/loyalty_generate_wizard.py:0
#, python-format
msgid "Can not generate coupon, no program is set."
msgstr "Nelze vygenerovat kupón, není nastaven žádný program."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Cancel"
msgstr "Zrušit"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "Card expires"
msgstr "Platnost karty vyprší"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_category_id
msgid "Categories"
msgstr "Kategorie"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__discount_applicability__cheapest
msgid "Cheapest Product"
msgstr "Nejlevnější produkt"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__clear_wallet
msgid "Clear Wallet"
msgstr "Vymazat peněženku"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Clear all promo point(s)"
msgstr "Vymazat všechny propagační body"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__code
msgid "Code"
msgstr "Kód"

#. module: loyalty
#: model:loyalty.program,name:loyalty.10_percent_with_code
msgid "Code for 10% on orders"
msgstr "Kód pro 10% na objednávky"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__communication_plan_ids
msgid "Communication Plan"
msgstr "Komunikační plán"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Communications"
msgstr "Komunikace"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__company_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__company_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__company_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__company_id
msgid "Company"
msgstr "Společnost"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_card.py:0
#, python-format
msgid "Compose Email"
msgstr "Napsat e-mail"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__rule_ids
msgid "Conditional rules"
msgstr "Podmíněná pravidla"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Conditions"
msgstr "Podmínky"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Congratulations"
msgstr "Gratulujeme"

#. module: loyalty
#: model:ir.model,name:loyalty.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
#, python-format
msgid "Control panel buttons"
msgstr "Tlačítka na ovládacím panelu"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__coupon_ids
#, python-format
msgid "Coupon"
msgstr "Kupón"

#. module: loyalty
#: model:ir.actions.report,name:loyalty.report_loyalty_card
msgid "Coupon Code"
msgstr "Kód kupónu"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__coupon_count
msgid "Coupon Count"
msgstr "Počet kupónů"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Coupon point(s)"
msgstr "Kupónové bod(y)"

#. module: loyalty
#: model:loyalty.program,portal_point_name:loyalty.10_percent_coupon
msgid "Coupon points"
msgstr "Kupónové body"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Coupon value"
msgstr "Hodnota kupónu"

#. module: loyalty
#: model:mail.template,name:loyalty.mail_template_loyalty_card
msgid "Coupon: Coupon Information"
msgstr "Kupon: Informace o kuponu"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.actions.act_window,name:loyalty.loyalty_card_action
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__coupons
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_tree
#, python-format
msgid "Coupons"
msgstr "Kupony"

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_gift_ewallet_action
msgid "Create a new one from scratch, or use one of the templates below."
msgstr ""
"Vytvořte nový úplně od začátku nebo použijte jednu z níže uvedených šablon."

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_discount_loyalty_action
msgid "Create one from scratch, or use a templates below:"
msgstr ""

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
#, python-format
msgid "Create record"
msgstr "Vytvořit záznam"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__create_uid
msgid "Created by"
msgstr "Vytvořeno uživatelem"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__create_date
msgid "Created on"
msgstr "Vytvořeno dne"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:loyalty.program,portal_point_name:loyalty.3_cabinets_plus_1_free
#, python-format
msgid "Credit(s)"
msgstr "Kredit(y)"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__currency_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__currency_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__currency_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__currency_id
msgid "Currency"
msgstr "Měna"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__currency_symbol
msgid "Currency sign, to be used when printing amounts."
msgstr "Měnová značka, která se použije při tisku částek."

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__applies_on__both
msgid "Current & Future orders"
msgstr "Aktuální a budoucí objednávky"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__applies_on__current
msgid "Current order"
msgstr "Aktuální objednávka"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__customer_tag_ids
msgid "Customer Tags"
msgstr "Štítky zákazníků"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__customer_ids
msgid "Customers"
msgstr "Zákazníci"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "DEMO_CODE"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "DEMO_TEXT"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Define Discount codes on conditional rules then share it with your customers"
" for rewards."
msgstr ""
"Definujte slevové kódy na podmíněných pravidlech a poté je sdílejte se svými"
" zákazníky za odměny."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__description
msgid "Description"
msgstr "Popis"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Description on order"
msgstr "Popis na objednávce"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__reward_type__discount
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Discount"
msgstr "Sleva"

#. module: loyalty
#: model:ir.actions.act_window,name:loyalty.loyalty_program_discount_loyalty_action
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Discount & Loyalty"
msgstr "Slevy a věrnostní programy"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_applicability
msgid "Discount Applicability"
msgstr "Použitelnost slevy"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__promo_code
msgid "Discount Code"
msgstr "Slevový kód"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_line_product_id
msgid "Discount Line Product"
msgstr "Produkt slevové řady"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_mode
msgid "Discount Mode"
msgstr "Režim slevy"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__payment_program_discount_product_id
msgid "Discount Product"
msgstr "Zlevněný produkt"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_domain
msgid "Discount Product Domain"
msgstr "Doména zlevněného produktu"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__code
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
#, python-format
msgid "Discount code"
msgstr "Slevový kód"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:loyalty.program,portal_point_name:loyalty.10_percent_with_code
#, python-format
msgid "Discount point(s)"
msgstr "Slevové bod(y)"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Discount product"
msgstr "Zlevněný produkt"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_category_id
msgid "Discounted Prod. Categories"
msgstr "Kategorie zlevněných prod."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_tag_id
msgid "Discounted Prod. Tag"
msgstr "Štítek zlevněného prod."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_ids
msgid "Discounted Products"
msgstr "Zlevněné produkty"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Discounts"
msgstr "Slevy"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__display_name
msgid "Display Name"
msgstr "Zobrazovací název"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Displayed as"
msgstr "Zobrazeno jako"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Drive repeat purchases by sending a unique, single-use coupon code for the "
"next purchase when a customer buys something in your store."
msgstr ""
"Podpořte opakované nákupy odesláním jedinečného kódu jednorázového kupónu "
"pro příští nákup, když si zákazník něco koupí ve vašem obchodě."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Each rule can grant points to the customer he will be able to exchange "
"against rewards"
msgstr ""
"Každé pravidlo může udělit body zákazníkovi, kterého bude moci vyměnit za "
"odměny"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__mail_template_id
msgid "Email Template"
msgstr "Emailová šablona"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__mail_template_id
msgid "Email template"
msgstr "Emailová šablona"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__date_to
msgid "End date"
msgstr "Konečné datum"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__minimum_amount_tax_mode__excl
msgid "Excluded"
msgstr "Vyloučeno"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__expiration_date
msgid "Expiration Date"
msgstr "Datum platnosti"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_card.py:0
#, python-format
msgid "Expiration date cannot be set on a loyalty card."
msgstr ""

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Fidelity Card"
msgstr ""

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Fidelity Cards"
msgstr "Věrnostní karty"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Fill in your eWallet, to pay future orders"
msgstr ""

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_follower_ids
msgid "Followers"
msgstr "Odběratelé"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_partner_ids
msgid "Followers (Partners)"
msgstr "Odběratelé (partneři)"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__mode
msgid "For"
msgstr "Pro"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__reward_type__product
#, python-format
msgid "Free Product"
msgstr "Produkt zdarma"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "Free Product - %s"
msgstr "Produkt zdarma- %s"

#. module: loyalty
#: model:loyalty.reward,description:loyalty.3_cabinets_plus_1_free_reward
msgid "Free Product - Large Cabinet"
msgstr "Produkt zdarma - velká skříňka"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "Free Product - [%s]"
msgstr "Produkt zdarma - [%s]"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "Free product"
msgstr "Produkt zdarma"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__applies_on__future
msgid "Future orders"
msgstr "Budoucí objednávky"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
#: model:ir.actions.act_window,name:loyalty.loyalty_generate_wizard_action
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
#, python-format
msgid "Generate"
msgstr "Generovat"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Generate &amp; share coupon codes manually. It can be used in eCommerce, "
"Point of Sale or regular orders to claim the Reward. You can define "
"constraints on its usage through conditional rule."
msgstr ""
"Generujte &amp; sdílejte kódy kupónů ručně. Lze jej použít v e-shopu, v "
"prodejním místě nebo běžných objednávkách k uplatnění Odměny. Pomocí "
"podmíněného pravidla můžete definovat omezení jeho použití."

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_generate_wizard
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Generate Coupons"
msgstr "Generovat kupóny"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Generate Gift Cards"
msgstr "Generovat dárkové poukazy"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Generate and share unique coupons with your customers"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Generate eWallet"
msgstr "Generovat digitální peněženku"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Get 10% off on some products, with a code"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_gift_ewallet_view_form
msgid "Gift &amp; Ewallet"
msgstr "Dárek a digitální peněženka"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_reward.py:0
#: model:ir.actions.report,name:loyalty.report_gift_card
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__gift_card
#: model:loyalty.reward,description:loyalty.gift_card_program_reward
#: model:product.template,name:loyalty.gift_card_product_50_product_template
#, python-format
msgid "Gift Card"
msgstr "Dárkový poukaz"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Gift Card Products"
msgstr "Produkty dárkových poukazů"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Gift Card value"
msgstr "Hodnota dárkového poukazu"

#. module: loyalty
#: model:mail.template,name:loyalty.mail_template_gift_card
msgid "Gift Card: Gift Card Information"
msgstr "Dárkový poukaz: Informace o dárkovém poukaze"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:loyalty.program,name:loyalty.gift_card_program
#, python-format
msgid "Gift Cards"
msgstr "Dárkové poukazy"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Gift Cards are created manually or automatically sent by email when the customer orders a gift card product.\n"
"                                    <br/>\n"
"                                    Then, Gift Cards can be used to pay orders."
msgstr ""
"Dárkové karty jsou vytvářeny ručně nebo automaticky zasílány e-mailem, když si zákazník objedná produkt dárkové karty.\n"
"                                    <br/>\n"
"                                    Poté lze dárkové karty použít k platbě objednávek."

#. module: loyalty
#: model:ir.actions.act_window,name:loyalty.loyalty_program_gift_ewallet_action
msgid "Gift cards & eWallet"
msgstr "Poukazy a digitální peněženky"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__points_granted
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Grant"
msgstr "Povolení"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Grant 1 credit for each item bought then reward the customer with Y items in"
" exchange of X credits."
msgstr ""
"Udělte 1 kredit za každou zakoupenou položku a poté odměňte zákazníka Y "
"položkami výměnou za X kreditů."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__has_message
msgid "Has Message"
msgstr "Má zprávu"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "Here is your gift card!"
msgstr "Zde je váš dárkový poukaz!"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Here is your reward from"
msgstr "Tady je vaše odměna od"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__id
msgid "ID"
msgstr "ID"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Pokud zaškrtnuto, nové zprávy vyžadují vaši pozornost."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Pokud zaškrtnuto, některé zprávy mají chybu při doručení."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "If minimum"
msgstr "Pokud minimum"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "In exchange of"
msgstr "Výměnou za"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_search
msgid "Inactive"
msgstr "Neaktivní"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__minimum_amount_tax_mode__incl
msgid "Included"
msgstr "Včetně"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/wizard/loyalty_generate_wizard.py:0
#, python-format
msgid "Invalid quantity."
msgstr "Neplatné množství."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_is_follower
msgid "Is Follower"
msgstr "Je odběratel"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__is_global_discount
msgid "Is Global Discount"
msgstr "Je globální sleva"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__is_nominative
msgid "Is Nominative"
msgstr "Je nominativní"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__is_payment_program
msgid "Is Payment Program"
msgstr "Je platební program"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__coupon_count_display
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_tree
msgid "Items"
msgstr "Položky"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "John Doe"
msgstr "Jan Testovací"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno uživatelem"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__write_date
msgid "Last Updated on"
msgstr "Naposledy upraveno dne"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_mail_view_tree
msgid "Limit"
msgstr "Omezení"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__limit_usage
msgid "Limit Usage"
msgstr "Limit použití"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Logo"
msgstr "Logo"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Loyalty Card"
msgstr ""

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.res_partner_form
#, python-format
msgid "Loyalty Cards"
msgstr "Věrnostní karty"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_mail
msgid "Loyalty Communication"
msgstr "Věrnostní komunikace"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr "Věrnostní kupón"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_program
msgid "Loyalty Program"
msgstr "Věrnostní program"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_reward
msgid "Loyalty Reward"
msgstr "Věrnostní odměna"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_rule
msgid "Loyalty Rule"
msgstr "Věrnostní pravidlo"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Loyalty point(s)"
msgstr "Věrnostní bod(y)"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_max_amount
msgid "Max Discount"
msgstr "Max sleva"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__max_usage
msgid "Max Usage"
msgstr "Max využití"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_program_check_max_usage
msgid "Max usage must be strictly positive if a limit is used."
msgstr "Max využití musí být striktně kladné, pokud je použit limit."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_has_error
msgid "Message Delivery error"
msgstr "Chyba při doručování zprávy"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_ids
msgid "Messages"
msgstr "Zprávy"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__minimum_amount_tax_mode
msgid "Minimum Amount Tax Mode"
msgstr "Režim minimální částky daně"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__minimum_amount
msgid "Minimum Purchase"
msgstr "Minimální nákup"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__minimum_qty
msgid "Minimum Quantity"
msgstr "Minimální množství"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__multi_product
msgid "Multi Product"
msgstr "Multiprodukt"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Next Order Coupon"
msgstr "Kupón pro další objednávku"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__next_order_coupons
#, python-format
msgid "Next Order Coupons"
msgstr "Kupóny pro další objednávky"

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_card_action
msgid "No Coupons Found."
msgstr "Nebyly nalezeny žádné kupóny."

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_gift_ewallet_action
msgid "No loyalty program found."
msgstr "Nelezen žádný věrnostní program."

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_discount_loyalty_action
msgid "No program found."
msgstr "Nenalezen žádný program."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcí"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chyb"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Počet zpráv vyžadujících akci"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet zpráv s chybou při doručení"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Odoo"
msgstr "Odoo"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__discount_applicability__order
msgid "Order"
msgstr "Objednávka"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__partner_id
msgid "Partner"
msgstr "Partner"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Point(s)"
msgstr "Bod(y)"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__points
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__points
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Points"
msgstr "Body"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__points_display
msgid "Points Display"
msgstr "Zobrazení bodů"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Points Unit"
msgstr "Jednotka bodů"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__required_points
msgid "Points needed"
msgstr "Potřebné body"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__point_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__points_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__portal_point_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__point_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_name
msgid "Portal Point Name"
msgstr "Název bodu portálu"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__portal_visible
msgid "Portal Visible"
msgstr "Portál viditelný"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__pricelist_ids
msgid "Pricelist"
msgstr "Ceník"

#. module: loyalty
#: model:ir.model,name:loyalty.model_product_template
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_id
msgid "Product"
msgstr "Produkt"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_domain
msgid "Product Domain"
msgstr "Doména produktu"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_tag_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_tag_id
msgid "Product Tag"
msgstr "Produktový štítek"

#. module: loyalty
#: model:ir.model,name:loyalty.model_product_product
msgid "Product Variant"
msgstr "Produktová varianta"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__payment_program_discount_product_id
msgid "Product used in the sales order to apply the discount."
msgstr "Produkt použitý v objednávce k uplatnění slevy."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_reward__discount_line_product_id
msgid ""
"Product used in the sales order to apply the discount. Each reward has its "
"own product for reporting purpose"
msgstr ""
"Produkt použitý v prodejní objednávce k uplatnění slevy. Každá odměna má "
"svůj vlastní produkt pro účely výkazu"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__trigger_product_ids
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_ids
msgid "Products"
msgstr "Produkty"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__program_id
msgid "Program"
msgstr "Program"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__name
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Program Name"
msgstr "Název programu"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__program_type
msgid "Program Type"
msgstr "Typ programu"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Program trigger"
msgstr "Spouštěč programu"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Promo Code"
msgstr "Promo kód"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Promo point(s)"
msgstr "Promo bod(y)"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Promos"
msgstr "Propagační akce"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Promotional Program"
msgstr "Propagační program"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__promotion
msgid "Promotions"
msgstr "Propagace"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__coupon_qty
msgid "Quantity"
msgstr "Množství"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Quantity rewarded"
msgstr "Odměněné množství"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Quantity to generate"
msgstr "Množství k vytvoření"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__rating_ids
msgid "Ratings"
msgstr "Hodnocení"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_amount
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Reward"
msgstr "Odměna"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_mode
msgid "Reward Point Mode"
msgstr "Režim bodů odměny"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_domain
msgid "Reward Product Domain"
msgstr "Doména odměny za produkt"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_qty
msgid "Reward Product Qty"
msgstr "Množství produktu za odměnu"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_uom_id
msgid "Reward Product Uom"
msgstr "MJ produktu za odměnu"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_ids
msgid "Reward Products"
msgstr "Produkty za odměnu"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_type
msgid "Reward Type"
msgstr "Typ odměny"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__reward_ids
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Rewards"
msgstr "Odměny"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_rule_reward_point_amount_positive
msgid "Rule points reward must be strictly positive."
msgstr "Pravidlo odměn za body musí být pouze kladné."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Rules & Rewards"
msgstr "Pravidla a odměny"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_generate_wizard__mode__selected
msgid "Selected Customers"
msgstr "Vybraní zákazníci"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Sell Gift Cards, that allows to purchase products"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_tree
msgid "Send"
msgstr "Odeslat"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Send a coupon after an order, valid for next purchase"
msgstr ""

#. module: loyalty
#: model:mail.template,description:loyalty.mail_template_gift_card
msgid "Sent to customer who purchased a gift card"
msgstr "Odesláno zákazníkovi, který si zakoupil dárkovou kartu"

#. module: loyalty
#: model:mail.template,description:loyalty.mail_template_loyalty_card
msgid "Sent to customer with coupon information"
msgstr "Odesláno zákazníkovi s informacemi o kupónu"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__sequence
msgid "Sequence"
msgstr "Sekvence"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Set up conditional rules on the order that will give access to rewards for "
"customers"
msgstr ""
"Nastavte podmíněná pravidla pro objednávku, která zákazníkům umožní přístup "
"k odměnám"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Show points Unit"
msgstr "Zobrazit jednotku bodů"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__discount_applicability__specific
msgid "Specific Products"
msgstr "Konkrétní produkty"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_split
msgid "Split per unit"
msgstr "Rozdělit na jednotku"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "Split per unit is not allowed for Loyalty and eWallet programs."
msgstr ""
"Rozdělení na jednotku není povoleno pro věrnostní programy a programy "
"digitální peněženky."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__date_from
msgid "Start Date"
msgstr "Počáteční datum"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__currency_symbol
msgid "Symbol"
msgstr "Symbol"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Thank you,"
msgstr "Děkuji,"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_reward_discount_positive
msgid "The discount must be strictly positive."
msgstr "Sleva musí být pouze kladná."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__date_to
msgid "The end date is included in the validity period of this program"
msgstr ""

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid ""
"The loyalty program's currency must be the same as all it's pricelists ones."
msgstr ""

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "The promo code must be unique."
msgstr "Promo kód musí být jedinečný."

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_reward_required_points_positive
msgid "The required points for a reward must be strictly positive."
msgstr "Požadované body pro odměnu musí být pouze kladné."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "The reward description field cannot be empty."
msgstr "Pole popisu odměny nesmí být prázdné."

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_reward_product_qty_positive
msgid "The reward product quantity must be strictly positive."
msgstr "Množství produktu za odměnu musí být pouze kladné."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__date_from
msgid "The start date is included in the validity period of this program"
msgstr ""

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid ""
"The validity period's start date must be anterior or equal to its end date."
msgstr ""

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_card_action
msgid "There haven't been any coupons generated yet."
msgstr "Dosud nebyly vygenerovány žádné kupóny."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_reward__reward_product_ids
msgid "These are the products that can be claimed with this rule."
msgstr "Toto jsou produkty, které lze reklamovat podle tohoto pravidla."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_reward__discount_max_amount
msgid ""
"This is the max amount this reward may discount, leave to 0 for no limit."
msgstr ""
"Toto je maximální částka, kterou může tato odměna slevit, ponechte na 0 pro "
"neomezenou."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/product_product.py:0
#, python-format
msgid ""
"This product may not be archived. It is being used for an active promotion "
"program."
msgstr ""
"Tento produkt nemusí být archivován. Používá se pro aktivní propagační "
"program."

#. module: loyalty
#: model:product.template,name:loyalty.ewallet_product_50_product_template
msgid "Top-up eWallet"
msgstr "Dobití digitální peněženky"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__total_order_count
msgid "Total Order Count"
msgstr "Celkový počet objednávek"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__trigger
msgid "Trigger"
msgstr "Spouštěč"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "Unsupported search operator"
msgstr ""

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__use_count
msgid "Use Count"
msgstr "Použijte počet"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__trigger__with_code
msgid "Use a code"
msgstr "Použijte kód"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Use points on"
msgstr "Využít body na"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Use this promo code before"
msgstr "Použijte tento promo kód před"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__user_has_debug
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__user_has_debug
msgid "User Has Debug"
msgstr "Uživatel má debug"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__valid_until
msgid "Valid Until"
msgstr "Platné do"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__trigger
msgid "When"
msgstr "Kdy"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_mail__trigger__points_reach
msgid "When Reaching"
msgstr "Při dosažení"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"When customers make an order, they accumulate points they can exchange for "
"rewards on the current order or on a future one."
msgstr ""
"Když zákazníci vytvoří objednávku, shromažďují body, které mohou vyměnit za "
"odměny za aktuální nebo budoucí objednávku."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"When generating coupon, you can define a specific points value that can be "
"exchanged for rewards."
msgstr ""
"Při generování kupónu můžete definovat konkrétní bodovou hodnotu, kterou lze"
" vyměnit za odměny."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_rule__reward_point_split
msgid ""
"Whether to separate reward coupons per matched unit, only applies to "
"'future' programs and trigger mode per money spent or unit paid.."
msgstr ""
"Zda oddělit kupóny na odměnu za odpovídající jednotku, se vztahuje pouze na "
"„budoucí“ programy a režim spouštění na utracené peníze nebo zaplacenou "
"jednotku."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__will_send_mail
msgid "Will Send Mail"
msgstr "Odešle poštou"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "Win points with each purchase, and claim gifts"
msgstr ""

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__mode__with_code
msgid "With a promotion code"
msgstr "S propagačním kódem"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "You can not delete a program in an active state"
msgstr "Nemůžete smazat program v aktivním stavu"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/product_product.py:0
#: code:addons/loyalty/models/product_template.py:0
#, python-format
msgid ""
"You cannot delete %(name)s as it is used in 'Coupons & Loyalty'. Please "
"archive it instead."
msgstr ""
"Nemůžete smazat %(name)s, protože se používá v části „Kupóny a věrnostní "
"programy“. Místo toho jej prosím archivujte."

#. module: loyalty
#: model:mail.template,subject:loyalty.mail_template_gift_card
msgid "Your Gift Card at {{ object.company_id.name }}"
msgstr "Váš dárkový poukaz v {{ object.company_id.name }}"

#. module: loyalty
#: model:mail.template,subject:loyalty.mail_template_loyalty_card
msgid "Your reward coupon from {{ object.program_id.company_id.name }} "
msgstr "Váš kupón na odměnu z {{ object.program_id.company_id.name }}"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "all"
msgstr "vše"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "discount"
msgstr "sleva"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "e.g. 10% discount on laptops"
msgstr "např. 10% sleva na laptopy"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_reward.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__ewallet
#, python-format
msgid "eWallet"
msgstr "Digitální peněženka"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "eWallet Products"
msgstr "Produkty digitální peněženky"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "eWallet value"
msgstr "Hodnota digitální peněženky"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#, python-format
msgid "eWallets"
msgstr "Digitální peněženky"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"eWallets are created manually or automatically when the customer orders a eWallet product.\n"
"                                    <br/>\n"
"                                    Then, eWallets are proposed during the checkout, to pay orders."
msgstr ""
"Digitální peněženky jsou vytvářeny ručně nebo automaticky, když si zákazník objedná produkt digitální peněženky.\n"
"                                    <br/>\n"
"                                    Poté jsou při pokladně navrženy digitální peněženky, které budou platit objednávky."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "item(s) bought"
msgstr "koupených produkt(ů)"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "loyalty Reward"
msgstr ""

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid ""
"on the cheapest product\n"
"                                        <br/>"
msgstr ""
"na nejlevnější produkt\n"
"                                        <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "on your next order"
msgstr "na vaši další objednávku"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid ""
"on your order\n"
"                                        <br/>"
msgstr ""
"na vaši objednávku\n"
"                                        <br/>"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "per %s spent"
msgstr "za %s utracené"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "per order"
msgstr "na objednávku"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
#, python-format
msgid "per unit paid"
msgstr "za zaplacenou jednotku"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "specific products"
msgstr "konkrétní produkty"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "spent"
msgstr "utraceno"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "the cheapest product"
msgstr "nejlevnější produkt"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "to"
msgstr "k"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "usages"
msgstr "použití"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "used)"
msgstr ""

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#, python-format
msgid "your order"
msgstr "Vaše objednávka"
