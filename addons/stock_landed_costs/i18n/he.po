# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_landed_costs
# 
# Translators:
# NoaFarkash, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# Michael<PERSON><PERSON>r, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON>fu<PERSON> <PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2024
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-29 10:44+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid " already out"
msgstr "כבר בחוץ"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.stock_landed_cost_view_kanban
msgid "<i class=\"fa fa-clock-o\" title=\"Date\" role=\"img\" aria-label=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" title=\"Date\" role=\"img\" aria-label=\"Date\"/>"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr "<span class=\"o_stat_text\">הערכה</span>"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_account_move_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"מוצר מנוהל מלאי הוא מוצר שעבורו מתבצע ניהול מלאי במערכת. יש להתקין את יישום "
"המלאי.מוצר לא מנוהל מלאי הוא מוצר אשר לא מתבצע עבורו ניהול מלאי במערכת.שירות"
" הוא מוצר לא חומרי שאתה מספק."

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__account_id
msgid "Account"
msgstr "חשבון"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__account_journal_id
msgid "Account Journal"
msgstr "יומן חשבון"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_ids
msgid "Activities"
msgstr "פעילויות"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Additional Costs"
msgstr "עלויות נוספות"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__additional_landed_cost
msgid "Additional Landed Cost"
msgstr "עלויות הוצאות הובלה נוספות"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__target_model
msgid "Apply On"
msgstr "החל על"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_current_cost_price
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_current_cost_price
msgid "By Current Cost"
msgstr "לפי עלות נוכחית"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_quantity
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_quantity
msgid "By Quantity"
msgstr "לפי כמות"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_volume
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_volume
msgid "By Volume"
msgstr "לפי נפח"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__by_weight
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__by_weight
msgid "By Weight"
msgstr "לפי משקל"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Cancel"
msgstr "בטל"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__state__cancel
msgid "Cancelled"
msgstr "בוטל"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__company_id
msgid "Company"
msgstr "חברה"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Compute"
msgstr "חשב"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_res_config_settings
msgid "Config Settings"
msgstr "הגדר הגדרות"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__price_unit
msgid "Cost"
msgstr "עלות"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__cost_line_id
msgid "Cost Line"
msgstr "שורת עלות"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__cost_lines
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Cost Lines"
msgstr "שורות עלות "

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid ""
"Cost and adjustments lines do not match. You should maybe recompute the "
"landed costs."
msgstr ""
"שורות עלות והתאמות אינן תואמות. אולי כדאי לחשב מחדש את עלויות הוצאות ההובלה."

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.account_view_move_form_inherited
msgid "Create Landed Costs"
msgstr "צור עלויות הוצאות הובלה"

#. module: stock_landed_costs
#: model_terms:ir.actions.act_window,help:stock_landed_costs.action_stock_landed_cost
msgid "Create a new landed cost"
msgstr "צור עלות הוצאת הובלה חדשה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__create_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__create_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__create_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__create_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__currency_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__currency_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__currency_id
msgid "Currency"
msgstr "מטבע"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__date
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Date"
msgstr "תאריך"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_res_config_settings__lc_journal_id
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.res_config_settings_view_form
msgid "Default Journal"
msgstr "יומן ברירת מחדל"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_product__split_method_landed_cost
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_template__split_method_landed_cost
msgid "Default Split Method"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_product_product__split_method_landed_cost
#: model:ir.model.fields,help:stock_landed_costs.field_product_template__split_method_landed_cost
msgid "Default Split Method when used for Landed Cost"
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__name
msgid "Description"
msgstr "תיאור"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__display_name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__display_name
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: stock_landed_costs
#: model:mail.message.subtype,name:stock_landed_costs.mt_stock_landed_cost_open
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Done"
msgstr "בוצע"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__state__draft
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Draft"
msgstr "טיוטה"

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__product_template__split_method_landed_cost__equal
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost_lines__split_method__equal
msgid "Equal"
msgstr "שווה"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost_lines__split_method
msgid ""
"Equal: Cost will be equally divided.\n"
"By Quantity: Cost will be divided according to product's quantity.\n"
"By Current cost: Cost will be divided according to product's current cost.\n"
"By Weight: Cost will be divided depending on its weight.\n"
"By Volume: Cost will be divided depending on its volume."
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Future Activities"
msgstr "פעילויות עתידיות"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Group By"
msgstr "קבץ לפי"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__id
msgid "ID"
msgstr "מזהה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_needaction
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_has_error
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_product_product__landed_cost_ok
#: model:ir.model.fields,help:stock_landed_costs.field_product_template__landed_cost_ok
msgid ""
"Indicates whether the product is a landed cost: when receiving a vendor "
"bill, you can allocate this cost on preceding receipts."
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move_line__is_landed_costs_line
msgid "Is Landed Costs Line"
msgstr "שורת עלויות הוצאות הובלה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_product__landed_cost_ok
#: model:ir.model.fields,field_description:stock_landed_costs.field_product_template__landed_cost_ok
msgid "Is a Landed Cost"
msgstr "עלויות הוצאות הובלה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__description
msgid "Item Description"
msgstr "תיאור פריט"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Journal"
msgstr "יומן"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_account_move
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__account_move_id
msgid "Journal Entry"
msgstr "פקודת יומן"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_account_move_line
msgid "Journal Item"
msgstr "תנועת יומן"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__cost_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__cost_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_layer__stock_landed_cost_id
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Landed Cost"
msgstr "עלות הוצאת הובלה"

#. module: stock_landed_costs
#: model:ir.actions.act_window,name:stock_landed_costs.action_stock_landed_cost
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_bank_statement_line__landed_costs_ids
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move__landed_costs_ids
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_payment__landed_costs_ids
#: model:ir.ui.menu,name:stock_landed_costs.menu_stock_landed_cost
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.account_view_move_form_inherited
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_tree
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_tree2
msgid "Landed Costs"
msgstr "עלויות הוצאות הובלה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_bank_statement_line__landed_costs_visible
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move__landed_costs_visible
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_payment__landed_costs_visible
msgid "Landed Costs Visible"
msgstr "עלויות הוצאות הובלה גלויות"

#. module: stock_landed_costs
#: model:mail.message.subtype,description:stock_landed_costs.mt_stock_landed_cost_open
msgid "Landed cost validated"
msgstr "עלויות הוצאות הובלה אומתו"

#. module: stock_landed_costs
#: model_terms:ir.actions.act_window,help:stock_landed_costs.action_stock_landed_cost
msgid ""
"Landed costs allow you to include additional costs (shipment, insurance, "
"customs duties, etc) into the cost of the product."
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__write_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__write_uid
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__write_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__write_date
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Late Activities"
msgstr "פעילויות באיחור"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_res_company__lc_journal_id
msgid "Lc Journal"
msgstr "יומן עלויות הוצאות הובלה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__name
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Name"
msgstr "שם"

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "New"
msgstr "חדש"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__final_cost
msgid "New Value"
msgstr "ערך חדש"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "הפעילות הבאה ביומן"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "מספר הודעות הדורשות פעולה"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Only draft landed costs can be validated"
msgstr "ניתן לאמת רק טיוטת עלויות הוצאות הובלה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__former_cost
msgid "Original Value"
msgstr "ערך מקורי"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Picking"
msgstr "ליקוט"

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Please configure Stock Expense Account for product: %s."
msgstr "הגדר חשבון הוצאות מלאי עבור מוצר: %s."

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid "Please define %s on which those additional costs should apply."
msgstr ""

#. module: stock_landed_costs
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__state__done
msgid "Posted"
msgstr "נרשם"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_product_template
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__product_id
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__product_id
msgid "Product"
msgstr "מוצר"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_account_move_line__product_type
msgid "Product Type"
msgstr "סוג מוצר"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "שורת הזמנת רכש"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__quantity
msgid "Quantity"
msgstr "כמות"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__rating_ids
msgid "Ratings"
msgstr "דירוגים"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__message_has_sms_error
msgid "SMS Delivery error"
msgstr "שגיאה בשליחת SMS"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Show all records which has next action date is before today"
msgstr "הצג את כל הרשומות שתאריך הפעולה הבא שלהן הוא עד היום"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost_lines__split_method
msgid "Split Method"
msgstr "שיטת פיצול"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__state
msgid "State"
msgstr "מדינה"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Status"
msgstr "סטטוס"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_landed_cost
msgid "Stock Landed Cost"
msgstr "מלאי עלויות הוצאות הובלה"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_landed_cost_lines
msgid "Stock Landed Cost Line"
msgstr "שורת מלאי עלויות הוצאות הובלה"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__move_id
msgid "Stock Move"
msgstr "תנועת מלאי"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_valuation_layer
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr "שכבת הערכת שווי מלאי"

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_search
msgid "Today Activities"
msgstr "פעילויות היום"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__amount_total
msgid "Total"
msgstr "סה\"כ"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__picking_ids
#: model:ir.model.fields.selection,name:stock_landed_costs.selection__stock_landed_cost__target_model__picking
msgid "Transfers"
msgstr "העברות"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Validate"
msgstr "אשר"

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid ""
"Validated landed costs cannot be cancelled, but you could create negative "
"landed costs to reverse them"
msgstr ""
"לא ניתן לבטל עלויות הוצאות הובלה מאומתות, אך ניתן ליצור עלויות הוצאות הובלה "
"שליליות כדי לבטל אותן"

#. module: stock_landed_costs
#: model:ir.model,name:stock_landed_costs.model_stock_valuation_adjustment_lines
msgid "Valuation Adjustment Lines"
msgstr "שורות התאמות שווי"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__valuation_adjustment_lines
#: model_terms:ir.ui.view,arch_db:stock_landed_costs.view_stock_landed_cost_form
msgid "Valuation Adjustments"
msgstr "התאמות שווי"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__vendor_bill_id
msgid "Vendor Bill"
msgstr "חשבונית ספק"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__volume
msgid "Volume"
msgstr "נפח"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_landed_cost__website_message_ids
msgid "Website Messages"
msgstr "הודעות מאתר האינטרנט"

#. module: stock_landed_costs
#: model:ir.model.fields,help:stock_landed_costs.field_stock_landed_cost__website_message_ids
msgid "Website communication history"
msgstr "היסטורית התקשרויות מאתר האינטרנט"

#. module: stock_landed_costs
#: model:ir.model.fields,field_description:stock_landed_costs.field_stock_valuation_adjustment_lines__weight
msgid "Weight"
msgstr "משקל"

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/stock_landed_cost.py:0
#, python-format
msgid ""
"You cannot apply landed costs on the chosen %s(s). Landed costs can only be "
"applied for products with FIFO or average costing method."
msgstr ""
"אתה לא יכול להחיל עלויות  העמסה על ה%sשנבחרו. עלויות העמסה יכולות להתבצע רק "
"עבור מוצרים עם שיטת חישוב FIFO או חישוב ממוצע."

#. module: stock_landed_costs
#. odoo-python
#: code:addons/stock_landed_costs/models/product.py:0
#, python-format
msgid ""
"You cannot change the product type or disable landed cost option because the"
" product is used in an account move line."
msgstr ""
"אתה לא יכול לשנות את סוג המוצר או לבטל את אפשרות עלות העמסה כי המוצר נמצא "
"בשורת תנועת חשבון."
