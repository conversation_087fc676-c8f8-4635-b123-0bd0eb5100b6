# -*- coding: utf-8 -*-
from odoo import models, api, _
from odoo import exceptions
import logging

_logger = logging.getLogger(__name__)


class CrmTeam(models.Model):
    _inherit = 'crm.team'

    def action_assign_leads_even_distribution(self, work_days=1, log=True):
        """
        Enhanced version of action_assign_leads that ensures more even distribution
        of leads among team members.
        
        This method uses round-robin assignment instead of weighted random choice
        to achieve better distribution fairness.
        
        :param float work_days: number of work days to consider when assigning leads
        :param bool log: whether to log the assignment process
        :return action: a client notification giving insights on assign process
        """
        teams_data, members_data = self._action_assign_leads_even_distribution(work_days=work_days)

        # format result messages
        logs = self._action_assign_leads_logs(teams_data, members_data)
        html_message = '<br />'.join(logs) if logs else 'No leads assigned.'
        notif_message = ' '.join(logs) if logs else 'No leads assigned.'

        # log a note in case of manual assign
        if log:
            from odoo.tools import html
            log_action = _("Even Distribution Lead Assignment requested by %(user_name)s", user_name=self.env.user.name)
            log_message = f"<p>{log_action}<br /><br />{html_message}</p>"
            self._message_log_batch(bodies=dict((team.id, log_message) for team in self))

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Lead Assignment'),
                'message': notif_message,
                'type': 'success',
                'sticky': False,
            }
        }

    def _action_assign_leads_even_distribution(self, work_days=1):
        """
        Enhanced private method for lead assignment using even distribution.
        
        :param float work_days: see ``CrmTeam.action_assign_leads_even_distribution()``;
        :return teams_data, members_data: structure-based result of assignment process
        """
        if not self.env.user.has_group('sales_team.group_sale_manager') and not self.env.user.has_group('base.group_system'):
            raise exceptions.UserError(_('Lead/Opportunities automatic assignment is limited to managers or administrators'))

        _logger.info('### START Even Distribution Lead Assignment (%d teams, %d sales persons, %.2f work_days)', 
                    len(self), len(self.crm_team_member_ids), work_days)
        
        # First allocate leads to teams (same as original)
        teams_data = self._allocate_leads(work_days=work_days)
        _logger.info('### Team repartition done. Starting even distribution salesmen assignment.')
        
        # Use enhanced member assignment
        members_data = self.crm_team_member_ids._assign_and_convert_leads_even_distribution(work_days=work_days)
        _logger.info('### END Even Distribution Lead Assignment')
        
        return teams_data, members_data

    def action_assign_leads_comparison(self, work_days=1):
        """
        Method to compare original vs even distribution assignment.
        Useful for testing and demonstration purposes.
        
        :param float work_days: number of work days to consider
        :return: comparison results
        """
        _logger.info('=== STARTING ASSIGNMENT COMPARISON ===')
        
        # Get current state
        initial_state = {}
        for member in self.crm_team_member_ids:
            initial_state[member.id] = {
                'name': member.user_id.name,
                'current_count': member.lead_month_count,
                'max_capacity': member.assignment_max
            }
        
        # Count available leads
        available_leads = self.env['crm.lead'].search_count([
            ('team_id', 'in', self.ids),
            ('user_id', '=', False),
            ('date_open', '=', False)
        ])
        
        _logger.info(f'Initial state: {available_leads} leads available for assignment')
        for member_id, data in initial_state.items():
            _logger.info(f"Member {data['name']}: {data['current_count']}/{data['max_capacity']} leads")
        
        # Perform even distribution assignment
        _logger.info('--- Performing EVEN DISTRIBUTION assignment ---')
        teams_data, members_data = self._action_assign_leads_even_distribution(work_days=work_days)
        
        # Log results
        total_assigned = sum(len(member_data['assigned']) for member_data in members_data.values())
        _logger.info(f'Even distribution assigned {total_assigned} leads')
        
        distribution_summary = {}
        for member, member_data in members_data.items():
            assigned_count = len(member_data['assigned'])
            if assigned_count > 0:
                distribution_summary[member.user_id.name] = assigned_count
                _logger.info(f"Member {member.user_id.name}: +{assigned_count} leads")
        
        _logger.info('=== ASSIGNMENT COMPARISON COMPLETED ===')
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Assignment Comparison'),
                'message': f'Even distribution assigned {total_assigned} leads. Check logs for details.',
                'type': 'info',
                'sticky': True,
            }
        }

    @api.model
    def _cron_assign_leads_even_distribution(self):
        """
        Cron method to automatically assign leads using even distribution every 3 minutes.
        This method finds teams that have unassigned leads and applies the even distribution
        assignment algorithm.
        """
        # Find teams that are eligible for auto-assignment
        teams_to_process = self.search([
            '&', '|', ('use_leads', '=', True), ('use_opportunities', '=', True),
            ('assignment_optout', '=', False),
            ('crm_team_member_ids', '!=', False)  # Teams must have members
        ])

        if not teams_to_process:
            _logger.info("Cron: No teams found for even distribution assignment")
            return True

        _logger.info(f"Cron: Found {len(teams_to_process)} teams for even distribution assignment")

        total_assigned = 0
        for team in teams_to_process:
            try:
                # Check if team has unassigned leads
                unassigned_leads_count = self.env['crm.lead'].search_count([
                    ('team_id', '=', team.id),
                    ('user_id', '=', False),
                    ('date_open', '=', False)
                ])

                if unassigned_leads_count > 0:
                    _logger.info(f"Cron: Team '{team.name}' has {unassigned_leads_count} unassigned leads")

                    # Perform even distribution assignment
                    teams_data, members_data = team._action_assign_leads_even_distribution(work_days=0.2)

                    # Count assignments made
                    team_assigned = sum(len(member_data['assigned']) for member_data in members_data.values())
                    total_assigned += team_assigned

                    if team_assigned > 0:
                        _logger.info(f"Cron: Assigned {team_assigned} leads to team '{team.name}' using even distribution")

                        # Log individual member assignments
                        for member, member_data in members_data.items():
                            assigned_count = len(member_data['assigned'])
                            if assigned_count > 0:
                                _logger.info(f"Cron: -> {member.user_id.name}: +{assigned_count} leads")
                    else:
                        _logger.info(f"Cron: No leads assigned to team '{team.name}' (members may be at capacity)")
                else:
                    _logger.debug(f"Cron: Team '{team.name}' has no unassigned leads")

            except Exception as e:
                _logger.error(f"Cron: Error processing team '{team.name}': {str(e)}")
                continue

        _logger.info(f"Cron: Even distribution assignment completed. Total leads assigned: {total_assigned}")
        return True
