# -*- coding: utf-8 -*-
from odoo import models, api
import logging

_logger = logging.getLogger(__name__)


class CrmTeamMember(models.Model):
    _inherit = 'crm.team.member'

    def _assign_and_convert_leads_even_distribution(self, work_days=1):
        """
        Enhanced version of _assign_and_convert_leads that ensures more even distribution
        of leads among team members using round-robin with capacity balancing.
        
        This method replaces the random weighted choice with a deterministic round-robin
        approach that considers current workload and capacity.
        
        :param float work_days: see ``CrmTeam.action_assign_leads()``;
        :return members_data: dict() with each member assignment result
        """
        if work_days < 0.2 or work_days > 30:
            raise ValueError(
                _('Leads team allocation should be done for at least 0.2 or maximum 30 work days, not %.2f.', work_days)
            )

        members = self.filtered(lambda member: not member.assignment_optout and member.assignment_max > 0)
        if not members:
            return {}

        # Prepare member data with current workload and capacity
        members_data = {}
        member_list = []
        
        for member in members:
            lead_domain = [
                '&', '&', ('user_id', '=', False), 
                ('date_open', '=', False), 
                ('team_id', '=', member.crm_team_id.id)
            ]
            
            # Add member-specific domain if exists
            if member.assignment_domain:
                from odoo.tools.safe_eval import literal_eval
                from odoo.osv import expression
                member_domain = literal_eval(member.assignment_domain)
                lead_domain = expression.AND([member_domain, lead_domain])

            leads = self.env["crm.lead"].search(lead_domain, order='probability DESC, id')
            to_assign = member._get_assignment_quota(work_days=work_days)
            
            member_info = {
                "team_member": member,
                "max": member.assignment_max,
                "to_assign": to_assign,
                "current_count": member.lead_month_count,
                "leads": leads,
                "assigned": self.env["crm.lead"],
                "assigned_count": 0
            }
            
            members_data[member.id] = member_info
            if to_assign > 0:  # Only include members who can receive assignments
                member_list.append(member_info)

        if not member_list:
            _logger.info('No members available for lead assignment')
            return {}

        # Sort members by current workload (ascending) for fair distribution
        member_list.sort(key=lambda x: (x['current_count'], x['team_member'].id))
        
        # Collect all available leads
        all_leads = set()
        for member_info in member_list:
            all_leads.update(member_info['leads'].ids)
        
        all_leads = list(all_leads)
        if not all_leads:
            _logger.info('No leads available for assignment')
            return {}

        # Round-robin assignment with capacity awareness
        member_index = 0
        leads_assigned = set()
        
        _logger.info(f'Starting even distribution assignment for {len(all_leads)} leads among {len(member_list)} members')
        
        for lead_id in all_leads:
            if lead_id in leads_assigned:
                continue
                
            assigned = False
            attempts = 0
            
            # Try to assign to next available member
            while not assigned and attempts < len(member_list):
                member_info = member_list[member_index]
                
                # Check if this lead is available for this member and member has capacity
                if (lead_id in member_info['leads'].ids and 
                    member_info['assigned_count'] < member_info['to_assign']):
                    
                    # Assign the lead
                    lead = self.env['crm.lead'].browse(lead_id)
                    members_data[member_info['team_member'].id]["assigned"] += lead
                    member_info['assigned_count'] += 1
                    leads_assigned.add(lead_id)
                    assigned = True
                    
                    # Convert to opportunity and assign user
                    lead.with_context(mail_auto_subscribe_no_notify=True).convert_opportunity(
                        lead.partner_id,
                        user_ids=member_info['team_member'].user_id.ids
                    )
                    
                    _logger.info(f"Assigned lead {lead_id} to member {member_info['team_member'].user_id.name} "
                               f"({member_info['assigned_count']}/{member_info['to_assign']})")
                
                # Move to next member
                member_index = (member_index + 1) % len(member_list)
                attempts += 1
            
            if not assigned:
                # If no member can take the lead due to capacity, assign to member with lowest total
                available_members = [m for m in member_list if lead_id in m['leads'].ids]
                if available_members:
                    # Sort by total workload (current + assigned in this batch)
                    available_members.sort(key=lambda x: x['current_count'] + x['assigned_count'])
                    fallback_member = available_members[0]
                    
                    lead = self.env['crm.lead'].browse(lead_id)
                    members_data[fallback_member['team_member'].id]["assigned"] += lead
                    fallback_member['assigned_count'] += 1
                    leads_assigned.add(lead_id)
                    
                    # Convert to opportunity and assign user
                    lead.with_context(mail_auto_subscribe_no_notify=True).convert_opportunity(
                        lead.partner_id,
                        user_ids=fallback_member['team_member'].user_id.ids
                    )
                    
                    _logger.warning(f"Overflow assignment: lead {lead_id} to member "
                                  f"{fallback_member['team_member'].user_id.name} "
                                  f"(exceeds quota: {fallback_member['assigned_count']}/{fallback_member['to_assign']})")

        # Log final distribution summary
        _logger.info('Even distribution assignment completed:')
        result_data = {}
        for member_id, member_info in members_data.items():
            if member_info["assigned"]:
                member = member_info["team_member"]
                assigned_count = len(member_info["assigned"])
                total_count = member_info['current_count'] + assigned_count
                _logger.info(f'-> Member {member.user_id.name}: assigned {assigned_count} leads '
                           f'(total: {total_count}/{member_info["max"]})')
                result_data[member] = {"assigned": member_info["assigned"]}

        return result_data
