# -*- coding: utf-8 -*-
from odoo import models, fields, api,_, tools
from odoo.exceptions import ValidationError

import logging

PARTNER_ADDRESS_FIELDS_TO_SYNC = [
    'street',
    'street2',
    'city',
    'zip',
    'state_id',
    'country_id',
    'district_id',
    'ward_id',
]

_logger = logging.getLogger(__name__)
class CrmLead(models.Model):

    _inherit = ['crm.lead']

    def _default_currency_id(self):
        return self.env.user.company_id.currency_id

    tax_code = fields.Char(tracking=True)
    company_type = fields.Selection(string='Company Type',
                                    selection=[('person', 'Individual'), ('company', 'Company')], default="company")

    btaskee_service_id = fields.Many2one('btaskee.service', string="Service")
    date_booking = fields.Datetime()
    service_type = fields.Selection(related='btaskee_service_id.service_type', tracking=True)
    service_line_ids = fields.One2many('btaskee.service.line', 'crm_lead_id', string='Service Details')
    district_id = fields.Many2one(
        "res.country.district", string='District',
        compute='_compute_partner_address_values', readonly=False, store=True,
        domain="[('state_id','=', state_id)]", tracking=True)

    ward_id = fields.Many2one(comodel_name='res.country.ward', string='Ward', change_default=True, compute='_compute_partner_address_values', readonly=False, store=True
                              ,domain="[('district_id', '=', district_id)]", tracking=True)

    lead_email = fields.Char(string='Email')
    lead_phone = fields.Char(string='Phone')
    account_email = fields.Char(string='Account Email')
    account_phone = fields.Char(string='Account Phone')

    lead_customer_name = fields.Char(string='Customer Name')

    sub_total_price = fields.Monetary(compute="compute_sub_total_price", store=True, currency_field='company_currency',
                          )
    vat = fields.Monetary(compute="compute_vat", store=True, currency_field='company_currency',
                                  string='Vat')
    total_price = fields.Monetary(compute="compute_total_price", store=True,currency_field='company_currency', string='Total Price')
    po_number = fields.Char(tracking=True)
    category_id = fields.Many2one('btaskee.service.category',string='Service Category')
    payment_date = fields.Date()
    contract_signed_date = fields.Date()
    iso_code = fields.Char()
    company_address_text = fields.Char()
    vn_business_status = fields.Char(help='integration status')
    warning_message = fields.Char(compute='_compute_warning_message')

    @api.depends('partner_id')
    def _compute_warning_message(self):
        for rec in self:
            warning_message = False
            if rec.partner_id.user_id != rec.user_id and not rec.partner_id:
                warning_message = 'This lead is assigned to another user. Please contact the manager to assign the lead to you.'
            rec.warning_message = warning_message

    @api.onchange('partner_id')
    def onchange_partner_id(self):
        if self.partner_id:
            self.account_email = self.partner_id.email
            self.account_phone = self.partner_id.phone

    @api.onchange('account_email')
    def onchange_account_email(self):
        if not self.partner_id:
            self.email_from = self.account_email

    @api.onchange('account_phone')
    def onchange_account_phone(self):
        if not self.partner_id:
            self.phone = self.account_phone


    def _get_lead_duplicates(self, partner=None, email=None, include_lost=False):
        # return {}
        return self
        """ Search for leads that seem duplicated based on partner / email.

        :param partner : optional customer when searching duplicated
        :param email: email (possibly formatted) to search
        :param boolean include_lost: if True, search includes archived opportunities
          (still only active leads are considered). If False, search for active
          and not won leads and opportunities;
        """
        if not email and not partner:
            return self.env['crm.lead']

        domain = []
        for normalized_email in [tools.email_normalize(email) for email in tools.email_split(email)]:
            domain.append(('email_normalized', '=', normalized_email))
        if partner:
            domain.append(('partner_id', '=', partner.id))

        if not domain:
            return self.env['crm.lead']

        domain = ['|'] * (len(domain) - 1) + domain
        if include_lost:
            domain += ['|', ('type', '=', 'opportunity'), ('active', '=', True)]
        else:
            domain += ['&', ('active', '=', True), '|', ('stage_id', '=', False), ('stage_id.is_won', '=', False)]

        return self.with_context(active_test=False).search(domain)



    def _prepare_address_values_from_partner(self, partner):
        # Sync all address fields from partner, or none, to avoid mixing them.
        if any(partner[f] for f in PARTNER_ADDRESS_FIELDS_TO_SYNC):
            values = {f: partner[f] for f in PARTNER_ADDRESS_FIELDS_TO_SYNC}
        else:
            values = {f: self[f] for f in PARTNER_ADDRESS_FIELDS_TO_SYNC}
        return values

    # Customer type = company
    def _create_customer(self):
        if self.company_type == 'company':
            partner_id = self.env['res.partner'].create({
                'name': self.partner_name,
                'company_type': 'company',
                'vat': self.tax_code,
                'phone': self.phone,
                'zip': self.zip,
                'email': self.email_from,
                'country_id': self.country_id.id,
                'state_id': self.state_id.id,
                'district_id': self.district_id.id,
                'ward_id': self.ward_id.id,
                'street': self.street,
                'user_id': self.user_id.id,

            })
            return partner_id

    def _create_individual_contact(self, company_partner):
        """Create an individual contact that belongs to the company partner"""
        if not self.lead_customer_name:
            return None

        # Check if individual contact already exists
        existing_contact = self.env['res.partner'].search([
            ('phone', '=', self.lead_phone),
            ('parent_id', '=', company_partner.id),
            ('company_type', '=', 'person')
        ], limit=1)

        if existing_contact:
            # Create portal user for existing contact if not already exists
            self._create_portal_user_for_contact(existing_contact)
            # Update admin company status for all individual contacts of this company
            self._update_admin_company_status(company_partner)
            return existing_contact

        # Create new individual contact
        individual_contact = self.env['res.partner'].create({
            'name': self.lead_customer_name,
            'company_type': 'person',
            'parent_id': company_partner.id,
            'email': self.lead_email or False,
            'phone': self.lead_phone or False,
            'is_company': False,
            'type':'contact',
            # Inherit address from company
            'street': company_partner.street,
            'street2': company_partner.street2,
            'city': company_partner.city,
            'state_id': company_partner.state_id.id if company_partner.state_id else False,
            'country_id': company_partner.country_id.id if company_partner.country_id else False,
            'district_id': company_partner.district_id.id if company_partner.district_id else False,
            'ward_id': company_partner.ward_id.id if company_partner.ward_id else False,
            'zip': company_partner.zip,
        })

        # Create portal user for the new individual contact
        self._create_portal_user_for_contact(individual_contact)

        # Update admin company status for all individual contacts of this company
        self._update_admin_company_status(company_partner)

        return individual_contact

    def _create_portal_user_for_contact(self, contact):
        """Create a portal user for the individual contact"""
        if not contact or not contact.email:
            return None

        # Check if user already exists for this contact
        existing_user = self.env['res.users'].search([
            ('partner_id', '=', contact.id)
        ], limit=1)

        if existing_user:
            return existing_user

        # Check if user with same email already exists
        existing_user_email = self.env['res.users'].search([
            ('login', '=', contact.email)
        ], limit=1)
        if existing_user_email:
            return existing_user_email

        # Get portal group
        portal_group = self.env.ref('base.group_portal', raise_if_not_found=False)
        if not portal_group:
            _logger.warning("Portal group not found, cannot create portal user")
            return None

        try:
            # Create new portal user
            portal_user = self.env['res.users'].create({
                'name': contact.name,
                'login': contact.email,
                'email': contact.email,
                'partner_id': contact.id,
                'groups_id': [(6, 0, [portal_group.id])],
                'active': True,
            })

            _logger.info(f"Created portal user for contact {contact.name} with email {contact.email}")
            return portal_user

        except Exception as e:
            _logger.error(f"Failed to create portal user for contact {contact.name}: {str(e)}")
            return None

    def _update_admin_company_status(self, company_partner):
        """Update admin company status for all individual contacts of the company"""
        if not company_partner:
            return

        # Get all individual contacts for this company
        individual_contacts = self.env['res.partner'].search([
            ('parent_id', '=', company_partner.id),
            ('company_type', '=', 'person'),
             ('type', '=', 'contact'),
            ('active', '=', True)
        ])

        # Determine admin status based on number of contacts
        if len(individual_contacts) == 1:
            # Only one contact - set as admin
            admin_contact = individual_contacts[0]
            admin_contact.is_admin_company = True



    def _validate_required_fields_for_company_contact(self):
        """Validate required fields before creating company contact"""
        missing_fields = []

        # Check company required fields
        if not self.partner_name:
            missing_fields.append('Company Name (Partner Name)')

        if not self.account_email:
            missing_fields.append('Account Email')

        if not self.tax_code:
            missing_fields.append('Tax Code')

        if not self.country_id:
            missing_fields.append('Country')

        # Check individual contact required fields
        if not self.lead_customer_name:
            missing_fields.append('Customer Name')

        if not self.lead_email:
            missing_fields.append('Customer Email')

        if not self.lead_phone:
            missing_fields.append('Customer Phone')

        # If any fields are missing, raise validation error
        if missing_fields:
            missing_fields_str = ', '.join(missing_fields)
            raise ValidationError(
                f"Please fill in the following required fields before creating company contact:\n\n"
                f"Missing fields: {missing_fields_str}\n\n"
                f"Company Information:\n"
                f"• Company Name (Partner Name)\n"
                f"• Account Email\n"
                f"• Tax Code\n"
                f"• Country\n\n"
                f"Individual Contact Information:\n"
                f"• Customer Name\n"
                f"• Customer Email\n"
                f"• Customer Phone"
            )

    # for B2B process
    def check_duplicate_contact_company(self):
        contact = False
        # Tìm theo Email
        email = self.account_email
        if email and '@' in email:
            domain = email.strip().split('@')[1].lower()
            # Tìm tất cả email trong hệ thống có domain trùng
            partners_with_same_domain = self.env['res.partner'].search([('company_type','=','company'),('email', 'ilike', f'%@{domain}')])
            if partners_with_same_domain:
                return  partners_with_same_domain
        if self.tax_code:
            partners_with_same_tax_code = self.env['res.partner'].search([('company_type','=','company'),('vat', '=', self.tax_code)])
            if partners_with_same_tax_code:
                return  partners_with_same_tax_code
        if self.phone:
            partners_with_same_phone = self.env['res.partner'].search([('company_type','=','company'),('phone', '=', self.phone)])
            if partners_with_same_phone:
                return  partners_with_same_phone
        # Tìm theo tên công ty
        if self.partner_name:
            partners_with_same_partner_name = self.env['res.partner'].search(
                [('company_type', '=', 'company'), ('name', '=', self.partner_name)])
            if partners_with_same_partner_name:
                return partners_with_same_partner_name
        return  contact


        
    @api.model
    def create(self, vals_list):
        if 'stage_id' not in vals_list:
            vals_list['stage_id'] = self.env.ref('crm.stage_lead1').id
        res = super().create(vals_list)
        exist_contact = res.check_duplicate_contact_company()
        if exist_contact:
            val_update = {'partner_id': exist_contact[0].id,
                          'tax_code': exist_contact[0].vat}
            if exist_contact[0].user_id:
                val_update['user_id'] = exist_contact.user_id.id
            res.write(val_update)
        res._compute_date_open()
        return res

    def validate_lead(self):
        contact = self.check_duplicate_contact_company()
        if contact:
            vals = {'partner_id': contact[0].id,
                    'account_email': contact[0].email,
                    'account_phone': contact[0].phone}
            if contact[0].user_id:
                vals['user_id'] = contact[0].user_id.id
            self.write(vals)


    def create_company_contact(self):
        # Validate required fields before creating company contact
        self._validate_required_fields_for_company_contact()

        exist_contact = self.check_duplicate_contact_company()
        if exist_contact:
            self.partner_id = exist_contact[0]
            # Create individual contact for existing company
            self._create_individual_contact(exist_contact[0])
        else:
            contact = self._create_customer()
            self.partner_id = contact.id
            contact.user_id = self.user_id.id
            # Create individual contact for new company
            self._create_individual_contact(contact)



    @api.depends('service_line_ids')
    def compute_sub_total_price(self):
        for rec in self:
            sub_total_price = 0
            for line in rec.service_line_ids:
                sub_total_price += line.total_price
            rec.sub_total_price = sub_total_price

    @api.depends('sub_total_price')
    def compute_vat(self):
        for rec in self:
            rec.vat = rec.sub_total_price * 0.1

    @api.depends('sub_total_price')
    def compute_total_price(self):
        for rec in self:
            rec.total_price = rec.sub_total_price + rec.vat
            rec.expected_revenue = rec.total_price



    def action_print_service_order(self):
        """Print Btaskee Service Order (based on bBenefit_PO01.docx template)"""
        if not self.po_number:
            raise ValidationError("Missing PO Number")
        return self.env.ref('btaskee_crm.action_report_btaskee_service_order').report_action(self)



    def action_quotation_send(self):
        """ Opens a wizard to compose an email, with relevant mail template loaded by default """
        self.ensure_one()
        #self.order_line._validate_analytic_distribution()
        #lang = self.env.context.get('lang')
        #mail_template = self._find_mail_template()
        mail_template = self.env.ref('btaskee_crm.email_template_crm_quotation')
        # if mail_template and mail_template.lang:
        #     lang = mail_template._render_lang(self.ids)[self.id]
        ctx = {
            'default_model': 'crm.lead',
            'default_res_ids': self.ids,
            'default_template_id': mail_template.id if mail_template else None,
            'default_composition_mode': 'comment',
            'mark_so_as_sent': True,
            'default_email_layout_xmlid': 'mail.mail_notification_layout_with_responsible_signature',
            'proforma': self.env.context.get('proforma', False),
            'force_email': True,
            #'model_description': self.with_context(lang=lang).type_name,
        }
        return {
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'mail.compose.message',
            'views': [(False, 'form')],
            'view_id': False,
            'target': 'new',
            'context': ctx,
        }

    @api.returns('mail.message', lambda value: value.id)
    def message_post(self, **kwargs):
        kwargs['email_from'] = self.env.user.login
        kwargs['reply_to'] = self.env.user.login
        return super(CrmLead, self).message_post(**kwargs)

    @api.model
    def _cron_validate_leads(self):
        """Cron method to validate leads every 5 minutes"""
        # Find leads that need validation (you can adjust the domain as needed)
        stage_check = [self.env.ref('crm.stage_lead1').id,self.env.ref('crm.stage_lead2').id]
        leads_to_validate = self.search([
            ('partner_id', '=', False),  # Leads without partner assigned
            ('stage_id', 'in', stage_check),   # Leads that have a stage
        ])

        _logger.info(f"Cron job: Found {len(leads_to_validate)} leads to validate")

        for lead in leads_to_validate:
            try:
                lead.validate_lead()
                _logger.info(f"Successfully validated lead {lead.id}: {lead.name}")
            except Exception as e:
                _logger.error(f"Error validating lead {lead.id}: {str(e)}")

        return True
