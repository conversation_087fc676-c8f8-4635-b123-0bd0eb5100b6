# -*- coding: utf-8 -*-
from odoo import models, fields, api,_, tools
import json
import requests
from odoo.exceptions import ValidationError, UserError
from datetime import datetime
import logging


_logger = logging.getLogger(__name__)
class ResPartner(models.Model):
    _inherit = 'res.partner'
    signup_token = fields.Char(groups="base.group_erp_manager,btaskee_crm.group_create_user")
    signup_type = fields.Char(groups="base.group_erp_manager,btaskee_crm.group_create_user")
    signup_expiration = fields.Datetime(groups="base.group_erp_manager,btaskee_crm.group_create_user")

    customer_code = fields.Char(string='Customer Code', readonly=True, copy=False, index=True)
    is_admin_company = fields.Boolean(string='Is Admin Company', default=False)
    position = fields.Char()
    @api.model
    def create(self, vals_list):
        """ Create a sequence for the Company customer  """
        res =  super().create(vals_list)
        if res.company_type =='company' or res.is_company:
            res.customer_code = self.env['ir.sequence'].next_by_code('b.company.code')
        return res