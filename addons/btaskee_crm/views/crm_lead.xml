<?xml version="1.0" encoding="utf-8"?>
<odoo>

	<record id="action_account_form" model="ir.actions.act_window">
            <field name="name">Accounts</field>
            <field name="res_model">res.partner</field>
            <field name="view_mode">kanban,tree,form</field>
            <field name="context">{'res_partner_search_mode': 'customer','search_default_type_company':1}</field>
            <field name="search_view_id" ref="base.view_res_partner_filter"/>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a Contact in your address book
              </p><p>
                Odoo helps you track all activities related to your contacts.
              </p>
            </field>
	</record>

    <menuitem
        id="crm.res_partner_menu_customer"
        name="Accounts"
        parent="crm.crm_menu_sales"
        action="action_account_form"
        sequence="5"/>

	<record id="crm_lead_view_filter_inherit" model="ir.ui.view">
		<field name="name">crm.lead.view.filter.inherit.hr</field>
		<field name="model">crm.lead</field>
		<field name="inherit_id" ref="crm.view_crm_case_opportunities_filter" />
		<field name="arch" type="xml">
			<xpath expr="//field[@name='name']" position="before">
				<field name="partner_name" string="Account Name"></field>
				<field name="account_email"></field>
				<field name="account_phone"></field>
				<field name="lead_email" string="Person email"></field>
				<field name="lead_phone" string="Person Phone"></field>
				<field name="tax_code"></field>
			</xpath>
			<xpath expr="//field[@name='name']" position="after">
				<searchpanel view_types="kanban,list,graph,pivot,hierarchy">
					<field name="stage_id" icon="fa-building"
						enable_counters="1" />
					<!--      <field name="user_id" icon="fa-users"
					enable_counters="1"/>-->
				</searchpanel>
			</xpath>
			<xpath expr="//filter[@name='salesperson']" position="after">
				<filter string="Account" name="customer_id" context="{'group_by':'partner_id'}"/>
			</xpath>
		</field>
	</record>

	<record id="crm_lead_view_tree_inherit" model="ir.ui.view">
		<field name="name">crm.lead.view.tree.inherit.hr</field>
		<field name="model">crm.lead</field>
		<field name="inherit_id" ref="crm.crm_case_tree_view_oppor" />
		<field name="arch" type="xml">
			<xpath expr="//field[@name='name']" position="after">
				<field name="category_id" />
			</xpath>
			<xpath expr="//field[@name='date_deadline']" position="after">
				<field name="contract_signed_date" optional="hide"/>
				<field name="payment_date" optional="hide"/>
			</xpath>

		</field>
	</record>

	<record id="crm_lead_view_form_inherit" model="ir.ui.view">
		<field name="name">crm.lead.view.form.inherit.hr</field>
		<field name="model">crm.lead</field>
		<field name="inherit_id" ref="crm.crm_lead_view_form" />
		<field name="arch" type="xml">
			<xpath expr="//field[@name='stage_id']" position="replace">
				<field force_save="1" name="stage_id"
					widget="statusbar_duration" options="{'clickable': '1'}"
					domain="['|', ('team_id', '=', team_id), ('team_id', '=', False)]"
					invisible="not active or type == 'lead'" />
			</xpath>
			<xpath expr="//header" position="inside">

				<button name="create_company_contact"
						type="object"
						string="Create Account"
						class="oe_highlight"
						icon="fa-address-card"
						invisible="partner_id != False or service_line_ids == [] or stage_id not in [%(crm.stage_lead2)d]"
						/>

				<button name="action_print_service_order"
						type="object"
						string="Print Quotation"
						class="btn-secondary"
						icon="fa-print"
						invisible="service_line_ids == [] or stage_id in [%(crm.stage_lead1)d,%(crm.stage_lead2)d]"
						/>
				<button name="action_quotation_send"
						type="object"
						string="Send Quotation"
						class="btn-secondary"
						invisible="service_line_ids == [] or stage_id in [%(crm.stage_lead1)d,%(crm.stage_lead2)d] or po_number == False"
						/>
				<button name="validate_lead"
						type="object"
						string="Validate"
						class="btn-secondary"
						invisible="1"
						/>

			</xpath>

			<xpath expr="//field[@name='name']" position="after">
				<field name="company_type" widget="radio"
					options="{'horizontal': true}" style="font-size:15px" />
			</xpath>
			<xpath expr="//field[@name='user_id']" position="after">
<!--				<label for="contract_signed_date">Contract Signed Date</label>-->
				<field name="contract_signed_date"></field>
<!--				<label for="payment_date">Payment Date</label>-->
				<field name="payment_date"></field>

			</xpath>

			<xpath expr="//group[@name='opportunity_partner']//field[@name='email_from']" position="attributes">
					<attribute name="invisible">1</attribute>
			</xpath>

			<xpath expr="//group[@name='opportunity_partner']//field[@name='phone']" position="attributes">
					<attribute name="invisible">1</attribute>
			</xpath>


			<xpath expr="//group[@name='opportunity_partner']/field[@name='partner_id']" position="after">
				<field name="lead_customer_name"></field>
				<field name="lead_email"></field>
				<field name="lead_phone"></field>
				<field name="category_id"></field>
				<field name="po_number"></field>
				<field name="company_id" invisible="1"></field>
			</xpath>
			<xpath
				expr="//group[@name='opportunity_partner']/field[@name='partner_id']"
				position="attributes">
				<attribute name="string">Account</attribute>
<!--				<attribute name="readonly">company_type == 'company'</attribute>-->
			</xpath>

			
			<xpath expr="//field[@name='user_id']" position="before">
				<field name='team_id'></field>
			</xpath>
			
			<xpath expr="//group[@name='opportunity_partner']" position="after">
				<group>
					<field name="partner_name"
						required="stage_id not in [%(crm.stage_lead2)d,%(crm.stage_lead1)d]" />
					<field name="account_email" readonly="0" ></field>
					<field name="account_phone" readonly="0" ></field>
					<field name="tax_code"
						required="stage_id not in [%(crm.stage_lead2)d,%(crm.stage_lead1)d]" />
					<field name="company_address_text" ></field>

					<label for="street" string="Address" />
					<div class="o_address_format">
						<field name="mobile_blacklisted" invisible="1"></field>
						<field name="country_id" placeholder="Country..."
							class="o_address_street"
							required="stage_id not in [%(crm.stage_lead2)d,%(crm.stage_lead1)d]" />
						<field name="state_id" placeholder="State..."
							class="o_address_street"
							required="stage_id not in [%(crm.stage_lead2)d,%(crm.stage_lead1)d]" />
						<field name="district_id" placeholder="District.."
							class="o_address_city"
							required="stage_id not in [%(crm.stage_lead2)d,%(crm.stage_lead1)d]" />
						<field name="ward_id" class="o_address_state"
							placeholder="Ward..."
							options="{&quot;no_open&quot;: True}"
							required="stage_id not in [%(crm.stage_lead2)d,%(crm.stage_lead1)d]" />
						<field name="street" placeholder="Street..."
							class="o_address_street"
							required="stage_id not in [%(crm.stage_lead2)d,%(crm.stage_lead1)d]" />
					</div>
					<field name="zip" string="Zip Code" placeholder="Zip Code"
						class="o_address_zip" />
				</group>
			</xpath>

			<xpath expr="//notebook" position="before">

				<!--for service type = air_conditioner-->
				<separator string="Service Details" ></separator>
				<field name="service_line_ids" >
					<tree string=""  default_order='date_booking' limit="10">
						<field name="btaskee_service_id" />
						<field name="crm_lead_id" column_invisible="True"/>
						<field name="address_id"/>
						<field name="date_booking" optional="hide"/>
						<field name="quantity"/>
						<field name="unit"></field>
						<field name="total_price" widget="monetary" />
						<field name="currency_id" column_invisible="True"/>
						<field name="note"/>
            		</tree>

				</field>
				<group col="12" class="oe_invoice_lines_tab">
					<group colspan="8">
<!--						<field name="promotion" placeholder="promotion....."-->
<!--							style='width:150px' />-->
					</group>
					<!-- Totals (only invoices / receipts) -->
					<group colspan="4">
						<group class="oe_subtotal_footer">

							<field name="sub_total_price"
								/>
							<field name="vat"
								/>
							<field name="total_price"
								/>
						</group>

					</group>
				</group>

			</xpath>


		</field>


	</record>
        <record model="ir.actions.act_window" id="crm.crm_lead_action_pipeline">
            <field name="name">Pipeline</field>
            <field name="res_model">crm.lead</field>
            <field name="view_mode">tree,kanban,graph,pivot,form,calendar,activity</field>
            <field name="domain">[('type','=','opportunity')]</field>
            <field name="context">{
                    'default_type': 'opportunity',
					'default_stage_id': 1,
                    'search_default_customer_id': 1
            }</field>
            <field name="search_view_id" ref="crm.view_crm_case_opportunities_filter"/>
        </record>

		<record id="crm.crm_case_tree_view_oppor" model="ir.ui.view">
            <field name="priority" eval="1"/>
		</record>

        <record id="crm.crm_case_kanban_view_leads" model="ir.ui.view">
            <field name="priority" eval="2"/>
		</record>
</odoo>