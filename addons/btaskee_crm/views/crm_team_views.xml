<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced CRM Team Form View with Even Distribution Assignment -->
    <record id="crm_team_view_form_enhanced" model="ir.ui.view">
        <field name="name">crm.team.form.enhanced</field>
        <field name="model">crm.team</field>
        <field name="inherit_id" ref="crm.sales_team_form_view_in_crm"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_assign_leads']" position="after">
                <button name="action_assign_leads_even_distribution" 
                        type="object" 
                        string="Assign Leads (Even Distribution)" 
                        class="btn-primary"
                        help="Assign leads using round-robin for more even distribution among team members"/>
<!--                <button name="action_assign_leads_comparison" -->
<!--                        type="object" -->
<!--                        string="Test Assignment Distribution" -->
<!--                        class="btn-secondary"-->
<!--                        help="Compare original vs even distribution assignment methods"/>-->
            </xpath>

            <xpath expr="//button[@name='action_assign_leads']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <!-- Action for Even Distribution Lead Assignment -->
    <record id="action_assign_leads_even_distribution" model="ir.actions.server">
        <field name="name">Assign Leads (Even Distribution)</field>
        <field name="model_id" ref="crm.model_crm_team"/>
        <field name="binding_model_id" ref="crm.model_crm_team"/>
        <field name="state">code</field>
        <field name="code">
if records:
    action = records.action_assign_leads_even_distribution()
        </field>
    </record>

    <!-- Action for Assignment Comparison -->
    <record id="action_assign_leads_comparison" model="ir.actions.server">
        <field name="name">Test Assignment Distribution</field>
        <field name="model_id" ref="crm.model_crm_team"/>
        <field name="binding_model_id" ref="crm.model_crm_team"/>
        <field name="state">code</field>
        <field name="code">
if records:
    action = records.action_assign_leads_comparison()
        </field>
    </record>
</odoo>
