<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- User Creation Wizard Form View -->
        <record id="user_creation_wizard_form" model="ir.ui.view">
            <field name="name">user.creation.wizard.form</field>
            <field name="model">user.creation.wizard</field>
            <field name="arch" type="xml">
                <form string="Create New User">
                    <sheet>
                        <div class="oe_title">
                            <h1>Create New Internal User</h1>
                            <p class="text-muted">
                                Create a new internal user with basic permissions (User: Own Documents Only).
                            </p>
                        </div>
                        <group>
                            <group>
                                <field name="name" placeholder="e.g. <PERSON>"/>
                                <field name="login" placeholder="e.g. john.smith"/>
                                <field name="email" placeholder="e.g. <EMAIL>"/>
                            </group>
                            <group>
                                <field name="phone" placeholder=""/>
                                <field name="company_id" options="{'no_create': True, 'no_open': True}"/>
                            </group>
                        </group>
                    </sheet>
                    <footer>
                        <button name="create_user" string="Create User" type="object" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- User Creation Wizard Action -->
        <record id="action_user_creation_wizard" model="ir.actions.act_window">
            <field name="name">Create New User</field>
            <field name="res_model">user.creation.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="user_creation_wizard_form"/>
        </record>

        <!-- Menu Item for User Creation -->
        <record id="menu_user_creation" model="ir.ui.menu">
            <field name="name">Create User</field>
            <field name="parent_id" ref="crm.crm_menu_config"/>
            <field name="action" ref="action_user_creation_wizard"/>
            <field name="groups_id" eval="[(4, ref('btaskee_crm.group_create_user'))]"/>
            <field name="sequence">10</field>
        </record>

        <!-- Add button to Users list view -->
        <record id="view_users_tree_create_user_button" model="ir.ui.view">
            <field name="name">res.users.tree.create.user.button</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//tree" position="inside">
                    <header>
                        <button name="%(action_user_creation_wizard)d" 
                                string="Create User" 
                                type="action" 
                                class="btn-primary"
                                groups="btaskee_crm.group_create_user"/>
                    </header>
                </xpath>
            </field>
        </record>

        <!-- Add button to Users form view -->
        <record id="view_users_form_create_user_button" model="ir.ui.view">
            <field name="name">res.users.form.create.user.button</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <button name="%(action_user_creation_wizard)d" 
                            string="Create New User" 
                            type="action" 
                            class="btn-secondary"
                            groups="btaskee_crm.group_create_user"/>
                </xpath>
            </field>
        </record>

        <!-- User Creation Success View -->
        <record id="user_creation_success_form" model="ir.ui.view">
            <field name="name">user.creation.success.form</field>
            <field name="model">res.users</field>
            <field name="arch" type="xml">
                <form string="User Created Successfully" create="false" edit="false">
                    <sheet>
                        <div>
                            <h4>User Created Successfully!</h4>
                            <p>The new user has been created with the following details:</p>
                        </div>
                        <group>
                            <group>
                                <field name="name" readonly="1"/>
                                <field name="login" readonly="1"/>
                                <field name="email" readonly="1"/>
                            </group>
                            <group>
                                <field name="phone" readonly="1"/>
                                <field name="company_id" readonly="1"/>
                                <field name="active" readonly="1"/>
                            </group>
                        </group>
                        <group string="Assigned Groups">
                            <field name="groups_id" readonly="1" nolabel="1">
                                <tree>
                                    <field name="name"/>
                                    <field name="category_id"/>
                                </tree>
                            </field>
                        </group>
                    </sheet>
                    <footer>
                        <button string="Close" class="btn-primary" special="cancel"/>
                        <button name="%(action_user_creation_wizard)d" 
                                string="Create Another User" 
                                type="action" 
                                class="btn-secondary"
                                groups="btaskee_crm.group_create_user"/>
                    </footer>
                </form>
            </field>
        </record>

    </data>
</odoo>
