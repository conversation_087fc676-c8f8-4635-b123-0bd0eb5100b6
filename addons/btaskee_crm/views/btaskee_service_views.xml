<?xml version="1.0" encoding="utf-8"?>
<odoo>
	<!-- ========================================== -->
	<!-- BTASKEE SERVICE VIEWS -->
	<!-- ========================================== -->

	<!-- Btaskee Service Tree View -->
	<record id="btaskee_service_tree_view" model="ir.ui.view">
		<field name="name">btaskee.service.tree</field>
		<field name="model">btaskee.service</field>
		<field name="arch" type="xml">
			<tree string="bTaskee Services" editable="bottom">
				<field name="name" />
				<field name="en_name" />

				<field name="category_id" />
				<field name="service_type" />
				<field name="api_call" />
				<field name="service_id"></field>
			</tree>
		</field>
	</record>

	<!-- Btaskee Service Form View -->
	<record id="btaskee_service_form_view" model="ir.ui.view">
		<field name="name">btaskee.service.form</field>
		<field name="model">btaskee.service</field>
		<field name="arch" type="xml">
			<form string="Btaskee Service">
				<sheet>
					<group>
						<group>
							<field name="service_type" />
						</group>
						<group>
							<field name="service_id"></field>
							<field name="api_call"
								placeholder="https://api.btaskee.com/..." />
						</group>
					</group>
				</sheet>
			</form>
		</field>
	</record>

	<!-- Btaskee Service Search View -->
	<record id="btaskee_service_search_view" model="ir.ui.view">
		<field name="name">btaskee.service.search</field>
		<field name="model">btaskee.service</field>
		<field name="arch" type="xml">
			<search string="Search bTaskee Services">
				<field name="service_type" />
				<field name="api_call" />
				<filter string="Air Conditioner" name="air_conditioner"
					domain="[('service_type', '=', 'Air Conditioner')]" />
				<filter string="Other Services" name="other"
					domain="[('service_type', '=', 'other')]" />
				<group expand="0" string="Group By">
					<filter string="Service Type" name="group_service_type"
						context="{'group_by': 'service_type'}" />
				</group>
			</search>
		</field>
	</record>

	<!-- Btaskee Service Action -->
	<record id="btaskee_service_action" model="ir.actions.act_window">
		<field name="name">bTaskee Services</field>
		<field name="res_model">btaskee.service</field>
		<field name="view_mode">tree,form</field>
		<field name="search_view_id" ref="btaskee_service_search_view" />
		<field name="help" type="html">
			<p class="o_view_nocontent_smiling_face">
				Create your first bTaskee Service!
			</p>
			<p>
				Configure bTaskee services with their API endpoints for
				integration.
			</p>
		</field>
	</record>

</odoo>
