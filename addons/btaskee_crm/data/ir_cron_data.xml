<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="ir_cron_validate_leads" model="ir.cron">
            <field name="name">bTaskee CRM: Validate Leads</field>
            <field name="model_id" ref="model_crm_lead"/>
            <field name="state">code</field>
            <field name="code">model._cron_validate_leads()</field>
            <field name="active" eval="True"/>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">5</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
        </record>

         <record id="ir_cron_action_assign_leads_even_distribution" model="ir.cron">
            <field name="name">CRM: Lead Assignment</field>
            <field name="model_id" ref="model_crm_team"/>
            <field name="state">code</field>
            <field name="code">model._cron_validate_leads()</field>
            <field name="active" eval="True"/>
            <field name="user_id" ref="base.user_root"/>
            <field name="interval_number">3</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
        </record>

    </data>
</odoo>



