# Even Distribution Lead Assignment

This enhancement provides a more fair and even distribution of leads among team members compared to the default random weighted assignment.

## Features

### 1. Even Distribution Assignment
- **Method**: `action_assign_leads_even_distribution()`
- **Algorithm**: Round-robin with capacity balancing
- **Benefits**: 
  - More predictable distribution
  - Reduces clustering of assignments
  - Better workload balance among team members

### 2. Assignment Comparison
- **Method**: `action_assign_leads_comparison()`
- **Purpose**: Test and compare distribution methods
- **Output**: Detailed logs showing assignment results

## How It Works

### Original Method (Random Weighted)
```python
# Uses random.choices() with weights
member = random.choices(population, weights=weights, k=1)[0]
```
**Issues**: Can assign multiple consecutive leads to the same person due to randomness.

### Enhanced Method (Round-Robin)
```python
# Deterministic round-robin assignment
member_index = (member_index + 1) % len(member_list)
```
**Benefits**: Ensures fair rotation among available team members.

## Algorithm Details

1. **Sort members by current workload** (ascending order)
2. **Use round-robin assignment** starting with least loaded member
3. **Respect capacity limits** - skip members at max capacity
4. **Fallback handling** - if all members at capacity, assign to least loaded
5. **Real-time balancing** - considers assignments made in current batch

## Usage

### Via UI
1. Go to CRM > Sales Teams
2. Open a team record
3. Click "Assign Leads (Even Distribution)" button
4. Use "Test Assignment Distribution" to compare methods

### Via Code
```python
# Even distribution assignment
team = self.env['crm.team'].browse(team_id)
team.action_assign_leads_even_distribution(work_days=1)

# Comparison test
team.action_assign_leads_comparison(work_days=1)
```

## Configuration

### Team Member Settings
- **Assignment Max**: Maximum leads per member (30-day period)
- **Assignment Domain**: Additional filters for member-specific leads
- **Assignment Optout**: Skip member from auto-assignment

### Monitoring
Check Odoo logs for detailed assignment information:
```
INFO: Even distribution assigned 10 leads
INFO: Member John Doe: assigned 3 leads (total: 15/30)
INFO: Member Jane Smith: assigned 4 leads (total: 12/30)
INFO: Member Bob Wilson: assigned 3 leads (total: 18/30)
```

## Benefits Over Original Method

1. **Fairness**: More even distribution among team members
2. **Predictability**: Deterministic assignment order
3. **Transparency**: Detailed logging of assignment decisions
4. **Flexibility**: Can be used alongside original method
5. **Capacity Awareness**: Better handling of member capacity limits

## Example Scenario

**Team Setup:**
- Member A: 10/30 leads (20 remaining capacity)
- Member B: 15/30 leads (15 remaining capacity)  
- Member C: 5/30 leads (25 remaining capacity)

**Assignment Order:**
1. Member C (lowest current count)
2. Member A (next lowest)
3. Member B (highest current count)
4. Repeat cycle...

**Result**: More balanced final distribution compared to random assignment.
