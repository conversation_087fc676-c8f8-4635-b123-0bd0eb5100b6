# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_gq
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-30 10:24+0000\n"
"PO-Revision-Date: 2023-11-30 10:24+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_gq
#: model:ir.model,name:l10n_gq.model_account_chart_template
msgid "Account Chart Template"
msgstr "Plantilla de plan contable"

#. module: l10n_gq
#: model:account.report.column,name:l10n_gq.account_tax_report_gq_balance
msgid "Base"
msgstr ""

#. module: l10n_gq
#: model:account.report.line,name:l10n_gq.account_tax_report_line_gq_purchases_exempt
#: model:account.report.line,name:l10n_gq.account_tax_report_line_gq_sales_exempt
msgid "Exempt"
msgstr "Exentas"

#. module: l10n_gq
#: model:account.report.line,name:l10n_gq.account_tax_report_line_gq_sales_export
msgid "Export"
msgstr "Exportación"

#. module: l10n_gq
#: model:account.report.line,name:l10n_gq.account_tax_report_line_gq_purchases_import
msgid "Import"
msgstr "Importación"

#. module: l10n_gq
#: model:account.report.line,name:l10n_gq.account_tax_report_line_gq_purchases
msgid "Incoming"
msgstr "De entrada"

#. module: l10n_gq
#: model:account.report.line,name:l10n_gq.account_tax_report_line_gq_net
msgid "Net VAT"
msgstr "Neto IVA"

#. module: l10n_gq
#: model:account.report.line,name:l10n_gq.account_tax_report_line_gq_sales
msgid "Outgoing"
msgstr "De salida"

#. module: l10n_gq
#. odoo-python
#: code:addons/l10n_gq/models/template_gq_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr "SYSCEBNL para Asociaciones"

#. module: l10n_gq
#. odoo-python
#: code:addons/l10n_gq/models/template_gq.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr "SYSCEBNL para Empresas"

#. module: l10n_gq
#: model:account.report.column,name:l10n_gq.account_tax_report_gq_tax
msgid "Tax"
msgstr "Impuesto"

#. module: l10n_gq
#: model:account.report.line,name:l10n_gq.account_tax_report_line_gq_purchases_taxable
msgid "Taxable"
msgstr "Imponible"

#. module: l10n_gq
#: model:account.report.line,name:l10n_gq.account_tax_report_line_gq_sales_15
msgid "Taxable operations at 15%"
msgstr "Operaciones imponibles al 15%"

#. module: l10n_gq
#: model:account.report.line,name:l10n_gq.account_tax_report_line_gq_sales_30
msgid "Taxable operations at 30%"
msgstr "Operaciones imponibles al 30%"

#. module: l10n_gq
#: model:account.report.line,name:l10n_gq.account_tax_report_line_gq_sales_6
msgid "Taxable operations at 6%"
msgstr "Operaciones imponibles al 6%"

#. module: l10n_gq
#: model:account.report.line,name:l10n_gq.account_tax_report_line_gq_credit
msgid "VAT Credit"
msgstr "Credito IVA"

#. module: l10n_gq
#: model:account.report,name:l10n_gq.account_tax_report_gq
msgid "VAT Report"
msgstr "Informe fiscal"

#. module: l10n_gq
#: model:account.report.line,name:l10n_gq.account_tax_report_line_gq_to_pay
msgid "VAT to pay"
msgstr "IVA a pagar"
