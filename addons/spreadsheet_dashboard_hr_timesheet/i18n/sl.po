# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_hr_timesheet
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid " days"
msgstr " dni"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Assignee"
msgstr "Zadolžen"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Assignees"
msgstr "Udeleženci"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Current"
msgstr "Trenutno"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Customer"
msgstr "Stranka"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Days to assign"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Days to close"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Hours Logged"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Hours logged"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "KPI"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Obdobje"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Previous"
msgstr "Nazaj"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_hr_timesheet.spreadsheet_dashboard_tasks
#, python-format
msgid "Project"
msgstr "Projekt"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tag"
msgstr "Ključna beseda"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tags"
msgstr "Ključne besede"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks"
msgstr "Opravilo"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks Analysis by Assignees"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks Analysis by Customer"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks Analysis by Project"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks Analysis by Tags"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks by Stage"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Tasks by State"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Time to Assign"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Time to Close"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Top Assignees"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Top Customers"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Top Projects"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "Top Tags"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "last period"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "since last period"
msgstr "od zadnjega obdobja"

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "stats - current"
msgstr ""

#. module: spreadsheet_dashboard_hr_timesheet
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_timesheet/data/files/tasks_dashboard.json:0
#, python-format
msgid "stats - previous"
msgstr ""
