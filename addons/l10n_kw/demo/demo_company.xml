<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_kw" model="res.partner">
        <field name="name">KW Company</field>
        <field name="vat"/>
        <field name="street">117 Street 7, Block 4, Fintas, Kuwait</field>
        <field name="street2">Unit 07 - 10, 38/F Fintas</field>
        <field name="city">Fintas</field>
        <field name="country_id" ref="base.kw"/>
        <field name="zip"/>
        <field name="phone">+96523245875</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.kwexample.com</field>
    </record>

    <record id="demo_company_kw" model="res.company">
        <field name="name">KW Company</field>
        <field name="partner_id" ref="partner_demo_company_kw"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_kw')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_kw.demo_company_kw'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>kw</value>
        <value model="res.company" eval="obj().env.ref('l10n_kw.demo_company_kw')"/>
    </function>
</odoo>
