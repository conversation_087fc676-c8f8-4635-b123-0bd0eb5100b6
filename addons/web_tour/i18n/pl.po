# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_tour
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_service.js:0
#, python-format
msgid ""
"<strong><b>Good job!</b> You went through all steps of this tour.</strong>"
msgstr ""
"<strong><b>Dobra robota!</b> Przeszedłeś przez wszystkie etapy tej "
"wycieczki.</strong>"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_utils.js:0
#, python-format
msgid "Click on the <i>Home icon</i> to navigate across apps."
msgstr "Kliknij na <i>ikonę Domu</i>, aby nawigować pomiędzy aplikacjami."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Close"
msgstr "Zamknij"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__user_id
msgid "Consumed by"
msgstr "Zużyte przez"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/debug_manager.js:0
#, python-format
msgid "Disable Tours"
msgstr "Wyłącz wycieczki"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: web_tour
#: model:ir.model,name:web_tour.model_ir_http
msgid "HTTP Routing"
msgstr "Wytyczanie HTTP"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__id
msgid "ID"
msgstr "ID"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_list
msgid "Menu"
msgstr "Menu"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Name"
msgstr "Nazwa"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Onboarding tours"
msgstr "Przewodniki wdrożeniowe"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_utils.js:0
#, python-format
msgid "Open bugger menu."
msgstr "Otwórz menu błędów."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Path"
msgstr "Ścieżka"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
#, python-format
msgid "Scroll down to reach the next step."
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
#, python-format
msgid "Scroll up to reach the next step."
msgstr ""

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Sequence"
msgstr "Sekwencja"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Start"
msgstr "Uruchom"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/debug_manager.js:0
#, python-format
msgid "Start Tour"
msgstr "Rozpocznij wycieczkę"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Start tour"
msgstr "Rozpocznij wycieczkę"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Test"
msgstr "Test"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Test tour"
msgstr "Przetestuj wycieczkę"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.xml:0
#, python-format
msgid "Testing tours"
msgstr "Przetestuj przewodnik"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.edit_tour_search
msgid "Tip"
msgstr "Wskazówka"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__name
msgid "Tour name"
msgstr "Nazwa wycieczki"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/debug/tour_dialog_component.js:0
#: model:ir.actions.act_window,name:web_tour.edit_tour_action
#: model:ir.model,name:web_tour.model_web_tour_tour
#: model:ir.ui.menu,name:web_tour.menu_tour_action
#, python-format
msgid "Tours"
msgstr "Samouczki"
