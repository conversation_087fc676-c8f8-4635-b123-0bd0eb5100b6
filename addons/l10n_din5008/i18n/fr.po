# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_din5008
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~16.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-15 12:05+0000\n"
"PO-Revision-Date: 2024-02-14 09:13+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.4.2\n"

#. module: l10n_din5008
#: model_terms:ir.ui.view,arch_db:l10n_din5008.din5008_css
msgid ""
"&amp;.din_page {\n"
"                        &amp;.header {\n"
"                            .company_header {\n"
"                                .name_container {\n"
"                                    color:"
msgstr ""

#. module: l10n_din5008
#: model_terms:ir.ui.view,arch_db:l10n_din5008.din5008_css
msgid ""
";\n"
"                                    }\n"
"                                }\n"
"                            }\n"
"                            h2 {\n"
"                                color:"
msgstr ""

#. module: l10n_din5008
#: model_terms:ir.ui.view,arch_db:l10n_din5008.din5008_css
msgid ""
";\n"
"                                }\n"
"                            }\n"
"                        }\n"
"                        &amp;.invoice_note {\n"
"                            td {\n"
"                                .address {\n"
"                                    &gt; span {\n"
"                                        color:"
msgstr ""

#. module: l10n_din5008
#: model_terms:ir.ui.view,arch_db:l10n_din5008.din5008_css
msgid ""
";\n"
"                            }\n"
"                            .page {\n"
"                                [name=invoice_line_table], "
"[name=stock_move_table], .o_main_table {\n"
"                                    th {\n"
"                                        color:"
msgstr ""

#. module: l10n_din5008
#: model_terms:ir.ui.view,arch_db:l10n_din5008.external_layout_din5008
msgid "<span>|</span>"
msgstr ""

#. module: l10n_din5008
#: model:ir.model,name:l10n_din5008.model_account_analytic_line
msgid "Analytic Line"
msgstr "Ligne analytique"

#. module: l10n_din5008
#: model_terms:ir.ui.view,arch_db:l10n_din5008.external_layout_din5008
msgid "BIC:"
msgstr "BIC :"

#. module: l10n_din5008
#: model:ir.model.fields,field_description:l10n_din5008.field_base_document_layout__bank_ids
msgid "Banks"
msgstr "Banques"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#, python-format
msgid "Beneficiary:"
msgstr "Bénéficiaire :"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#, python-format
msgid "Cancelled Invoice"
msgstr "Facture annulée"

#. module: l10n_din5008
#: model:ir.model.fields,field_description:l10n_din5008.field_base_document_layout__city
msgid "City"
msgstr "Ville"

#. module: l10n_din5008
#: model:ir.model.fields,field_description:l10n_din5008.field_base_document_layout__company_details
msgid "Company Details"
msgstr "Détails de la société"

#. module: l10n_din5008
#: model:ir.model,name:l10n_din5008.model_base_document_layout
msgid "Company Document Layout"
msgstr "Mise en page des documents de votre société"

#. module: l10n_din5008
#: model:ir.model.fields,field_description:l10n_din5008.field_base_document_layout__company_registry
msgid "Company ID"
msgstr "ID de la société"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#, python-format
msgid "Credit Note"
msgstr "Note de crédit"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#: code:addons/l10n_din5008/models/base_document_layout.py:0
#, python-format
msgid "Delivery Date"
msgstr "Date de livraison"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#, python-format
msgid "Draft Invoice"
msgstr "Facture en brouillon"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#: code:addons/l10n_din5008/models/base_document_layout.py:0
#, python-format
msgid "Due Date"
msgstr "Date d'échéance"

#. module: l10n_din5008
#: model:ir.model.fields,field_description:l10n_din5008.field_base_document_layout__account_fiscal_country_id
msgid "Fiscal Country"
msgstr "Pays d'imposition"

#. module: l10n_din5008
#: model:ir.model.fields,help:l10n_din5008.field_base_document_layout__report_footer
msgid "Footer text displayed at the bottom of all reports."
msgstr "Pied de page affiché sur tous les rapports."

#. module: l10n_din5008
#: model_terms:ir.ui.view,arch_db:l10n_din5008.external_layout_din5008
msgid "HRB Nr:"
msgstr "N° HRB :"

#. module: l10n_din5008
#: model:ir.model.fields,help:l10n_din5008.field_base_document_layout__company_details
msgid "Header text displayed at the top of all reports."
msgstr "Texte d'en-tête affiché en haut de tous les rapports."

#. module: l10n_din5008
#: model_terms:ir.ui.view,arch_db:l10n_din5008.external_layout_din5008
msgid "IBAN:"
msgstr "IBAN :"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#: code:addons/l10n_din5008/models/base_document_layout.py:0
#, python-format
msgid "Invoice"
msgstr "Facture client"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#: code:addons/l10n_din5008/models/base_document_layout.py:0
#, python-format
msgid "Invoice Date"
msgstr "Date de facturation"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#: code:addons/l10n_din5008/models/base_document_layout.py:0
#, python-format
msgid "Invoice No."
msgstr "N° de facture"

#. module: l10n_din5008
#: model:ir.model,name:l10n_din5008.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: l10n_din5008
#: model:ir.model.fields,field_description:l10n_din5008.field_account_bank_statement_line__l10n_din5008_addresses
#: model:ir.model.fields,field_description:l10n_din5008.field_account_move__l10n_din5008_addresses
#: model:ir.model.fields,field_description:l10n_din5008.field_account_payment__l10n_din5008_addresses
msgid "L10N Din5008 Addresses"
msgstr "Adresses L10N Din5008"

#. module: l10n_din5008
#: model:ir.model.fields,field_description:l10n_din5008.field_account_analytic_line__l10n_din5008_document_title
#: model:ir.model.fields,field_description:l10n_din5008.field_account_bank_statement_line__l10n_din5008_document_title
#: model:ir.model.fields,field_description:l10n_din5008.field_account_move__l10n_din5008_document_title
#: model:ir.model.fields,field_description:l10n_din5008.field_account_payment__l10n_din5008_document_title
#: model:ir.model.fields,field_description:l10n_din5008.field_base_document_layout__l10n_din5008_document_title
msgid "L10N Din5008 Document Title"
msgstr "Titre du document L10N Din5008"

#. module: l10n_din5008
#: model:ir.model.fields,field_description:l10n_din5008.field_account_analytic_line__l10n_din5008_template_data
#: model:ir.model.fields,field_description:l10n_din5008.field_account_bank_statement_line__l10n_din5008_template_data
#: model:ir.model.fields,field_description:l10n_din5008.field_account_move__l10n_din5008_template_data
#: model:ir.model.fields,field_description:l10n_din5008.field_account_payment__l10n_din5008_template_data
#: model:ir.model.fields,field_description:l10n_din5008.field_base_document_layout__l10n_din5008_template_data
msgid "L10N Din5008 Template Data"
msgstr "Données de modèle L10N Din5008"

#. module: l10n_din5008
#: model_terms:ir.ui.view,arch_db:l10n_din5008.external_layout_din5008
msgid "Page: <span class=\"page\"/> of <span class=\"topage\"/>"
msgstr "Page : <span class=\"page\"/> sur <span class=\"topage\"/>"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#: code:addons/l10n_din5008/models/base_document_layout.py:0
#, python-format
msgid "Reference"
msgstr "Référence"

#. module: l10n_din5008
#: model:ir.model.fields,field_description:l10n_din5008.field_base_document_layout__report_footer
msgid "Report Footer"
msgstr "Pied de page de rapport"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#, python-format
msgid "Shipping Address:"
msgstr "Adresse de livraison :"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#, python-format
msgid "Source"
msgstr "Source"

#. module: l10n_din5008
#: model:ir.model.fields,field_description:l10n_din5008.field_base_document_layout__street
msgid "Street"
msgstr "Rue"

#. module: l10n_din5008
#: model:ir.model.fields,field_description:l10n_din5008.field_base_document_layout__street2
msgid "Street2"
msgstr "Rue 2"

#. module: l10n_din5008
#: model_terms:ir.ui.view,arch_db:l10n_din5008.external_layout_din5008
#: model_terms:ir.ui.view,arch_db:l10n_din5008.report_invoice_document
msgid "Tax ID"
msgstr "N° de TVA"

#. module: l10n_din5008
#: model:ir.model.fields,help:l10n_din5008.field_base_document_layout__account_fiscal_country_id
msgid "The country to use the tax reports from for this company"
msgstr ""
"Le pays à partir duquel utiliser les déclarations fiscales pour cette société"

#. module: l10n_din5008
#: model:ir.model.fields,help:l10n_din5008.field_base_document_layout__company_registry
msgid ""
"The registry number of the company. Use it if it is different from the Tax "
"ID. It must be unique across all partners of a same country"
msgstr ""
"Le numéro de registre de la société. Utilisez-le s'il est différent du "
"numéro d'identification fiscale. Il doit être unique pour tous les "
"partenaires d'un même pays"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#, python-format
msgid "Vendor Bill"
msgstr "Facture fournisseur"

#. module: l10n_din5008
#. odoo-python
#: code:addons/l10n_din5008/models/account_move.py:0
#, python-format
msgid "Vendor Credit Note"
msgstr "Avoir fournisseur"

#. module: l10n_din5008
#: model:ir.model.fields,field_description:l10n_din5008.field_base_document_layout__zip
msgid "Zip"
msgstr "Code postal"
