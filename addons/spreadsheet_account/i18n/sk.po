# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_account
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-08 07:29+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_datasource.js:0
#, python-format
msgid "%s is not a valid year."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid ""
"'%s' is not a valid period. Supported formats are \"21/12/2022\", "
"\"Q1/2022\", \"12/2022\", and \"2022\"."
msgstr ""

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_account_account
msgid "Account"
msgstr "Účet"

#. module: spreadsheet_account
#: model:ir.model,name:spreadsheet_account.model_res_company
msgid "Companies"
msgstr "Spoločnosti"

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Get the total balance for the specified account(s) and period."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Get the total credit for the specified account(s) and period."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Get the total debit for the specified account(s) and period."
msgstr ""

#. module: spreadsheet_account
#. odoo-python
#: code:addons/spreadsheet_account/models/account.py:0
#, python-format
msgid "Journal items for account prefix %s"
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Returns the account codes of a given group."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid ""
"Returns the ending date of the fiscal year encompassing the provided date."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid ""
"Returns the starting date of the fiscal year encompassing the provided date."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/index.js:0
#, python-format
msgid "See records"
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Set to TRUE to include unposted entries."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_datasource.js:0
#, python-format
msgid "The company fiscal year could not be found."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "The company to target (Advanced)."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "The company."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid ""
"The date range. Supported formats are \"21/12/2022\", \"Q1/2022\", "
"\"12/2022\", and \"2022\"."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "The day from which to extract the fiscal year end."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "The day from which to extract the fiscal year start."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "The prefix of the accounts."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "The technical account type (possible values are: %s)."
msgstr ""

#. module: spreadsheet_account
#. odoo-javascript
#: code:addons/spreadsheet_account/static/src/accounting_functions.js:0
#, python-format
msgid "Year offset applied to date_range."
msgstr ""
