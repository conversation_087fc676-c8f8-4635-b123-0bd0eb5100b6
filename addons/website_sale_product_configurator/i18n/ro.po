# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_product_configurator
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Price</span>"
msgstr "<span class=\"label\">Preț</span>"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "<span class=\"label\">Product</span>"
msgstr "<span class=\"label\">Produs</span>"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "<strong>Total:</strong>"
msgstr "<strong> Total: </strong>"

#. module: website_sale_product_configurator
#: model:ir.model.fields,field_description:website_sale_product_configurator.field_website__add_to_cart_action
msgid "Add To Cart Action"
msgstr "Acțiune de adăugare în coș"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.product_quantity_config
msgid "Add one"
msgstr "Adaugă"

#. module: website_sale_product_configurator
#. odoo-javascript
#: code:addons/website_sale_product_configurator/static/src/js/website_sale_options.js:0
#, python-format
msgid "Add to cart"
msgstr "Adaugă în coș"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "Available Options:"
msgstr "Opțiuni disponibile:"

#. module: website_sale_product_configurator
#. odoo-javascript
#: code:addons/website_sale_product_configurator/static/src/js/website_sale_options.js:0
#, python-format
msgid "Continue Shopping"
msgstr "Continuați cumpărăturile"

#. module: website_sale_product_configurator
#: model:ir.model.fields.selection,name:website_sale_product_configurator.selection__website__add_to_cart_action__force_dialog
msgid "Let the user decide (dialog)"
msgstr "Lăsați utilizatorul să decidă (dialog)"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.optional_product_items
msgid "Option not available"
msgstr "Opțiune indisponibilă"

#. module: website_sale_product_configurator
#. odoo-javascript
#: code:addons/website_sale_product_configurator/static/src/js/website_sale_options.js:0
#, python-format
msgid "Proceed to Checkout"
msgstr "Continuă către finalizare"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.optional_product_items
msgid "Product Image"
msgstr "Imagine produs"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.configure_optional_products
msgid "Quantity"
msgstr "Cantitate"

#. module: website_sale_product_configurator
#: model_terms:ir.ui.view,arch_db:website_sale_product_configurator.product_quantity_config
msgid "Remove one"
msgstr "Eliminați unu"

#. module: website_sale_product_configurator
#: model:ir.model,name:website_sale_product_configurator.model_sale_order
msgid "Sales Order"
msgstr "Comandă de vânzare"

#. module: website_sale_product_configurator
#: model:ir.model,name:website_sale_product_configurator.model_website
msgid "Website"
msgstr "Site web"
