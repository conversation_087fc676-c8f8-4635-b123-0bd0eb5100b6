<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- RESOURCE.RESOURCE -->
    <record id="view_resource_resource_search" model="ir.ui.view">
        <field name="name">resource.resource.search</field>
        <field name="model">resource.resource</field>
        <field name="arch" type="xml">
            <search string="Search Resource">
               <field name="name" />
               <field name="resource_type"/>
               <field name="user_id"/>
               <field name="calendar_id"/>
               <field name="company_id" groups="base.group_multi_company"/>
               <filter string="Human" name="human" domain="[('resource_type','=', 'user')]"/>
               <filter string="Material" name="material" domain="[('resource_type','=', 'material')]"/>
               <separator />
               <filter string="Archived" name="inactive" domain="[('active','=',False)]"/>
               <group expand="0" string="Group By">
                    <filter string="User" name="user" domain="[]" context="{'group_by':'user_id'}"/>
                    <filter string="Type" name="type" domain="[]" context="{'group_by':'resource_type'}"/>
                    <filter string="Company" name="company" domain="[]" context="{'group_by':'company_id'}" groups="base.group_multi_company"/>
                    <filter string="Working Time" name="working_period" domain="[]" context="{'group_by':'calendar_id'}"/>
                </group>
           </search>
        </field>
    </record>

    <record id="resource_resource_form" model="ir.ui.view">
        <field name="name">resource.resource.form</field>
        <field name="model">resource.resource</field>
        <field name="arch" type="xml">
            <form string="Resource">
            <sheet>
                <field name="active" invisible="1" />
                <field name="company_id" invisible="1"/>
                <widget name="web_ribbon" title="Archived" bg_color="text-bg-danger" invisible="active"/>
                <group>
                    <group name="user_details">
                        <field name="name"/>
                        <field name="user_id" invisible="resource_type == 'material'" required="resource_type == 'user'"/>
                        <field name="company_id" options="{'no_create': True}" groups="base.group_multi_company"/>
                        <field name="resource_type" />
                    </group>
                    <group name="resource_details">
                        <field name="calendar_id"/>
                        <field name="tz"/>
                        <field name="time_efficiency"/>
                    </group>
                </group>
            </sheet>
            </form>
        </field>
    </record>

    <record id="resource_resource_tree" model="ir.ui.view">
        <field name="name">resource.resource.tree</field>
        <field name="model">resource.resource</field>
        <field name="arch" type="xml">
            <tree string="Resources" multi_edit="1" default_order="name">
                <field name="company_id" column_invisible="True"/>
                <field name="name" />
                <field name="user_id" />
                <field name="company_id" groups="base.group_multi_company" optional="show"/>
                <field name="calendar_id" optional="show" />
                <field name="tz" optional="hide" />
                <field name="resource_type" optional="show" />
                <field name="time_efficiency"/>
            </tree>
        </field>
    </record>

    <record id="action_resource_resource_tree" model="ir.actions.act_window">
        <field name="name">Resources</field>
        <field name="res_model">resource.resource</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{}</field>
        <field name="search_view_id" ref="view_resource_resource_search"/>
        <field name="help">Resources allow you to create and manage resources that should be involved in a specific project phase. You can also set their efficiency level and workload based on their weekly working hours.</field>
    </record>

    <record id="resource_resource_action_from_calendar" model="ir.actions.act_window">
        <field name="name">Resources</field>
        <field name="res_model">resource.resource</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{
            'default_calendar_id': active_id,
            'search_default_calendar_id': active_id}</field>
        <field name="search_view_id" ref="view_resource_resource_search"/>
        <field name="help">Resources allow you to create and manage resources that should be involved in a specific project phase. You can also set their efficiency level and workload based on their weekly working hours.</field>
    </record>
</odoo>
