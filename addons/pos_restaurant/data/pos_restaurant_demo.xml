<odoo>
    <data noupdate="1">
        <record id="drinks" model="pos.category">
            <field name="name">Drinks</field>
            <field name="image_128" type="base64" file="pos_restaurant/static/img/drink_category.png" />
        </record>

        <record id="product_category_pos_food" model="product.category">
            <field name="parent_id" ref="point_of_sale.product_category_pos"/>
            <field name="name">Food</field>
        </record>

        <record id="food" model="pos.category">
            <field name="name">Food</field>
            <field name="image_128" type="base64" file="pos_restaurant/static/img/food_category.png" />
        </record>

        <!-- Food -->
        <record id="pos_food_margherita" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">7.0</field>
            <field name="name">Margherita</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-pizza-ma.jpg"/>
        </record>
        <record id="pos_food_funghi" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">7.0</field>
            <field name="name">Funghi</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-pizza-fu.jpg"/>
        </record>
        <record id="pos_food_vege" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">7.0</field>
            <field name="name">Vegetarian</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-pizza-ve.jpg"/>
        </record>
        <record id="pos_food_bolo" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">4.5</field>
            <field name="name">Pasta Bolognese</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-pasta.jpg"/>
        </record>
        <record id="pos_food_4formaggi" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">5.5</field>
            <field name="name">Pasta 4 formaggi </field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-pasta-4f.jpg"/>
        </record>
        <record id="pos_food_bacon" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">7.5</field>
            <field name="name">Bacon Burger</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-burger.jpg"/>
        </record>
        <record id="pos_food_cheeseburger" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">7.0</field>
            <field name="name">Cheese Burger</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-cheeseburger.jpg"/>
        </record>
        <record id="pos_food_chicken" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">3.0</field>
            <field name="name">Chicken Curry Sandwich</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-sandwich.jpg"/>
        </record>
        <record id="pos_food_tuna" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">3.0</field>
            <field name="name">Spicy Tuna Sandwich</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-tuna.jpg"/>
        </record>
        <record id="pos_food_mozza" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">3.9</field>
            <field name="name">Mozzarella Sandwich</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-mozza.jpg"/>
        </record>
        <record id="pos_food_club" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">3.4</field>
            <field name="name">Club Sandwich</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-club.jpg"/>
        </record>
        <record id="pos_food_maki" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">12.0</field>
            <field name="name">Lunch Maki 18pc</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-maki.jpg"/>
        </record>
        <record id="pos_food_salmon" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">13.80</field>
            <field name="name">Lunch Salmon 20pc</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-salmon.jpg"/>
        </record>
        <record id="pos_food_temaki" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">14.0</field>
            <field name="name">Lunch Temaki mix 3pc</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-temaki.jpg"/>
        </record>
        <record id="pos_food_chirashi" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">9.25</field>
            <field name="name">Salmon and Avocado</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
            <field name="categ_id" ref="pos_restaurant.product_category_pos_food"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-salmon-avocado.jpg"/>
        </record>

        <!-- Drinks -->
        <record id="coke" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">2.20</field>
            <field name="name">Coca-Cola</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('drinks')])]"/>
            <field name="categ_id" ref="point_of_sale.product_category_pos"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-coke.jpg"/>
        </record>

        <record id="water" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">2.20</field>
            <field name="name">Water</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('drinks')])]"/>
            <field name="categ_id" ref="point_of_sale.product_category_pos"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-water.jpg"/>
        </record>

        <record id="minute_maid" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">2.20</field>
            <field name="name">Minute Maid</field>
            <field name="weight">0.01</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('drinks')])]"/>
            <field name="categ_id" ref="point_of_sale.product_category_pos"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-minute_maid.jpg"/>
        </record>

        <record id="espresso" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">4.70</field>
            <field name="name">Espresso</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('drinks')])]"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-espresso.jpg"/>
        </record>

        <record id="green_tea" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">4.70</field>
            <field name="name">Green Tea</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('drinks')])]"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-green_tea.jpg"/>
        </record>

        <record id="milkshake_banana" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">3.60</field>
            <field name="name">Milkshake Banana</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('drinks')])]"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-milkshake_banana.jpg"/>
        </record>

        <record id="ice_tea" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">2.20</field>
            <field name="name">Ice Tea</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('drinks')])]"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-ice_tea.jpg"/>
        </record>

        <record id="schweppes" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">2.20</field>
            <field name="name">Schweppes</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('drinks')])]"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-schweppes.jpg"/>
        </record>

        <record id="fanta" model="product.product">
            <field name="available_in_pos">True</field>
            <field name="list_price">2.20</field>
            <field name="name">Fanta</field>
            <field name="pos_categ_ids" eval="[(6, 0, [ref('drinks')])]"/>
            <field name="image_1920" type="base64" file="pos_restaurant/static/img/th-fanta.jpg"/>
        </record>

        <!-- Combo -->
        <record id="cheeseburger_combo_line" model="pos.combo.line">
            <field name="product_id" ref="pos_food_cheeseburger"/>
            <field name="combo_price">0</field>
        </record>
        <record id="bacon_burger_combo_line" model="pos.combo.line">
            <field name="product_id" ref="pos_food_bacon"/>
            <field name="combo_price">0</field>
        </record>
        <record id="burger_combo" model="pos.combo">
            <field name="name">Burgers Choice</field>
            <field name="combo_line_ids" eval="[(6, 0, [ref('cheeseburger_combo_line'), ref('bacon_burger_combo_line')])]"/>
        </record>

        <record id="coke_combo_line" model="pos.combo.line">
            <field name="product_id" ref="coke"/>
            <field name="combo_price">0</field>
        </record>
        <record id="water_combo_line" model="pos.combo.line">
            <field name="product_id" ref="water"/>
            <field name="combo_price">0</field>
        </record>
        <record id="maid_combo_line" model="pos.combo.line">
            <field name="product_id" ref="minute_maid"/>
            <field name="combo_price">0</field>
        </record>
        <record id="milkshake_combo_line" model="pos.combo.line">
            <field name="product_id" ref="milkshake_banana"/>
            <field name="combo_price">2</field>
        </record>
        <record id="drink_combo" model="pos.combo">
            <field name="name">Drinks choice</field>
            <field name="combo_line_ids" eval="[(6, 0, [ref('coke_combo_line'), ref('water_combo_line'), ref('maid_combo_line'), ref('milkshake_combo_line')])]"/>
        </record>

        <record id="burger_drink_combo" model="product.product">
          <field name="available_in_pos">True</field>
          <field name="list_price">10</field>
          <field name="name">Burger Menu Combo</field>
          <field name="type">combo</field>
          <field name="uom_id" ref="uom.product_uom_unit"/>
          <field name="uom_po_id" ref="uom.product_uom_unit"/>
          <field name="image_1920" type="base64" file="pos_restaurant/static/img/combo-hamb.jpg"/>
          <field name="combo_ids" eval="[(6, 0, [ref('drink_combo'), ref('burger_combo')])]"/>
          <field name="pos_categ_ids" eval="[(6, 0, [ref('food')])]"/>
          <field name="taxes_id" eval="[(5,)]"/>  <!-- no taxes -->
        </record>

        <function model="restaurant.floor" name="unlink">
            <value model="restaurant.floor" eval="obj().search([
                    ('pos_config_ids', 'in', ref('pos_config_main_restaurant')),
                ]).id"/>
        </function>

        <!-- Pos Config -->
        <record model="pos.config" id="pos_config_main_restaurant">
            <field name="iface_printbill">True</field>
            <field name="limit_categories">True</field>
            <field name="iface_available_categ_ids"
                eval="[(6, 0, [ref('drinks'), ref('food')])]" />
        </record>

        <!-- Closed Sessions -->
        <!-- forcecreate is set to false in order to not create record when updating the db -->

        <function model="ir.model.data" name="_update_xmlids">
            <value model="base" eval="[{
                    'xml_id': 'pos_restaurant.payment_method',
                    'record': obj().env.ref('pos_restaurant.pos_config_main_restaurant')._get_payment_method('bank'),
                    'noupdate': True,
                }]" />
        </function>

        <!-- Closed Session 3 -->

        <record id="pos_closed_session_3" model="pos.session" forcecreate="False">
            <field name="config_id" ref="pos_config_main_restaurant" />
            <field name="user_id" ref="base.user_admin" />
            <field name="start_at" eval="(DateTime.today() + relativedelta(days=-1)).strftime('%Y-%m-%d %H:%M:%S')" />
            <field name="stop_at"
                eval="(DateTime.today() + relativedelta(days=-1, hours=1)).strftime('%Y-%m-%d %H:%M:%S')" />
        </record>

        <record id="pos_closed_order_3_1" model="pos.order" forcecreate="False">
            <field name="session_id" ref="pos_closed_session_3" />
            <field name="company_id" ref="base.main_company" />
            <field name="name">ClosedDemo/0005</field>
            <field name="state">paid</field>
            <field name="amount_total">14.0</field>
            <field name="amount_tax">0.0</field>
            <field name="amount_paid">14.0</field>
            <field name="amount_return">0.0</field>
            <field name="pos_reference">Order 00000-003-1001</field>
        </record>

        <record id="pos_closed_orderline_3_1_1" model="pos.order.line" forcecreate="False">
            <field name="name">Closed Orderline 3.1.1</field>
            <field name="product_id" ref="pos_food_margherita" />
            <field name="price_subtotal">7.0</field>
            <field name="price_subtotal_incl">7.0</field>
            <field name="price_unit">7.0</field>
            <field name="order_id" ref="pos_closed_order_3_1" />
            <field name="full_product_name">Margherita</field>
        </record>

        <record id="pos_closed_orderline_3_1_2" model="pos.order.line" forcecreate="False">
            <field name="name">Closed Orderline 3.1.2</field>
            <field name="product_id" ref="pos_food_funghi" />
            <field name="price_subtotal">7.0</field>
            <field name="price_subtotal_incl">7.0</field>
            <field name="price_unit">7.0</field>
            <field name="order_id" ref="pos_closed_order_3_1" />
            <field name="full_product_name">Funghi</field>
        </record>

        <record id="pos_payment_1" model="pos.payment" forcecreate="False">
            <field name="payment_method_id" ref="pos_restaurant.payment_method" />
            <field name="pos_order_id" ref="pos_closed_order_3_1" />
            <field name="amount">14.0</field>
        </record>

        <record id="pos_closed_order_3_2" model="pos.order" forcecreate="False">
            <field name="session_id" ref="pos_closed_session_3" />
            <field name="company_id" ref="base.main_company" />
            <field name="name">ClosedDemo/0006</field>
            <field name="state">paid</field>
            <field name="amount_total">7.0</field>
            <field name="amount_tax">0.0</field>
            <field name="amount_paid">7.0</field>
            <field name="amount_return">0.0</field>
            <field name="pos_reference">Order 00000-003-1002</field>
        </record>

        <record id="pos_closed_orderline_3_2_1" model="pos.order.line" forcecreate="False">
            <field name="name">Closed Orderline 3.2.1</field>
            <field name="product_id" ref="pos_food_vege" />
            <field name="price_subtotal">7.0</field>
            <field name="price_subtotal_incl">7.0</field>
            <field name="price_unit">7.0</field>
            <field name="order_id" ref="pos_closed_order_3_2" />
            <field name="full_product_name">Vegetarian</field>
        </record>

        <record id="pos_payment_2" model="pos.payment" forcecreate="False">
            <field name="payment_method_id" ref="pos_restaurant.payment_method" />
            <field name="pos_order_id" ref="pos_closed_order_3_2" />
            <field name="amount">7.0</field>
        </record>

        <function model="pos.session" name="action_pos_session_closing_control"
            eval="[[ref('pos_closed_session_3')]]" />

        <!-- Closed Session 4 -->

        <record id="pos_closed_session_4" model="pos.session" forcecreate="False">
            <field name="config_id" ref="pos_config_main_restaurant" />
            <field name="user_id" ref="base.user_admin" />
            <field name="start_at" eval="(DateTime.today() + relativedelta(days=-1)).strftime('%Y-%m-%d %H:%M:%S')" />
            <field name="stop_at"
                eval="(DateTime.today() + relativedelta(days=-1, hours=1)).strftime('%Y-%m-%d %H:%M:%S')" />
        </record>

        <record id="pos_closed_order_4_1" model="pos.order" forcecreate="False">
            <field name="session_id" ref="pos_closed_session_4" />
            <field name="company_id" ref="base.main_company" />
            <field name="name">ClosedDemo/0007</field>
            <field name="state">paid</field>
            <field name="amount_total">6.7</field>
            <field name="amount_tax">0.0</field>
            <field name="amount_paid">6.7</field>
            <field name="amount_return">0.0</field>
            <field name="pos_reference">Order 00000-004-1001</field>
        </record>

        <record id="pos_closed_orderline_4_1_1" model="pos.order.line" forcecreate="False">
            <field name="name">Closed Orderline 4.1.1</field>
            <field name="product_id" ref="water" />
            <field name="price_subtotal">2.20</field>
            <field name="price_subtotal_incl">2.20</field>
            <field name="price_unit">2.20</field>
            <field name="order_id" ref="pos_closed_order_4_1" />
            <field name="full_product_name">Water</field>
        </record>

        <record id="pos_closed_orderline_4_1_2" model="pos.order.line" forcecreate="False">
            <field name="name">Closed Orderline 4.1.2</field>
            <field name="product_id" ref="pos_food_bolo" />
            <field name="price_subtotal">4.5</field>
            <field name="price_subtotal_incl">4.5</field>
            <field name="price_unit">4.5</field>
            <field name="order_id" ref="pos_closed_order_4_1" />
            <field name="full_product_name">Pasta Bolognese</field>
        </record>

        <record id="pos_payment_3" model="pos.payment" forcecreate="False">
            <field name="payment_method_id" ref="pos_restaurant.payment_method" />
            <field name="pos_order_id" ref="pos_closed_order_4_1" />
            <field name="amount">6.7</field>
        </record>

        <record id="pos_closed_order_4_2" model="pos.order" forcecreate="False">
            <field name="session_id" ref="pos_closed_session_4" />
            <field name="company_id" ref="base.main_company" />
            <field name="name">ClosedDemo/0008</field>
            <field name="state">paid</field>
            <field name="amount_total">28.0</field>
            <field name="amount_tax">0.0</field>
            <field name="amount_paid">28.0</field>
            <field name="amount_return">0.0</field>
            <field name="pos_reference">Order 00000-004-1002</field>
        </record>

        <record id="pos_closed_orderline_4_2_1" model="pos.order.line" forcecreate="False">
            <field name="name">Closed Orderline 4.2.1</field>
            <field name="product_id" ref="pos_food_cheeseburger" />
            <field name="price_subtotal">28.0</field>
            <field name="price_subtotal_incl">28.0</field>
            <field name="price_unit">7.0</field>
            <field name="qty">4</field>
            <field name="order_id" ref="pos_closed_order_4_2" />
            <field name="full_product_name">Cheese Burger</field>
        </record>

        <record id="pos_payment_4" model="pos.payment" forcecreate="False">
            <field name="payment_method_id" ref="pos_restaurant.payment_method" />
            <field name="pos_order_id" ref="pos_closed_order_4_2" />
            <field name="amount">28.0</field>
        </record>

        <function model="pos.session" name="action_pos_session_closing_control"
            eval="[[ref('pos_closed_session_4')]]" />

        <!-- Floors: Main Floor -->
        <record id="floor_main" model="restaurant.floor">
            <field name="name">Main Floor</field>
            <field name="background_color">rgb(249,250,251)</field>
            <field name="pos_config_ids" eval="[(6, 0, [ref('pos_restaurant.pos_config_main_restaurant')])]" />
        </record>

        <record id="table_01" model="restaurant.table">
            <field name="name">1</field>
            <field name="floor_id" ref="pos_restaurant.floor_main" />
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">50</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_02" model="restaurant.table">
            <field name="name">2</field>
            <field name="floor_id" ref="pos_restaurant.floor_main" />
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">212</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_03" model="restaurant.table">
            <field name="name">3</field>
            <field name="floor_id" ref="pos_restaurant.floor_main" />
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">374</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_04" model="restaurant.table">
            <field name="name">4</field>
            <field name="floor_id" ref="pos_restaurant.floor_main" />
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">536</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_05" model="restaurant.table">
            <field name="name">5</field>
            <field name="floor_id" ref="pos_restaurant.floor_main" />
            <field name="seats">4</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">698</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_06" model="restaurant.table">
            <field name="name">6</field>
            <field name="floor_id" ref="pos_restaurant.floor_main" />
            <field name="seats">4</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">860</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_07" model="restaurant.table">
            <field name="name">7</field>
            <field name="floor_id" ref="pos_restaurant.floor_main" />
            <field name="seats">4</field>
            <field name="color">rgb(235,109,109)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">50</field>
            <field name="position_v">280</field>
        </record>

        <record id="table_08" model="restaurant.table">
            <field name="name">8</field>
            <field name="floor_id" ref="pos_restaurant.floor_main" />
            <field name="seats">4</field>
            <field name="color">rgb(235,109,109)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">212</field>
            <field name="position_v">280</field>
        </record>

        <record id="table_09" model="restaurant.table">
            <field name="name">9</field>
            <field name="floor_id" ref="pos_restaurant.floor_main" />
            <field name="seats">6</field>
            <field name="color">rgb(235,109,109)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">698</field>
            <field name="position_v">280</field>
        </record>

        <record id="table_10" model="restaurant.table">
            <field name="name">10</field>
            <field name="floor_id" ref="pos_restaurant.floor_main" />
            <field name="seats">6</field>
            <field name="color">rgb(235,109,109)</field>
            <field name="shape">square</field>
            <field name="width">100</field>
            <field name="height">100</field>
            <field name="position_h">860</field>
            <field name="position_v">280</field>
        </record>

        <record id="table_11" model="restaurant.table">
            <field name="name">11</field>
            <field name="floor_id" ref="pos_restaurant.floor_main" />
            <field name="seats">6</field>
            <field name="color">rgb(78,210,190)</field>
            <field name="shape">round</field>
            <field name="width">210</field>
            <field name="height">210</field>
            <field name="position_h">400</field>
            <field name="position_v">230</field>
        </record>

        <!-- Restaurant Floor: Patio -->

        <record id="floor_patio" model="restaurant.floor">
            <field name="name">Patio</field>
            <field name="background_color">rgb(130, 233, 171)</field>
            <field name="pos_config_ids" eval="[(6, 0, [ref('pos_restaurant.pos_config_main_restaurant')])]" />
        </record>

        <!-- Patio: Left table row -->

        <record id="table_21" model="restaurant.table">
            <field name="name">1</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio" />
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">85</field>
            <field name="position_h">100</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_22" model="restaurant.table">
            <field name="name">2</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio" />
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">85</field>
            <field name="position_h">100</field>
            <field name="position_v">166</field>
        </record>

        <record id="table_23" model="restaurant.table">
            <field name="name">3</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio" />
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">85</field>
            <field name="position_h">100</field>
            <field name="position_v">283</field>
        </record>

        <record id="table_24" model="restaurant.table">
            <field name="name">4</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio" />
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">85</field>
            <field name="position_h">100</field>
            <field name="position_v">400</field>
        </record>

        <!-- Patio: Right table row -->

        <record id="table_25" model="restaurant.table">
            <field name="name">5</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio" />
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">85</field>
            <field name="position_h">800</field>
            <field name="position_v">50</field>
        </record>

        <record id="table_26" model="restaurant.table">
            <field name="name">6</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio" />
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">85</field>
            <field name="position_h">800</field>
            <field name="position_v">166</field>
        </record>

        <record id="table_27" model="restaurant.table">
            <field name="name">7</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio" />
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">85</field>
            <field name="position_h">800</field>
            <field name="position_v">283</field>
        </record>

        <record id="table_28" model="restaurant.table">
            <field name="name">8</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio" />
            <field name="seats">2</field>
            <field name="color">rgb(53,211,116)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">85</field>
            <field name="position_h">800</field>
            <field name="position_v">400</field>
        </record>

        <!-- Patio: Center table block -->

        <record id="table_29" model="restaurant.table">
            <field name="name">9</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio" />
            <field name="seats">4</field>
            <field name="color">rgb(235,191,109)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">120</field>
            <field name="position_h">330</field>
            <field name="position_v">100</field>
        </record>

        <record id="table_29" model="restaurant.table">
            <field name="name">9</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio" />
            <field name="seats">4</field>
            <field name="color">rgb(235,191,109)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">120</field>
            <field name="position_h">330</field>
            <field name="position_v">100</field>
        </record>

        <record id="table_30" model="restaurant.table">
            <field name="name">10</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio" />
            <field name="seats">4</field>
            <field name="color">rgb(235,191,109)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">120</field>
            <field name="position_h">560</field>
            <field name="position_v">100</field>
        </record>

        <record id="table_31" model="restaurant.table">
            <field name="name">11</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio" />
            <field name="seats">4</field>
            <field name="color">rgb(235,191,109)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">120</field>
            <field name="position_h">330</field>
            <field name="position_v">315</field>
        </record>

        <record id="table_32" model="restaurant.table">
            <field name="name">12</field>
            <field name="floor_id" ref="pos_restaurant.floor_patio" />
            <field name="seats">4</field>
            <field name="color">rgb(235,191,109)</field>
            <field name="shape">square</field>
            <field name="width">130</field>
            <field name="height">120</field>
            <field name="position_h">560</field>
            <field name="position_v">315</field>
        </record>

        <function model="pos.config" name="add_cash_payment_method" />

        <!-- Open Session -->

        <record id="pos_open_session_2" model="pos.session" forcecreate="False">
            <field name="config_id" ref="pos_config_main_restaurant" />
            <field name="user_id" ref="base.user_admin" />
        </record>

        <record id="pos_open_order_2" model="pos.order" forcecreate="False">
            <field name="session_id" ref="pos_open_session_2" />
            <field name="company_id" ref="base.main_company" />
            <field name="name">Restaurant/00001</field>
            <field name="state">draft</field>
            <field name="amount_total">22.90</field>
            <field name="amount_tax">0.0</field>
            <field name="amount_paid">0.0</field>
            <field name="amount_return">0.0</field>
            <field name="pos_reference">Order 00002-001-0000</field>
            <field name="partner_id" ref="base.res_partner_1" />
            <field name="table_id" ref="table_01" />
            <field name="customer_count">8</field>
        </record>

        <record id="pos_orderline_2" model="pos.order.line" forcecreate="False">
            <field name="name">Orderline 2</field>
            <field name="product_id" ref="coke" />
            <field name="price_subtotal">4.40</field>
            <field name="price_subtotal_incl">4.40</field>
            <field name="price_unit">2.20</field>
            <field name="qty">2</field>
            <field name="order_id" ref="pos_open_order_2" />
            <field name="full_product_name">Coca-Cola</field>
            <field name="uuid">00000000-0000-4000-000000000000</field>
        </record>

        <record id="pos_orderline_3" model="pos.order.line" forcecreate="False">
            <field name="name">Orderline 3</field>
            <field name="product_id" ref="pos_food_chirashi" />
            <field name="price_subtotal">18.5</field>
            <field name="price_subtotal_incl">18.5</field>
            <field name="price_unit">9.25</field>
            <field name="qty">2</field>
            <field name="order_id" ref="pos_open_order_2" />
            <field name="full_product_name">Salmon and Avocado</field>
            <field name="uuid">00000000-0000-4000-000000000001</field>
        </record>

        <record id="pos_open_order_3" model="pos.order" forcecreate="False">
            <field name="session_id" ref="pos_open_session_2" />
            <field name="company_id" ref="base.main_company" />
            <field name="name">Restaurant/00002</field>
            <field name="state">draft</field>
            <field name="amount_total">21.8</field>
            <field name="amount_tax">0.0</field>
            <field name="amount_paid">0.0</field>
            <field name="amount_return">0.0</field>
            <field name="pos_reference">Order 00002-002-0000</field>
            <field name="partner_id" ref="base.res_partner_2" />
            <field name="table_id" ref="table_02" />
            <field name="customer_count">3</field>
        </record>

        <record id="pos_orderline_4" model="pos.order.line" forcecreate="False">
            <field name="name">Orderline 4</field>
            <field name="product_id" ref="pos_food_temaki" />
            <field name="price_subtotal">14.0</field>
            <field name="price_subtotal_incl">14.0</field>
            <field name="price_unit">14.0</field>
            <field name="qty">1</field>
            <field name="order_id" ref="pos_open_order_3" />
            <field name="full_product_name">Lunch Temaki mix 3pc</field>
            <field name="uuid">00000000-0000-4000-000000000002</field>
        </record>

        <record id="pos_orderline_5" model="pos.order.line" forcecreate="False">
            <field name="name">Orderline 5</field>
            <field name="product_id" ref="pos_food_mozza" />
            <field name="price_subtotal">7.8</field>
            <field name="price_subtotal_incl">7.8</field>
            <field name="price_unit">3.9</field>
            <field name="qty">2</field>
            <field name="order_id" ref="pos_open_order_3" />
            <field name="full_product_name">Mozzarella Sandwich</field>
            <field name="uuid">00000000-0000-4000-000000000003</field>
        </record>

        <record id="pos_open_order_4" model="pos.order" forcecreate="False">
            <field name="session_id" ref="pos_open_session_2" />
            <field name="company_id" ref="base.main_company" />
            <field name="name">Restaurant/00003</field>
            <field name="state">draft</field>
            <field name="amount_total">10.5</field>
            <field name="amount_tax">0.0</field>
            <field name="amount_paid">0.0</field>
            <field name="amount_return">0.0</field>
            <field name="pos_reference">Order 00002-003-0000</field>
            <field name="partner_id" ref="base.res_partner_4" />
            <field name="table_id" ref="table_04" />
            <field name="customer_count">5</field>
        </record>

        <record id="pos_orderline_6" model="pos.order.line" forcecreate="False">
            <field name="name">Orderline 6</field>
            <field name="product_id" ref="pos_food_chicken" />
            <field name="price_subtotal">3.0</field>
            <field name="price_subtotal_incl">3.0</field>
            <field name="price_unit">3.0</field>
            <field name="qty">1</field>
            <field name="order_id" ref="pos_open_order_4" />
            <field name="full_product_name">Chicken Curry Sandwich</field>
            <field name="uuid">00000000-0000-4000-000000000004</field>
        </record>

        <record id="pos_orderline_7" model="pos.order.line" forcecreate="False">
            <field name="name">Orderline 7</field>
            <field name="product_id" ref="pos_food_bacon" />
            <field name="price_subtotal">7.5</field>
            <field name="price_subtotal_incl">7.5</field>
            <field name="price_unit">7.5</field>
            <field name="qty">1</field>
            <field name="order_id" ref="pos_open_order_4" />
            <field name="full_product_name">Bacon Burger</field>
            <field name="uuid">00000000-0000-4000-000000000005</field>
        </record>

        <record id="pos_open_order_5" model="pos.order" forcecreate="False">
            <field name="session_id" ref="pos_open_session_2" />
            <field name="company_id" ref="base.main_company" />
            <field name="name">Restaurant/00004</field>
            <field name="state">draft</field>
            <field name="amount_total">5.5</field>
            <field name="amount_tax">0.0</field>
            <field name="amount_paid">0.0</field>
            <field name="amount_return">0.0</field>
            <field name="pos_reference">Order 00002-004-0000</field>
            <field name="partner_id" ref="base.res_partner_10" />
            <field name="table_id" ref="table_06" />
            <field name="customer_count">1</field>
        </record>

        <record id="pos_orderline_8" model="pos.order.line" forcecreate="False">
            <field name="name">Orderline 8</field>
            <field name="product_id" ref="pos_food_4formaggi" />
            <field name="price_subtotal">5.5</field>
            <field name="price_subtotal_incl">5.5</field>
            <field name="price_unit">5.5</field>
            <field name="qty">1</field>
            <field name="order_id" ref="pos_open_order_5" />
            <field name="full_product_name">Pizza 4 Formaggi</field>
        </record>

        <function model="pos.session" name="_set_last_order_preparation_change"
            eval="[[ref('pos_open_order_2'), ref('pos_open_order_3'), ref('pos_open_order_4')]]"/>
    </data>
</odoo>
