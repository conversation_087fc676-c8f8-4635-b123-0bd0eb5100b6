# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_epson_printer
# 
# Translators:
# Wil Odoo, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Abe Manyo, 2023\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Cashdrawer"
msgstr "Laci Kas"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid ""
"Check on the printer configuration for the 'Device ID' setting. It should be"
" set to: "
msgstr ""
"Periksa konfigurasi printer untuk pengaturan 'Device ID'. Pengaturan "
"harusnya disetel ke:"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_res_config_settings
msgid "Config Settings"
msgstr "Pengaturan Konfigurasi"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Epson Printer IP"
msgstr "Ip Epson Printer"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_printer__epson_printer_ip
msgid "Epson Printer IP Address"
msgstr "Alamat Ip Epson Printer"

#. module: pos_epson_printer
#. odoo-python
#: code:addons/pos_epson_printer/models/pos_printer.py:0
#, python-format
msgid "Epson Printer IP Address cannot be empty."
msgstr "Alamat Ip Epson Printer tidak boleh kosong."

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Epson Receipt Printer IP Address"
msgstr "ALamat IP Epson Receipt Printer "

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid ""
"If you are on a secure server (HTTPS) please make sure you manually accepted"
" the certificate by accessing %s. "
msgstr ""
"Jika Anda berada pada server aman (HTTPS) mohon pastikan Anda secara manual "
"menerima sertifikat dengan mengakses %s."

#. module: pos_epson_printer
#: model:ir.model.fields,help:pos_epson_printer.field_pos_config__epson_printer_ip
#: model:ir.model.fields,help:pos_epson_printer.field_pos_printer__epson_printer_ip
msgid "Local IP address of an Epson receipt printer."
msgstr "IP adress lokal untuk pencetak resi Epson."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid "No paper was detected by the printer"
msgstr "Tidak ada kertas yang dideteksi printer"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid "Please check if the printer has enough paper and is ready to print."
msgstr ""
"Mohon periksa bila printer memiliki kertas yang cukup dan siap untuk "
"mencetak."

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Konfigurasi Point of Sale"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_printer
msgid "Point of Sale Printer"
msgstr "Printer POS"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_session
msgid "Point of Sale Session"
msgstr "Sesi Point of Sale"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_res_config_settings__pos_epson_printer_ip
msgid "Pos Epson Printer Ip"
msgstr "IP Epson Printer POS"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_printer__printer_type
msgid "Printer Type"
msgstr "Tipe Printer"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid "Printing failed"
msgstr "Pencetakan gagal"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid ""
"The Epson receipt printer will be used instead of the receipt printer "
"connected to the IoT Box."
msgstr ""
"Epson receipt printer akan digunakan alih-alih receipt printer yang "
"terhubung ke IoT Box."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid "The following error code was given by the printer:"
msgstr "Kode error berikut diberikan oleh printer:"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid "The printer was successfully reached, but it wasn't able to print."
msgstr "Printer sukses terhubung, tapi tidak dapat mencetak."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
#, python-format
msgid "To find more details on the error reason, please search online for:"
msgstr ""
"Untuk mendapatkan lebih banyak detail mengenai alasan error, mohon cari "
"online untuk:"

#. module: pos_epson_printer
#: model:ir.model.fields.selection,name:pos_epson_printer.selection__pos_printer__printer_type__epson_epos
msgid "Use an Epson printer"
msgstr "Gunakan printer Epson"
