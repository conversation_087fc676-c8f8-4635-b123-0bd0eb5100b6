# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <PERSON>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Rune Restad, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-06 18:38+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: <PERSON> (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "%(name)s's Sales Order Items"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "%(name)s's Sales Orders"
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid ""
"<span class=\"fa fa-lg fa-building-o fa-fw\" title=\"Values set here are "
"company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            <span class=\"o_stat_value\">0</span> Sales Order\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Make Billable\n"
"                        </span>"
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Sales Orders\n"
"                        </span>"
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "<span class=\"o_stat_text\">Sales Order</span>"
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
msgid "<span>)</span>"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Based on Delivered Quantity (Manual)"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Based on Milestones"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__allow_billable
#: model:ir.model.fields,field_description:sale_project.field_project_project__allow_billable
#: model:ir.model.fields,field_description:sale_project.field_project_task__allow_billable
msgid "Billable"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "Canceled"
msgstr "Avbrutt"

#. module: sale_project
#: model:ir.model,name:sale_project.model_res_config_settings
msgid "Config Settings"
msgstr "Innstillinger"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Cost of Goods Sold"
msgstr "Kostnader av varesalg"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Cost of Goods Sold Items"
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Create Project"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr "Opprett på ordre"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__project_partner_id
#: model:ir.model.fields,field_description:sale_project.field_project_project__partner_id
msgid "Customer"
msgstr "Kunde"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Customer Invoices"
msgstr "Kundefakturaer"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Delivered"
msgstr "Levert"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__display_sale_order_button
msgid "Display Sales Order"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__display_sales_stat_buttons
msgid "Display Sales Stat Buttons"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__visible_project
msgid "Display project"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "Done"
msgstr "Fullført"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Down Payments"
msgstr "Nedbetalinger"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__project_id
msgid "Generated Project"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__task_id
msgid "Generated Task"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_to_invoice
msgid "Has SO to Invoice"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_with_nothing_to_invoice
msgid "Has a SO with an invoice status of No"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "In Progress"
msgstr "Pågår"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__invoice_count
msgid "Invoice Count"
msgstr "Fakturaantall"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create a project"
" for the order with a task for each sales order line to track the time "
"spent."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create a task in"
" an existing project to track the time spent."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create an empty "
"project for the order to track the time spent."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). "
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create a project for the order with a task for each "
"sales order line to track the time spent."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create a task in an existing project to track the time "
"spent."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create an empty project for the order to track the time "
"spent."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice your milestones when they are reached."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create a project for the "
"order with a task for each sales order line to track the time spent."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create a task in an existing "
"project to track the time spent."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create an empty project for "
"the order to track the time spent."
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_project_view_form_simplified_inherit
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Invoice your time and material to customers"
msgstr ""

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Invoiced"
msgstr "Fakturert"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Invoices"
msgstr "Fakturaer"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Invoicing Policy"
msgstr "Faktureringspolicy"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__is_product_milestone
msgid "Is Product Milestone"
msgstr ""

#. module: sale_project
#: model:ir.model,name:sale_project.model_account_move_line
msgid "Journal Item"
msgstr "Bilagslinje"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Load more"
msgstr "Last inn mer"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_type
#: model:ir.model.fields,help:sale_project.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Sett ordreantall manuelt: Fakturer basert på det manuelt angitte antallet, uten å opprette en analytisk konto.\n"
"Timelister i kontrakt: Fakturer basert på registrerte timer i den relaterte timelisten.\n"
"Opprett en oppgave og registrer timer: Opprett en oppgave ved bekreftelse av salgsordre, og registrer jobbede timer."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Materials"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Metode for oppdatering av levert antall"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__milestone_count
msgid "Milestone Count"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_project.selection__sale_order_line__qty_delivered_method__milestones
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Milestones"
msgstr "Milepæler"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "New Sales Order Item"
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "No Sales Order"
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "Non-billable"
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "Not Billed"
msgstr ""

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__no
msgid "Nothing"
msgstr "Ingenting"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_count
msgid "Number of Projects"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,help:sale_project.field_product_template__service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or task.         From those, you can track the service you are selling.\n"
"         'In sale order's project': Will use the sale order's configured project if defined or fallback to         creating a new project based on the selected template."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Other Services"
msgstr "Andre tjenester"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__quantity_percentage
msgid ""
"Percentage of the ordered quantity that will automatically be delivered once"
" the milestone is reached."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Portal Sale Order"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Prepaid/Fixed Price"
msgstr "Forhåndsbetalt/fast pris"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product"
msgstr "Produkt"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product Variant"
msgstr "Produktvariant"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "Prosjekt"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr ""

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_milestone
msgid "Project Milestone"
msgstr ""

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_type__milestones
msgid "Project Milestones"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "Prosjektmal"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_ids
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Projects"
msgstr "Prosjekter"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_ids
msgid "Projects used in this sales order."
msgstr "Prosjekter brukt i denne salgsordren."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__product_uom_qty
msgid "Quantity"
msgstr "Antall"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__quantity_percentage
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "Quantity (%)"
msgstr ""

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Tilbudsmal-linje"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__reached_milestones_ids
msgid "Reached Milestones"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_display_name
msgid "Sale Line Display Name"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_count
msgid "Sale Order Count"
msgstr "Antall salgsordre"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_line_count
msgid "Sale Order Line Count"
msgstr ""

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sales"
msgstr "Salg"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Sales & Invoicing"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project_milestone.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#, python-format
msgid "Sales Order"
msgstr "Salgsordre"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order Item"
msgstr "Salgsordreelement"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__sale_line_id
msgid "Sales Order Item that will be updated once the milestone is reached."
msgstr ""

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added in order to be invoiced to your customer.\n"
"By default the sales order item set on the project will be selected. In the absence of one, the last prepaid sales order item that has time remaining will be used.\n"
"Remove the sales order item in order to make this task non billable. You can also change or remove the sales order item of each timesheet entry individually."
msgstr ""

#. module: sale_project
#. odoo-javascript
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sales Order Items"
msgstr ""

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "Salgsordrelinje"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Sales Orders"
msgstr "Salgsordre"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,help:sale_project.field_project_task__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr ""

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr ""

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_project_view_form_simplified_inherit
msgid "Select who to bill..."
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_policy
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__show_create_project_button
msgid "Show Create Project Button"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__show_project_button
msgid "Show Project Button"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__show_task_button
msgid "Show Task Button"
msgstr ""

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sold"
msgstr "Solgt"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "Oppgave"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "Task Created (%s): %s"
msgstr ""

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_count
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Tasks"
msgstr "Oppgaver"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Oppgaveanalyse"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_ids
msgid "Tasks associated to this sale"
msgstr "Oppgaver tilknyttet dette salget"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid "This Sales Order must contain at least one product of type \"Service\"."
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "This task has been created from: %s (%s)"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "To Do"
msgstr "Å gjøre"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "Å fakturere"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_type
msgid "Track Service"
msgstr "Spor tjeneste"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Track what you sold, delivered, and invoiced."
msgstr ""

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__product_uom
msgid "Unit of Measure"
msgstr "Enhet"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__vendor_bill_count
msgid "Vendor Bill Count"
msgstr "Leverandør regningskonto"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Vendor Bills"
msgstr "Leverandørfakturaer"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "View Sales Order"
msgstr ""

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You cannot link the order item %(order_id)s - %(product_id)s to this task "
"because it is a re-invoiced expense."
msgstr ""
