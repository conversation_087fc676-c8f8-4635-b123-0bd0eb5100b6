# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_project
# 
# Translators:
# Wil Odoo, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-06 18:38+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                  Nenhum marco foi encontrado. Vamos criar um?\n"
"                </p><p>\n"
"                  Acompanhe os principais pontos de progresso que devem ser alcançados para obter sucesso.\n"
"                </p>\n"
"            "

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "%(name)s's Sales Order Items"
msgstr "Itens do pedido de venda de %(name)s"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "%(name)s's Sales Orders"
msgstr "Pedidos de venda de %(name)s"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid ""
"<span class=\"fa fa-lg fa-building-o fa-fw\" title=\"Values set here are "
"company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o fa-fw\" title=\"Values set here are "
"company-specific.\" groups=\"base.group_multi_company\"/>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            <span class=\"o_stat_value\">0</span> Sales Order\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Make Billable\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            <span class=\"o_stat_value\">0</span> pedido de venda\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Tornar faturável\n"
"                        </span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Sales Orders\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Pedidos de venda\n"
"                        </span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "<span class=\"o_stat_text\">Sales Order</span>"
msgstr "<span class=\"o_stat_text\">Pedido de venda</span>"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
msgid "<span>)</span>"
msgstr "<span>)</span>"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"De acordo com a configuração do produto, a quantidade entregue pode ser calculada automaticamente pelo mecanismo:\n"
"- Manual: a quantidade é definida manualmente na linha\n"
"- Analítico das despesas: a quantidade é a soma da quantidade das despesas lançadas\n"
"- Planilha de horas: a quantidade é a soma das horas registradas nas tarefas vinculadas a essa linha de venda\n"
"- Movimentações de estoque: a quantidade vem de separações confirmadas\n"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Based on Delivered Quantity (Manual)"
msgstr "Com base na quantidade entregue (manual)"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Based on Milestones"
msgstr "Com base em marcos"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__allow_billable
#: model:ir.model.fields,field_description:sale_project.field_project_project__allow_billable
#: model:ir.model.fields,field_description:sale_project.field_project_task__allow_billable
msgid "Billable"
msgstr "Faturável"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "Canceled"
msgstr "Cancelado"

#. module: sale_project
#: model:ir.model,name:sale_project.model_res_config_settings
msgid "Config Settings"
msgstr "Configurações"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Cost of Goods Sold"
msgstr "Custo das mercadorias vendidas"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Cost of Goods Sold Items"
msgstr "Cost of Goods Sold Items"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Create Project"
msgstr "Criar projeto"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_tracking
msgid "Create on Order"
msgstr "Criar no pedido"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__project_partner_id
#: model:ir.model.fields,field_description:sale_project.field_project_project__partner_id
msgid "Customer"
msgstr "Cliente"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Customer Invoices"
msgstr "Faturas de clientes"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Delivered"
msgstr "Entregue"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__display_sale_order_button
msgid "Display Sales Order"
msgstr "Exibir pedido de venda"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__display_sales_stat_buttons
msgid "Display Sales Stat Buttons"
msgstr "Exibir botões de estatísticas de vendas"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__visible_project
msgid "Display project"
msgstr "Exibir projeto"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "Done"
msgstr "Concluído"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Down Payments"
msgstr "Entradas"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__project_id
msgid "Generated Project"
msgstr "Projeto gerado"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__task_id
msgid "Generated Task"
msgstr "Tarefa gerada"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_to_invoice
msgid "Has SO to Invoice"
msgstr "Tem um pedido de venda para faturar "

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__has_any_so_with_nothing_to_invoice
msgid "Has a SO with an invoice status of No"
msgstr "Tem um pedido de vendas com um status de fatura de Não"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "In Progress"
msgstr "Em andamento"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__invoice_count
msgid "Invoice Count"
msgstr "Contagem de faturas"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice ordered quantities as soon as this service is sold."
msgstr "Faturar as quantidades pedidas assim que esse serviço for vendido."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create a project"
" for the order with a task for each sales order line to track the time "
"spent."
msgstr ""
"Faturar as quantidades pedidas assim que esse serviço for vendido. Crie um "
"projeto para o pedido com uma tarefa para cada linha do pedido de vendas "
"para controlar o tempo gasto."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create a task in"
" an existing project to track the time spent."
msgstr ""
"Faturar as quantidades pedidas assim que esse serviço for vendido. Crie uma "
"tarefa em um projeto existente para controlar o tempo gasto."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice ordered quantities as soon as this service is sold. Create an empty "
"project for the order to track the time spent."
msgstr ""
"Faturar as quantidades pedidas assim que esse serviço for vendido. Crie um "
"projeto vazio para o pedido para monitorar o tempo gasto."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). "
msgstr ""
"Fature esse serviço quando ele for entregue (defina a quantidade manualmente"
" em suas linhas de pedido de venda)."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create a project for the order with a task for each "
"sales order line to track the time spent."
msgstr ""
"Fature esse serviço quando ele for entregue (defina a quantidade manualmente"
" nas linhas do pedido de venda). Crie um projeto para o pedido com uma "
"tarefa para cada linha do pedido de venda para controlar o tempo gasto."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create a task in an existing project to track the time "
"spent."
msgstr ""
"Fature esse serviço quando ele for entregue (defina a quantidade manualmente"
" nas linhas do pedido de venda). Crie uma tarefa em um projeto existente "
"para controlar o tempo gasto."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice this service when it is delivered (set the quantity by hand on your "
"sales order lines). Create an empty project for the order to track the time "
"spent."
msgstr ""
"Fature esse serviço quando ele for entregue (defina a quantidade manualmente"
" nas linhas do pedido de venda). Crie um projeto em branco para o pedido a "
"fim de rastrear o tempo gasto."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Invoice your milestones when they are reached."
msgstr "Fature os marcos quando eles forem alcançados."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create a project for the "
"order with a task for each sales order line to track the time spent."
msgstr ""
"Fature os marcos quando eles forem alcançados. Crie um projeto para o pedido"
" com uma tarefa para cada linha do pedido de vendas para monitorar o tempo "
"gasto."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create a task in an existing "
"project to track the time spent."
msgstr ""
"Fature os marcos quando eles forem alcançados. Crie uma tarefa em um projeto"
" existente para monitorar o tempo gasto."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"Invoice your milestones when they are reached. Create an empty project for "
"the order to track the time spent."
msgstr ""
"Fature os marcos quando eles forem alcançados. Crie um projeto vazio para o "
"pedido para monitorar o tempo gasto."

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_project_view_form_simplified_inherit
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Invoice your time and material to customers"
msgstr "Fature seu tempo e material para clientes"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Invoiced"
msgstr "Faturado"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Invoices"
msgstr "Faturas"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.product_template_form_view_invoice_policy_inherit_sale_project
msgid "Invoicing Policy"
msgstr "Política de faturamento"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__is_product_milestone
msgid "Is Product Milestone"
msgstr "É um marco do produto"

#. module: sale_project
#: model:ir.model,name:sale_project.model_account_move_line
msgid "Journal Item"
msgstr "Item do diário"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Load more"
msgstr "Carregar mais"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_type
#: model:ir.model.fields,help:sale_project.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Definir as quantidades manualmente em pedidos: faturas baseadas em quantidades manuais, sem a criação de uma conta analítica.\n"
"Planilha de horas em contratos: faturas baseadas nas horas registradas nas planilhas de horas relacionadas.\n"
"Criar uma tarefa e rastrear as horas: criar uma tarefa na validação do pedido e rastrear as horas trabalhadas."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Materials"
msgstr "Materiais"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Método para atualizar a quantidade entregue"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__milestone_count
msgid "Milestone Count"
msgstr "Total de marcos"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale_project.selection__sale_order_line__qty_delivered_method__milestones
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Milestones"
msgstr "Marcos"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "New Sales Order Item"
msgstr "Novo item do pedido de venda"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "No Sales Order"
msgstr "Nenhum pedido de venda"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
msgid "Non-billable"
msgstr "Não faturável"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.portal_tasks_list_inherit
msgid "Not Billed"
msgstr "Não faturado"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__no
msgid "Nothing"
msgstr "Nada"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_count
msgid "Number of Projects"
msgstr "Número de projetos"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_product_product__service_tracking
#: model:ir.model.fields,help:sale_project.field_product_template__service_tracking
msgid ""
"On Sales order confirmation, this product can generate a project and/or task.         From those, you can track the service you are selling.\n"
"         'In sale order's project': Will use the sale order's configured project if defined or fallback to         creating a new project based on the selected template."
msgstr ""
"Na confirmação do pedido de venda, este produto pode gerar um projeto e/ou tarefa. A partir destes, você pode rastrear o serviço que está vendendo.\n"
"'No projeto do pedido de venda': usará o projeto configurado do pedido de venda se definido ou alternativamente criará um novo projeto baseado no modelo selecionado."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Other Services"
msgstr "Outros serviços"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__quantity_percentage
msgid ""
"Percentage of the ordered quantity that will automatically be delivered once"
" the milestone is reached."
msgstr ""
"Porcentagem da quantidade pedida que será automaticamente entregue quando o "
"marco for alcançado."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Portal Sale Order"
msgstr "Pedido de venda adequado"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid "Prepaid/Fixed Price"
msgstr "Pré-pago/preço fixo"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_template
msgid "Product"
msgstr "Produto"

#. module: sale_project
#: model:ir.model,name:sale_project.model_product_product
msgid "Product Variant"
msgstr "Variante do produto"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_id
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_id
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__project_only
msgid "Project"
msgstr "Projeto"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_in_project
msgid "Project & Task"
msgstr "Projeto e tarefa"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_milestone
msgid "Project Milestone"
msgstr "Marco do projeto"

#. module: sale_project
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_type__milestones
msgid "Project Milestones"
msgstr "Marcos do projeto"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__project_template_id
#: model:ir.model.fields,field_description:sale_project.field_product_template__project_template_id
msgid "Project Template"
msgstr "Modelo de projeto"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__project_sale_order_id
msgid "Project's sale order"
msgstr "Pedido de venda do projeto"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#: model:ir.model.fields,field_description:sale_project.field_sale_order__project_ids
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
#, python-format
msgid "Projects"
msgstr "Projetos"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_ids
msgid "Projects used in this sales order."
msgstr "Projetos utilizados neste pedido de venda."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__product_uom_qty
msgid "Quantity"
msgstr "Quantidade"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__quantity_percentage
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "Quantity (%)"
msgstr "Quantidade (%)"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_template_line
msgid "Quotation Template Line"
msgstr "Linha do modelo de cotação"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order_line__reached_milestones_ids
msgid "Reached Milestones"
msgstr "Marcos alcançados"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_display_name
msgid "Sale Line Display Name"
msgstr "Nome de exibição da linha de venda"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_count
msgid "Sale Order Count"
msgstr "Contagem de pedidos de venda"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_line_count
msgid "Sale Order Line Count"
msgstr "Contagem de linhas de pedido de venda"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sales"
msgstr "Vendas"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.view_edit_project_inherit_form
msgid "Sales & Invoicing"
msgstr "Vendas e faturamento"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project_milestone.py:0
#: model:ir.model,name:sale_project.model_sale_order
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_order_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_order_id
#, python-format
msgid "Sales Order"
msgstr "Pedido de venda"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_project__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_project_task__sale_line_id
#: model:ir.model.fields,field_description:sale_project.field_report_project_task_user__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_project.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:sale_project.view_sale_project_inherit_form
#, python-format
msgid "Sales Order Item"
msgstr "Item do pedido de venda"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_milestone__sale_line_id
msgid "Sales Order Item that will be updated once the milestone is reached."
msgstr ""
"Item do pedido de venda que será atualizado quando a etapa for atingida."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_line_id
msgid ""
"Sales Order Item to which the time spent on this task will be added in order to be invoiced to your customer.\n"
"By default the sales order item set on the project will be selected. In the absence of one, the last prepaid sales order item that has time remaining will be used.\n"
"Remove the sales order item in order to make this task non billable. You can also change or remove the sales order item of each timesheet entry individually."
msgstr ""
"Item do pedido de venda ao qual o tempo gasto nessa tarefa será adicionado para ser faturado ao seu cliente.\n"
"Por padrão, o item do pedido de venda definido no projeto será selecionado. Na ausência de um, será usado o último item do pedido de venda pré-pago que tiver tempo restante.\n"
"Remova o item do pedido de venda para tornar essa tarefa não faturável. Você também pode alterar ou remover o item do pedido de venda de cada registro de planilha de horas individualmente."

#. module: sale_project
#. odoo-javascript
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sales Order Items"
msgstr "Itens do pedido de venda"

#. module: sale_project
#: model:ir.model,name:sale_project.model_sale_order_line
msgid "Sales Order Line"
msgstr "Linha do pedido de venda"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Sales Orders"
msgstr "Pedidos de venda"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""
"Item do pedido de venda que será selecionado por padrão nas tarefas e planilhas de horas desse projeto, exceto se o funcionário definido nas planilhas de horas estiver explicitamente vinculado a outro item do pedido de venda no projeto.\n"
"Ele pode ser modificado em cada tarefa e entrada de planilha de horas individualmente, se necessário."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_project__sale_order_id
#: model:ir.model.fields,help:sale_project.field_project_task__project_sale_order_id
msgid "Sales order to which the project is linked."
msgstr "Pedido de venda ao qual o projeto está relacionado."

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_project_task__sale_order_id
msgid "Sales order to which the task is linked."
msgstr "Pedidos de venda aos quais a tarefa está vinculada."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Invoice"
msgstr "Buscar nas faturas"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/controllers/portal.py:0
#, python-format
msgid "Search in Sales Order"
msgstr "Buscar no pedido de venda"

#. module: sale_project
#: model:ir.model.fields,help:sale_project.field_sale_order__project_id
msgid "Select a non billable project on which tasks can be created."
msgstr ""
"Selecione um projeto não faturável no qual as tarefas podem ser criadas."

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_project_view_form_simplified_inherit
msgid "Select who to bill..."
msgstr "Selecione a quem cobrar..."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_policy
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_policy
msgid "Service Invoicing Policy"
msgstr "Política de faturamento de serviço"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__show_create_project_button
msgid "Show Create Project Button"
msgstr "Exibir botão 'Criar projeto'"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__show_project_button
msgid "Show Project Button"
msgstr "Exibir botão de projeto"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__show_task_button
msgid "Show Task Button"
msgstr "Exibir botão de tarefa"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Sold"
msgstr "Vendido"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task
#: model:ir.model.fields.selection,name:sale_project.selection__product_template__service_tracking__task_global_project
msgid "Task"
msgstr "Tarefa"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "Task Created (%s): %s"
msgstr "Tarefa criada (%s): %s"

#. module: sale_project
#: model:ir.model,name:sale_project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Recorrência de tarefa"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_count
#: model_terms:ir.ui.view,arch_db:sale_project.view_order_form_inherit_sale_project
msgid "Tasks"
msgstr "Tarefas"

#. module: sale_project
#: model:ir.model,name:sale_project.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Análise de tarefa"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_sale_order__tasks_ids
msgid "Tasks associated to this sale"
msgstr "Tarefas associadas a esta venda"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a global project since it will generate a "
"project."
msgstr "O produto %s não deve ter um projeto global, pois gerará um projeto."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project nor a project template since it "
"will not generate project."
msgstr ""
"O produto %s não deve ter um projeto nem um modelo de projeto, pois não "
"gerará projeto."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/product.py:0
#, python-format
msgid ""
"The product %s should not have a project template since it will generate a "
"task in a global project."
msgstr ""
"O produto %s não deve ter um modelo de projeto, pois gerará uma tarefa em um"
" projeto global."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order.py:0
#, python-format
msgid "This Sales Order must contain at least one product of type \"Service\"."
msgstr ""
"Esse pedido de venda deve conter pelo menos um produto do tipo \"Serviço\"."

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "This task has been created from: %s (%s)"
msgstr " Essa tarefa foi criada a partir de: %s (%s)"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/sale_order_line.py:0
#, python-format
msgid "To Do"
msgstr "A fazer"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_task__task_to_invoice
msgid "To invoice"
msgstr "A faturar"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_product_product__service_type
#: model:ir.model.fields,field_description:sale_project.field_product_template__service_type
msgid "Track Service"
msgstr "Serviço de rastreamento"

#. module: sale_project
#. odoo-javascript
#: code:addons/sale_project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#, python-format
msgid "Track what you sold, delivered, and invoiced."
msgstr "Monitore o que é vendido, entregue e faturado."

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_milestone__product_uom
msgid "Unit of Measure"
msgstr "Unidade de medida"

#. module: sale_project
#: model:ir.model.fields,field_description:sale_project.field_project_project__vendor_bill_count
msgid "Vendor Bill Count"
msgstr "Contagem de faturas do fornecedor"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid "Vendor Bills"
msgstr "Faturas de fornecedor"

#. module: sale_project
#: model_terms:ir.ui.view,arch_db:sale_project.project_milestone_view_tree
msgid "View Sales Order"
msgstr "Exibir pedido de venda"

#. module: sale_project
#. odoo-python
#: code:addons/sale_project/models/project.py:0
#, python-format
msgid ""
"You cannot link the order item %(order_id)s - %(product_id)s to this task "
"because it is a re-invoiced expense."
msgstr ""
"Não é possível vincular o item do pedido %(order_id)s - %(product_id)s a "
"esta tarefa porque é uma despesa refaturada."
