<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="web.PropertyTags">
        <!-- Copy many2many tags style without duplicating all the CSS -->
        <div t-attf-class="o_field_property_tag o_field_widget o_field_many2many_tags d-flex {{props.readonly ? 'readonly' : ''}}">
            <TagsList tags="tagListItems"/>
            <div
                t-if="!props.readonly"
                class="o_field_property_dropdown_menu o_input_dropdown">
                <AutoComplete
                    id="props.id"
                    value="''"
                    sources="autocompleteSources"
                    onSelect.bind="({value}) => this.onOptionSelected(value)"
                    resetOnSelect="true"/>
                <span class="o_dropdown_button" />
            </div>
        </div>
    </t>
    <t t-name="web.PropertyTagsColorListPopover">
        <div class="o_tag_popover m-2">
            <ColorList
                colors="props.colors"
                forceExpanded="true"
                onColorSelected.bind="(id) => props.switchTagColor(id, props.tag)"/>
        </div>
    </t>
    <t t-name="web.PropertyTagsField">
        <PropertyTags t-props="propertyTagsProps"/>
    </t>
</templates>
