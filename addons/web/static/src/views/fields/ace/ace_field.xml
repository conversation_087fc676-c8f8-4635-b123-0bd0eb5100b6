<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="web.AceField">
        <!-- TODO check if some classes are useless -->
        <div class="o_field_widget oe_form_field o_ace_view_editor oe_ace_open">
            <CodeEditor
                value="state.initialValue"
                mode="mode"
                readonly="props.readonly"
                onBlur.bind="commitChanges"
                onChange.bind="handleChange"
                class="'ace-view-editor'"
                theme="theme"
                maxLines="200"
            />
        </div>
    </t>

</templates>
