# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_stock_picking_batch
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <g.geit<PERSON><PERSON>@dileanity.com>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Greek (https://app.transifex.com/odoo/teams/41243/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,help:delivery_stock_picking_batch.field_stock_picking_type__batch_max_weight
msgid ""
"A transfer will not be automatically added to batches that will exceed this weight if the transfer is added to it.\n"
"Leave this value as '0' if no weight limit."
msgstr ""
" Μια μεταφορά δεν θα προστεθεί αυτόματα σε παρτίδες που θα υπερβούν αυτό το βάρος αν προστεθεί σε αυτές.\n"
"Αφήστε την τιμή στο ‘0’ αν δεν υπάρχει όριο βάρους."

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,help:delivery_stock_picking_batch.field_stock_picking_type__batch_group_by_carrier
msgid "Automatically group batches by carriers"
msgstr "Αυτόματη ομαδοποίηση παρτίδων ανά μεταφορέα"

#. module: delivery_stock_picking_batch
#: model:ir.model,name:delivery_stock_picking_batch.model_stock_picking_batch
msgid "Batch Transfer"
msgstr "Ομαδική Μεταφορά"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,field_description:delivery_stock_picking_batch.field_stock_picking_type__batch_group_by_carrier
msgid "Carrier"
msgstr "Μεταφορέας"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,field_description:delivery_stock_picking_batch.field_stock_picking_type__batch_max_weight
msgid "Maximum weight per batch"
msgstr ""

#. module: delivery_stock_picking_batch
#: model:ir.model,name:delivery_stock_picking_batch.model_stock_picking_type
msgid "Picking Type"
msgstr "Τύπος Διαλογής"

#. module: delivery_stock_picking_batch
#: model:ir.model,name:delivery_stock_picking_batch.model_stock_picking
msgid "Transfer"
msgstr "Μεταφορά"

#. module: delivery_stock_picking_batch
#: model:ir.model.fields,field_description:delivery_stock_picking_batch.field_stock_picking_type__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Ετικέτα μονάδας μέτρησης βάρους"
