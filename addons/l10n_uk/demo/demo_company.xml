<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_uk" model="res.partner">
        <field name="name">UK Company</field>
        <field name="vat"/>
        <field name="street"/>
        <field name="city"/>
        <field name="country_id" ref="base.uk"/>
        <field name="state_id" ref="base.state_uk119"/>
        <field name="zip"/>
        <field name="phone">+32 010 12 34 56</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.ukexample.com</field>
    </record>

    <record id="demo_company_uk" model="res.company">
        <field name="name">UK Company</field>
        <field name="partner_id" ref="partner_demo_company_uk"/>
    </record>

    <record id="demo_bank_uk" model="res.partner.bank">
        <field name="acc_number">GB46BARC20038484747597</field>
        <field name="partner_id" ref="partner_demo_company_uk"/>
        <field name="company_id" ref="demo_company_uk"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_uk')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_uk.demo_company_uk'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>uk</value>
        <value model="res.company" eval="obj().env.ref('l10n_uk.demo_company_uk')"/>
    </function>
</odoo>
