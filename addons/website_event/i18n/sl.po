# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# J<PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-22 18:36+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_count
msgid "# Registrations"
msgstr "# Registracije"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "'. Showing results for '"
msgstr "'. Prikazani rezultati za '"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "(Ref:"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "(only"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
")\n"
"                                    <i class=\"fa fa-long-arrow-down d-block text-muted mx-3 my-2\" style=\"font-size: 1.5rem\"/>"
msgstr ""

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "<b>Drag and Drop</b> this snippet below the event title."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>End</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>Start</b>"
msgstr "<b>Začetek</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid ""
"<em>Write here a quote from one of your attendees. It gives confidence in "
"your events.</em>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "<i class=\"fa fa-ban me-2\"/>Sold Out"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<i class=\"fa fa-calendar me-2\"/>Add to Calendar"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-check me-2\"/>Registered"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.navbar
msgid ""
"<i class=\"fa fa-chevron-left me-2\"/>\n"
"            <span>Back to events</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to Google Calendar"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to iCal/Outlook"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> Dodaj v iCal/Outlook"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid ""
"<i class=\"fa fa-gear me-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event tickets\"/><em>Configure Tickets</em>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<i class=\"fa fa-map-marker fa-fw\" role=\"img\"/>Get the direction"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"<span class=\"badge text-bg-secondary text-uppercase "
"o_wevent_badge\">Speaker</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<span class=\"fa fa-plus me-1\"/> Create an Event"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_days pe-1\">0</span><span "
"class=\"o_countdown_metric pe-1\">days</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                        <i class=\"fa fa-ban me-2\"/>Sold Out\n"
"                                    </span>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr "<span>Spletni dogodki</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong> You ordered more tickets than available seats</strong>"
msgstr ""
"<strong> Naročili ste več vstopnic kot je razpoložljivih sedežev</strong>"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_2
msgid "A friend"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "A past event"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "About Us"
msgstr "O nas"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "About us"
msgstr "O nas"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "Vse države"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "All Events"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "All countries"
msgstr ""

#. module: website_event
#: model:event.question,title:website_event.event_0_question_1
msgid "Allergies"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_menu
msgid "Allows to display and manage event-specific menus on website."
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__name
msgid "Answer"
msgstr "Odgovor"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.action_event_registration_report
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_graph
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_pivot
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_tree
msgid "Answer Breakdown"
msgstr "Razčlenitev odgovorov"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__answer_ids
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "Answers"
msgstr "Odgovori"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__once_per_order
msgid "Ask once per order"
msgstr "Vprašajte enkrat na naročilo"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""
"Pri komaj 13 letih je Janez Novak že začel razvijati svoje prve poslovne "
"aplikacije za stranke. Po obvladovanju gradbeništva je ustanovil TinyERP. To"
" je bila prva faza OpenERP, ki je kasneje postal Odoo, najpogosteje "
"nameščena odprtokodna poslovna programska oprema na svetu."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__registration_answer_ids
msgid "Attendee Answers"
msgstr "Odgovori udeležencev"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__registration_answer_choice_ids
msgid "Attendee Selection Answers"
msgstr "Odgovori za izbor udeležencev"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendees"
msgstr "Udeleženci"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid "Author"
msgstr "Avtor"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_1
#: model:event.question.answer,name:website_event.event_5_question_0_answer_1
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_1
msgid "Blog Post"
msgstr "Članek na blogu"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__partner_id
msgid "Booked by"
msgstr "Rezerviral"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Business Workshops"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__can_publish
msgid "Can Publish"
msgstr "Lahko objavite"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Cancel"
msgstr "Prekliči"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Card design"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Close"
msgstr "Zaključi"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.action_event_registration_report
msgid "Come back once you have registrations to overview answers."
msgstr "Vrnite se, ko boste imeli prijave, da pregledate odgovore."

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_1
msgid "Commercials"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
#, python-format
msgid "Community"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu
#: model:ir.model.fields,field_description:website_event.field_event_type__community_menu
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__community
msgid "Community Menu"
msgstr ""

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__company_name
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "Company"
msgstr "Podjetje"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Conference For Architects"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid "Configure event tickets"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Confirm Registration"
msgstr "Potrdi registracijo"

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_0
msgid "Consumers"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Countries"
msgstr "Države"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__cover_properties
msgid "Cover Properties"
msgstr "Lastnosti naslovnice"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__create_uid
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__create_uid
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__create_uid
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_uid
msgid "Created by"
msgstr "Ustvaril"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__create_date
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__create_date
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__create_date
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_date
msgid "Created on"
msgstr "Ustvarjeno"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_time
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Date"
msgstr "Datum"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Date &amp; Time"
msgstr "Datum &amp; Čas"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Description"
msgstr "Opis"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__display_name
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__display_name
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__display_name
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_type__website_menu
msgid "Display a dedicated menu on Website"
msgstr "Na spletnem mestu prikažite poseben meni"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__community_menu
#: model:ir.model.fields,help:website_event.field_event_type__community_menu
msgid "Display community tab on website"
msgstr ""

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Don't forget to click <b>save</b> when you're done."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Download All Tickets"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Download Tickets <i class=\"ms-1 fa fa-download\"/>"
msgstr "Prenesi vstopnice <i class=\"ms-1 fa fa-download\"/>"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__email
#, python-format
msgid "Email"
msgstr "E-pošta"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "End -"
msgstr "Zaključek -"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/website_event.js:0
#, python-format
msgid "Error"
msgstr "Napaka"

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
#: model:ir.model.fields,field_description:website_event.field_event_question__event_id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__event_id
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__event_id
msgid "Event"
msgstr "Dogodek"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu_ids
msgid "Event Community Menus"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Event Date"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr "Lokacija dogodka"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_id
msgid "Event Menu"
msgstr "Meni dogodka"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "Event Name"
msgstr "Naziv dogodka"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Event Page"
msgstr ""

#. module: website_event
#: model:ir.actions.act_window,name:website_event.action_event_pages_list
msgid "Event Pages"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_question
msgid "Event Question"
msgstr "Vprašanje o dogodku"

#. module: website_event
#: model:ir.model,name:website_event.model_event_question_answer
msgid "Event Question Answer"
msgstr "Odgovor na vprašanje o dogodku"

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration
msgid "Event Registration"
msgstr "Registracija dogodka"

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration_answer
msgid "Event Registration Answer"
msgstr "Odgovor na prijavo na dogodek"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_ids
msgid "Event Registrations"
msgstr "Prijave na dogodke"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__subtitle
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Event Subtitle"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag
msgid "Event Tag"
msgstr "Oznaka dogodka"

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag_category
msgid "Event Tag Category"
msgstr "v"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_dynamic_snippet_options_template
msgid "Event Tags"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_event_type
msgid "Event Template"
msgstr "Predloga za dogodek"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Event Title"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__event_type_id
msgid "Event Type"
msgstr "Vrsta dogodka"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr "Dogodek ni najden!"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr "Dogodek objavljen"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr "Dogodek ne objavljen"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website.py:0
#: model:ir.ui.menu,name:website_event.menu_event_pages
#: model:website.menu,name:website_event.menu_events
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
#: model_terms:ir.ui.view,arch_db:website_event.snippets
#, python-format
msgid "Events"
msgstr "Dogodki"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Events Page"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Expired"
msgstr "Poteklo"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_register_cta
#: model:ir.model.fields,field_description:website_event.field_event_type__menu_register_cta
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Extra Register Button"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "Filter by Country"
msgstr "Filtriranje po državi"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_time
msgid "Filter by Date"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
msgid "Filters"
msgstr "Filtri"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"Find out what people see and say about this event, and join the "
"conversation."
msgstr ""
"Ugotovite, kaj ljudje vidijo in povedo o tem dogodku in se pridružite "
"pogovoru."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Follow Us"
msgstr "Sledi nam"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Following content will appear on all events."
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__general_question_ids
msgid "General Questions"
msgstr "Splošna vprašanja"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Google"
msgstr "Google"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Great Reno Ballon Race"
msgstr "Velika dirka balonov v Renu"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Grid"
msgstr "Mreža"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Hockey Tournament"
msgstr "Hokejski turnir"

#. module: website_event
#: model:event.question,title:website_event.event_7_question_1
msgid "How did you hear about us?"
msgstr ""

#. module: website_event
#: model:event.question,title:website_event.event_0_question_2
#: model:event.question,title:website_event.event_1_question_0
#: model:event.question,title:website_event.event_5_question_0
#: model:event.question,title:website_event.event_type_data_sports_question_0
msgid "How did you learn about this event?"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__id
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__id
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__id
msgid "ID"
msgstr "ID"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_question__once_per_order
msgid ""
"If True, this question will be asked only once and its value will be "
"propagated to every attendees.If not it will be asked for every attendee of "
"a reservation."
msgstr ""
"Če je vrednost Resnična, bo to vprašanje zastavljeno samo enkrat in njegova "
"vrednost bo posredovana vsem udeležencem. Če ni, bo zastavljeno vsem "
"udeležencem rezervacije."

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__introduction
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
#, python-format
msgid "Introduction"
msgstr "Uvodna predstavitev"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu
msgid "Introduction Menu"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu_ids
msgid "Introduction Menus"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_done
msgid "Is Done"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "Je v teku"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_participating
msgid "Is Participating"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__is_published
msgid "Is Published"
msgstr "Je objavljen"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "John DOE"
msgstr "John DOE"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__write_uid
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__write_uid
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__write_uid
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_uid
msgid "Last Updated by"
msgstr "Zadnji posodobil"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__write_date
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__write_date
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__write_date
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_date
msgid "Last Updated on"
msgstr "Zadnjič posodobljeno"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Layout"
msgstr "Postavitev"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "List"
msgstr "Seznam"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Live Music Festival"
msgstr "Festival žive glasbe"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__location
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
#, python-format
msgid "Location"
msgstr "Lokacija"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu
msgid "Location Menu"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu_ids
msgid "Location Menus"
msgstr ""

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Looking great! Let's now <b>publish</b> this page so that it becomes "
"<b>visible</b> on your website!"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Mandatory"
msgstr "Obvezno"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__is_mandatory_answer
msgid "Mandatory Answer"
msgstr "Obvezni odgovor"

#. module: website_event
#: model:event.question,title:website_event.event_0_question_0
msgid "Meal Type"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_id
msgid "Menu"
msgstr "Meni"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr ""

#. module: website_event
#: model:ir.actions.act_window,name:website_event.website_event_menu_action
msgid "Menus"
msgstr "Meniji"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_0
msgid "Mixed"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields,field_description:website_event.field_event_event__address_name
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__name
#, python-format
msgid "Name"
msgstr "Naziv"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_event_action_add
msgid "New Event"
msgstr "Nov dogodek"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website.py:0
#, python-format
msgid "Next Events"
msgstr "Prihodnji dogodki"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.action_event_registration_report
msgid "No Answers yet!"
msgstr "Še ni odgovorov!"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "No Website Menu Items yet!"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No event matching your search criteria could be found."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No events scheduled yet"
msgstr ""

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.event_registration_action_from_visitor
msgid "No registration linked to this visitor"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No results found for '"
msgstr "Ni rezultatov za \""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "Not Published"
msgstr "Ne objavljeno"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Once per Order"
msgstr "Enkrat na naročilo"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "Online Events"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "OpenWood Collection Online Reveal"
msgstr "Spletna predstavitev zbirke OpenWood"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "Organizator"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Our Trainings"
msgstr "Naša izobraževanja"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_0
msgid "Our website"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Past Events"
msgstr "Pretekli dogodki"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_2
msgid "Pastafarian"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__phone
#, python-format
msgid "Phone"
msgstr "Telefon"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Photos"
msgstr "Fotografije"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_search
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "Published"
msgstr "Objavljeno"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__question_id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__question_id
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "Question"
msgstr "Vprašanje"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__question_type
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__question_type
msgid "Question Type"
msgstr "Vrsta vprašanja"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
#, python-format
msgid "Question cannot be linked to both an Event and an Event Type."
msgstr "Vprašanja ni mogoče povezati z dogodkom in vrsto dogodka hkrati."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__question_ids
#: model:ir.model.fields,field_description:website_event.field_event_type__question_ids
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Questions"
msgstr "Vprašanja"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Quotes"
msgstr "Citati"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_2
#: model:event.question.answer,name:website_event.event_5_question_0_answer_2
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_2
msgid "Radio Ad"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Ref:"
msgstr ""

#. module: website_event
#. odoo-javascript
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: code:addons/website_event/static/src/js/register_toaster_widget.js:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__register
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.navbar
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
#, python-format
msgid "Register"
msgstr "Prijava"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Register Button"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu
msgid "Register Menu"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu_ids
msgid "Register Menus"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registered_ids
msgid "Registered Events"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__registration_id
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#, python-format
msgid "Registration"
msgstr "Registracija"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Registration confirmed!"
msgstr "Registracija potrjena!"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registration failed! These tickets are not available anymore."
msgstr ""

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_registration_action_from_visitor
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_form
msgid "Registrations"
msgstr "Registracije"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations Closed"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations are <b>closed</b>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations not yet open"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_remaining
msgid "Remaining before start"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_remaining
msgid "Remaining time before event starts (minutes)"
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_2
msgid "Research"
msgstr "Raziskave"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_id
#: model:ir.model.fields,help:website_event.field_event_tag__website_id
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_id
msgid "Restrict publishing to this website."
msgstr "Omejitev objav na to spletno stran."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr "Nazaj na seznam dogodkov."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO optimizirano"

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_1
msgid "Sales"
msgstr "Prodaja"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Sales end on"
msgstr "Prodaja se zaključi"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Sales start on"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.dynamic_filter_template_event_event_card
#: model_terms:ir.ui.view,arch_db:website_event.dynamic_filter_template_event_event_picture
msgid "Sample"
msgstr "Vzorec"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_search_box_input
msgid "Search an event..."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_search
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_tree
msgid "Selected Answers"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_tree
msgid "Selected answer"
msgstr "Izbrani odgovor"

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__simple_choice
msgid "Selection"
msgstr "Izbor"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__seo_name
msgid "Seo name"
msgstr "Seo ime"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__sequence
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__sequence
msgid "Sequence"
msgstr "Zaporedje"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Share"
msgstr "Souporaba"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_tree
msgid "Show on Website"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Sidebar"
msgstr "Stranska vrstica"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_0
#: model:event.question.answer,name:website_event.event_5_question_0_answer_0
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_0
msgid "Social Media"
msgstr "Socialni medij"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sold Out"
msgstr "Razprodano"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr "Iskani dogodek žal ni več na voljo."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__specific_question_ids
msgid "Specific Questions"
msgstr "Specifična vprašanja"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Start -"
msgstr "Začetek -"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_today
msgid "Start Today"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "Start → End"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
msgid "Starts <span/>"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Stats"
msgstr "Statistika"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Sub-menu (Specific)"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__value_answer_id
msgid "Suggested answer"
msgstr "Predlagani odgovor"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Google reCaptcha je zaznal sumljivo dejavnost."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Template badge"
msgstr ""

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__text_box
msgid "Text Input"
msgstr "Vnos besedila"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__value_text_box
msgid "Text answer"
msgstr "Besedilni odgovor"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_url
#: model:ir.model.fields,help:website_event.field_event_tag__website_url
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_url
msgid "The full URL to access the document through the website."
msgstr "Polna URL povezava za dostop do dokumenta preko spletne strani."

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "The website must be from the same company as the event."
msgstr ""

#. module: website_event
#: model:ir.model.constraint,message:website_event.constraint_event_registration_answer_value_check
msgid "There must be a suggested value or a text value."
msgstr "Obstajati mora predlagana vrednost ali besedilna vrednost."

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "This month"
msgstr "Ta mesec"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "This shortcut will bring you right back to the event form."
msgstr ""

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "This technical menu displays all event sub-menu items."
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "This ticket is not available for sale for this event"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket #"
msgstr "Vstopnica #"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Ticket Sales starting on"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Tickets"
msgstr "Zahtevki"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Tickets for this Event are <b>Sold Out</b>"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__title
msgid "Title"
msgstr "Naslov"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Today"
msgstr "Danes"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.navbar
msgid "Toggle navigation"
msgstr "Preklopi navigacijo"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Top Bar Filter"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_form
msgid "Type"
msgstr "Tip"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_visitor.py:0
#, python-format
msgid "Unsupported 'Not In' operation on visitors registrations"
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:website.snippet.filter,name:website_event.website_snippet_filter_event_list
#: model_terms:ir.ui.view,arch_db:website_event.event_time
#, python-format
msgid "Upcoming Events"
msgstr ""

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Use this <b>shortcut</b> to easily access your event web page."
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "Use this paragraph to write a short text about your events or company."
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_event_menu__view_id
msgid "Used when not being an url based menu"
msgstr ""

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_1
msgid "Vegetarian"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__view_id
msgid "View"
msgstr "Prikaz"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_published
msgid "Visible on current website"
msgstr "Vidno na trenutni spletni strani"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__visitor_id
msgid "Visitor"
msgstr "Obiskovalec"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "We couldn't find any event matching your search for:"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "We couldn't find any event scheduled at this moment."
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_website
#: model:ir.model.fields,field_description:website_event.field_event_event__website_id
#: model:ir.model.fields,field_description:website_event.field_event_tag__website_id
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_id
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
msgid "Website"
msgstr "Spletna stran"

#. module: website_event
#: model:ir.model,name:website_event.model_website_event_menu
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_form
msgid "Website Event Menu"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_search
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_tree
msgid "Website Event Menus"
msgstr ""

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr "Spletna domača stran"

#. module: website_event
#: model:ir.model,name:website_event.model_website_menu
#: model:ir.model.fields,field_description:website_event.field_event_event__website_menu
msgid "Website Menu"
msgstr "Meni spletne strani"

#. module: website_event
#: model:ir.ui.menu,name:website_event.menu_website_event_menu
msgid "Website Menus"
msgstr ""

#. module: website_event
#: model:ir.model,name:website_event.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Filtriranje gradnikov s spletne strani"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Website Submenu"
msgstr ""

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_url
msgid "Website URL"
msgstr "URL spletne strani"

#. module: website_event
#: model:ir.model,name:website_event.model_website_visitor
msgid "Website Visitor"
msgstr "Obiskovalec spletnega mesta"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_description
msgid "Website meta description"
msgstr "Meta opis spletne strani"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_keywords
msgid "Website meta keywords"
msgstr "Meta ključne besede spletne strani"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_title
msgid "Website meta title"
msgstr "Meta naziv spletne strani"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_og_img
msgid "Website opengraph image"
msgstr "Spletna opengraph slika"

#. module: website_event
#: model:event.question,title:website_event.event_5_question_1
msgid "What's your Hockey level?"
msgstr ""

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "Ali se je dogodek začel"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_today
msgid "Whether event is going to start today if still not ongoing"
msgstr ""

#. module: website_event
#: model:event.question,title:website_event.event_7_question_0
msgid "Which field are you working in"
msgstr ""

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"With the Edit button, you can <b>customize</b> the web page visitors will "
"see when registering."
msgstr ""

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
#, python-format
msgid ""
"You cannot change the question type of a question that already has answers!"
msgstr ""
"Vrste vprašanja, na katero že obstajajo odgovori, ne morete spremeniti!"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
#, python-format
msgid ""
"You cannot delete a question that has already been answered by attendees."
msgstr ""
"Vprašanja, na katerega so udeleženci že odgovorili, ne morete izbrisati."

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question_answer.py:0
#, python-format
msgid ""
"You cannot delete an answer that has already been selected by attendees."
msgstr "Odgovora, ki so ga udeleženci že izbrali, ne morete izbrisati."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "available)"
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "e.g. \"Conference for Architects\""
msgstr ""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "e.g. \"Do you have any diet restrictions?\""
msgstr "npr. \"Ali imate kakšne prehranske omejitve?\""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "iCal/Outlook"
msgstr ""
