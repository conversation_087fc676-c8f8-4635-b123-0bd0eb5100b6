# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_count
msgid "# Registrations"
msgstr "# Înscrieri"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "'. Showing results for '"
msgstr "'. Afișare rezultate pentru '"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "(Ref:"
msgstr "(Ref:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "(only"
msgstr "(numai"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
")\n"
"                                    <i class=\"fa fa-long-arrow-down d-block text-muted mx-3 my-2\" style=\"font-size: 1.5rem\"/>"
msgstr ""
")\n"
"                                    <i class=\"fa fa-long-arrow-down d-block text-muted mx-3 my-2\" style=\"font-size: 1.5rem\"/>"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "<b>Drag and Drop</b> this snippet below the event title."
msgstr "<b>Trageți și Plasați</b> acest fragment sub titlul evenimentului."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>End</b>"
msgstr "<b>Sfârșit</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<b>Start</b>"
msgstr "<b>Start</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid ""
"<em>Write here a quote from one of your attendees. It gives confidence in "
"your events.</em>"
msgstr ""
"<em>Scrieți aici un citat de la unul dintre participanții dvs. Oferă "
"încredere în evenimentele tale.</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "<i class=\"fa fa-ban me-2\"/>Sold Out"
msgstr "<i class=\"fa fa-ban me-2\"/>Vândut"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/>Nepublicat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<i class=\"fa fa-calendar me-2\"/>Add to Calendar"
msgstr "<i class=\"fa fa-calendar me-2\"/>Adăugați la calendar"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<i class=\"fa fa-check me-2\"/>Registered"
msgstr "<i class=\"fa fa-check me-2\"/>Înregistrat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.navbar
msgid ""
"<i class=\"fa fa-chevron-left me-2\"/>\n"
"            <span>Back to events</span>"
msgstr ""
"<i class=\"fa fa-chevron-left me-2\"/>\n"
"            <span>Înapoi la evenimente</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"
msgstr ""
"<i class=\"fa fa-facebook text-facebook\" aria-label=\"Facebook\" "
"title=\"Facebook\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to Google Calendar"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> Adăugare la Google Calendar"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "<i class=\"fa fa-fw fa-calendar\"/> Add to iCal/Outlook"
msgstr "<i class=\"fa fa-fw fa-calendar\"/> Adăugare la iCal/Outlook"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid ""
"<i class=\"fa fa-gear me-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event tickets\"/><em>Configure Tickets</em>"
msgstr ""
"<i class=\"fa fa-gear me-1\" role=\"img\" aria-label=\"Configure\" "
"title=\"Configure event tickets\"/><em>Configurați biletele</em>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "<i class=\"fa fa-map-marker fa-fw\" role=\"img\"/>Get the direction"
msgstr "<i class=\"fa fa-map-marker fa-fw\" role=\"img\"/>Obțineți direcția"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"
msgstr "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" title=\"Twitter\"/>"
msgstr ""
"<i class=\"fa fa-twitter text-twitter\" aria-label=\"Twitter\" "
"title=\"Twitter\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"<span class=\"badge text-bg-secondary text-uppercase "
"o_wevent_badge\">Speaker</span>"
msgstr ""
"<span class=\"badge text-bg-secondary text-uppercase "
"o_wevent_badge\">Vorbitor</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "<span class=\"fa fa-plus me-1\"/> Create an Event"
msgstr "<span class=\"fa fa-plus me-1\"/> Creați un eveniment"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_days pe-1\">0</span><span "
"class=\"o_countdown_metric pe-1\">days</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_days pe-1\">0</span><span "
"class=\"o_countdown_metric pe-1\">zile</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_hours\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_minutes\">00</span><span "
"class=\"o_countdown_metric\">:</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_widget
msgid ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"
msgstr ""
"<span class=\"o_countdown_remaining o_timer_seconds\">00</span><span "
"class=\"o_countdown_metric\"/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                        <i class=\"fa fa-ban me-2\"/>Sold Out\n"
"                                    </span>"
msgstr ""
"<span itemprop=\"availability\" content=\"http://schema.org/SoldOut\" class=\"text-danger\">\n"
"                                        <i class=\"fa fa-ban me-2\"/>Vândut\n"
"                                    </span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "<span>Online Events</span>"
msgstr "<span>Evenimente Online</span>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "<strong> You ordered more tickets than available seats</strong>"
msgstr ""
"<strong> Ai comandat mai multe bilete decât locuri disponibile</strong>"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_2
msgid "A friend"
msgstr "Un prieten"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "A past event"
msgstr "Un eveniment trecut"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "About Us"
msgstr "Despre noi"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "About us"
msgstr "Despre noi"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "Toate țările"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "All Events"
msgstr "Toate evenimentele"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "All countries"
msgstr "Toate țările"

#. module: website_event
#: model:event.question,title:website_event.event_0_question_1
msgid "Allergies"
msgstr "Alergii"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_menu
msgid "Allows to display and manage event-specific menus on website."
msgstr ""
"Permite afișarea și gestionarea meniurilor specifice evenimentului de pe "
"site"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__name
msgid "Answer"
msgstr "Raspuns"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.action_event_registration_report
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_graph
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_pivot
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_tree
msgid "Answer Breakdown"
msgstr "Răspuns la defalcare"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__answer_ids
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "Answers"
msgstr "Răspunsuri"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__once_per_order
msgid "Ask once per order"
msgstr "Solicitați o singură dată pe comandă"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid ""
"At just 13 years old, John DOE was already starting to develop his first "
"business applications for customers. After mastering civil engineering, he "
"founded TinyERP. This was the first phase of OpenERP which would later "
"became Odoo, the most installed open-source business software worldwide."
msgstr ""
"La doar 13 ani, John DOE începe deja să dezvolte primele sale aplicații de "
"afaceri pentru clienți. După stăpânirea ingineriei civile, a fondat TinyERP."
" Aceasta a fost prima fază a OpenERP, care va deveni ulterior Odoo, cel mai "
"instalat software de afaceri open-source la nivel mondial"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__registration_answer_ids
msgid "Attendee Answers"
msgstr "Răspunsuri ale participanților"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__registration_answer_choice_ids
msgid "Attendee Selection Answers"
msgstr "Selecție de răspunsuri ale participanților"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Attendees"
msgstr "Participanți"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_quotes
msgid "Author"
msgstr "Autor"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_1
#: model:event.question.answer,name:website_event.event_5_question_0_answer_1
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_1
msgid "Blog Post"
msgstr "Postare Blog"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__partner_id
msgid "Booked by"
msgstr "Rezervat de"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Business Workshops"
msgstr "Ateliere de afaceri"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag__can_publish
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__can_publish
msgid "Can Publish"
msgstr "Poate publica"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Cancel"
msgstr "Anulează"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Card design"
msgstr "Design card"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Close"
msgstr "Închide"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.action_event_registration_report
msgid "Come back once you have registrations to overview answers."
msgstr "Reveniți odată ce aveți înregistrări pentru a vedea răspunsurile."

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_1
msgid "Commercials"
msgstr "Reclame"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
#, python-format
msgid "Community"
msgstr "Comunitate"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu
#: model:ir.model.fields,field_description:website_event.field_event_type__community_menu
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__community
msgid "Community Menu"
msgstr "Meniu Comunitate"

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__company_name
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "Company"
msgstr "Companie"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Conference For Architects"
msgstr "Conferință pentru Arhitecți"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_configure_tickets_button
msgid "Configure event tickets"
msgstr "Configurați bilete pentru eveniment"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Confirm Registration"
msgstr "Confirmă înregistrarea"

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_0
msgid "Consumers"
msgstr "Consumatori"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Countries"
msgstr "Tari"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__cover_properties
msgid "Cover Properties"
msgstr "Proprietăți Copertă"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__create_uid
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__create_uid
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__create_uid
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__create_date
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__create_date
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__create_date
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_time
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Date"
msgstr "Dată"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Date &amp; Time"
msgstr "Data și ora"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Data (nou la vechi)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Data (vechi la nou)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Description"
msgstr "Descriere"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__display_name
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__display_name
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__display_name
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_type__website_menu
msgid "Display a dedicated menu on Website"
msgstr "Afișați un meniu dedicat pe site-ul web"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__community_menu
#: model:ir.model.fields,help:website_event.field_event_type__community_menu
msgid "Display community tab on website"
msgstr "Afișați tab-ul de comunitate pe site"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Don't forget to click <b>save</b> when you're done."
msgstr "Nu uitați să faceți clic pe<b>salvare</b> când ați terminat."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Download All Tickets"
msgstr "Descărcați toate biletele"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Download Tickets <i class=\"ms-1 fa fa-download\"/>"
msgstr "Descărcați biletele<i class=\"ms-1 fa fa-download\"/>"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__email
#, python-format
msgid "Email"
msgstr "Email"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "End -"
msgstr "Sfârșit -"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/website_event.js:0
#, python-format
msgid "Error"
msgstr "Eroare"

#. module: website_event
#: model:ir.model,name:website_event.model_event_event
#: model:ir.model.fields,field_description:website_event.field_event_question__event_id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__event_id
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__event_id
msgid "Event"
msgstr "Eveniment"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__community_menu_ids
msgid "Event Community Menus"
msgstr "Meniuri eveniment comunitate"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
msgid "Event Date"
msgstr "Data Evenimentului"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.template_location
msgid "Event Location"
msgstr "Locație eveniment"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_id
msgid "Event Menu"
msgstr "Meniu eveniment"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "Event Name"
msgstr "Nume eveniment"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Event Page"
msgstr "Pagină eveniment"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.action_event_pages_list
msgid "Event Pages"
msgstr "Pagini eveniment"

#. module: website_event
#: model:ir.model,name:website_event.model_event_question
msgid "Event Question"
msgstr "Întrebare Eveniment"

#. module: website_event
#: model:ir.model,name:website_event.model_event_question_answer
msgid "Event Question Answer"
msgstr "Răspuns la întrebarea evenimentului"

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration
msgid "Event Registration"
msgstr "Înregistrare eveniment"

#. module: website_event
#: model:ir.model,name:website_event.model_event_registration_answer
msgid "Event Registration Answer"
msgstr "Răspuns la înregistrarea evenimentului"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registration_ids
msgid "Event Registrations"
msgstr "Înregistrări eveniment"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__subtitle
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Event Subtitle"
msgstr "Subtitrare Eveniment"

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag
msgid "Event Tag"
msgstr "Etichetă Eveniment"

#. module: website_event
#: model:ir.model,name:website_event.model_event_tag_category
msgid "Event Tag Category"
msgstr "Categorie Etichetă Eveniment"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_dynamic_snippet_options_template
msgid "Event Tags"
msgstr "Etichete eveniment"

#. module: website_event
#: model:ir.model,name:website_event.model_event_type
msgid "Event Template"
msgstr "Șablon Eveniment"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Event Title"
msgstr "Titlu Eveniment"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__event_type_id
msgid "Event Type"
msgstr "Tipul Evenimentului"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Event not found!"
msgstr "Evenimentul nu a fost găsit!"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_published
#: model:mail.message.subtype,name:website_event.mt_event_published
msgid "Event published"
msgstr "Eveniment publicat"

#. module: website_event
#: model:mail.message.subtype,description:website_event.mt_event_unpublished
#: model:mail.message.subtype,name:website_event.mt_event_unpublished
msgid "Event unpublished"
msgstr "Eveniment nepublicat"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website.py:0
#: model:ir.ui.menu,name:website_event.menu_event_pages
#: model:website.menu,name:website_event.menu_events
#: model_terms:ir.ui.view,arch_db:website_event.event_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
#: model_terms:ir.ui.view,arch_db:website_event.snippets
#, python-format
msgid "Events"
msgstr "Evenimente"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Events Page"
msgstr "Pagină evenimente"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Expired"
msgstr "Expirat"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__menu_register_cta
#: model:ir.model.fields,field_description:website_event.field_event_type__menu_register_cta
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Extra Register Button"
msgstr "Buton pentru înregistrare suplimentară"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "Filter by Country"
msgstr "Filtrează după țară"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_time
msgid "Filter by Date"
msgstr "Filtrați după dată"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_topbar
msgid "Filters"
msgstr "Filtre"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid ""
"Find out what people see and say about this event, and join the "
"conversation."
msgstr ""
"Aflați ce văd și spun oamenii despre acest eveniment și participați la "
"conversație."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_follow_us
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Follow Us"
msgstr "Urmărește-ne"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.layout
msgid "Following content will appear on all events."
msgstr "Conținutul următor va apărea la toate evenimentele."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__general_question_ids
msgid "General Questions"
msgstr "Întrebări generale"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Google"
msgstr "Google"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Great Reno Ballon Race"
msgstr "Mare cursă de baloane Reno"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Grid"
msgstr "Grilă"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Hockey Tournament"
msgstr "Turneu de Hockey"

#. module: website_event
#: model:event.question,title:website_event.event_7_question_1
msgid "How did you hear about us?"
msgstr "Cum ați auzit de noi?"

#. module: website_event
#: model:event.question,title:website_event.event_0_question_2
#: model:event.question,title:website_event.event_1_question_0
#: model:event.question,title:website_event.event_5_question_0
#: model:event.question,title:website_event.event_type_data_sports_question_0
msgid "How did you learn about this event?"
msgstr "Cum ați aflat despre acest eveniment?"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__id
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__id
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__id
msgid "ID"
msgstr "ID"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_question__once_per_order
msgid ""
"If True, this question will be asked only once and its value will be "
"propagated to every attendees.If not it will be asked for every attendee of "
"a reservation."
msgstr ""
"Dacă este adevărat, această întrebare va fi adresată o singură dată, iar "
"valoarea acesteia va fi transmisă tuturor participanților. Dacă nu, va fi "
"adresată fiecărui participant la o rezervare."

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__introduction
#: model_terms:ir.ui.view,arch_db:website_event.template_intro
#, python-format
msgid "Introduction"
msgstr "Introducere"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu
msgid "Introduction Menu"
msgstr "Meniu Introducere"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__introduction_menu_ids
msgid "Introduction Menus"
msgstr "Meniuri Introducere"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_done
msgid "Is Done"
msgstr "Este efectuat"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_ongoing
msgid "Is Ongoing"
msgstr "Este în Curs de Desfășurare"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_participating
msgid "Is Participating"
msgstr "Este Participant"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag__is_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__is_published
msgid "Is Published"
msgstr "Este publicat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.s_speaker_bio
msgid "John DOE"
msgstr "John DOE"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__write_uid
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__write_uid
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__write_uid
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__write_date
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__write_date
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__write_date
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Layout"
msgstr "Aspect"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "List"
msgstr "Listă"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "Live Music Festival"
msgstr "Festivalul de muzică live"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__location
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
#, python-format
msgid "Location"
msgstr "Locatie"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu
msgid "Location Menu"
msgstr "Meniu locație"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__location_menu_ids
msgid "Location Menus"
msgstr "Meniuri locație"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"Looking great! Let's now <b>publish</b> this page so that it becomes "
"<b>visible</b> on your website!"
msgstr ""
"Arată grozav! Haideți acum să<b>publicăm</b> această pagină, astfel încât să"
" devină<b>vizibilă</b> pe site-ul dvs. web!"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Mandatory"
msgstr "Obligatoriu"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__is_mandatory_answer
msgid "Mandatory Answer"
msgstr "Răspuns obligatoriu"

#. module: website_event
#: model:event.question,title:website_event.event_0_question_0
msgid "Meal Type"
msgstr "Tip gustare"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_id
msgid "Menu"
msgstr "Meniu"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Tip Meniu"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.website_event_menu_action
msgid "Menus"
msgstr "Meniuri"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_0
msgid "Mixed"
msgstr "Mixt"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields,field_description:website_event.field_event_event__address_name
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__name
#, python-format
msgid "Name"
msgstr "Nume"

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_event_action_add
msgid "New Event"
msgstr "Eveniment nou"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website.py:0
#, python-format
msgid "Next Events"
msgstr "Evenimentele urmatoare"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.action_event_registration_report
msgid "No Answers yet!"
msgstr "Nu există răspunsuri încă!"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "No Website Menu Items yet!"
msgstr "Încă nu există articole în meniul site-ului!"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No event matching your search criteria could be found."
msgstr ""
"Nu a putut fi găsit niciun eveniment care să corespundă criteriilor dvs. de "
"căutare."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No events scheduled yet"
msgstr "Încă nu sunt programate evenimente"

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.event_registration_action_from_visitor
msgid "No registration linked to this visitor"
msgstr "Nicio înregistrare legată de acest vizitator"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "No results found for '"
msgstr "Nu sunt rezultate pentru '"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "Not Published"
msgstr "Nepublicat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Once per Order"
msgstr "O singură dată pe comandă"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_location
msgid "Online Events"
msgstr "Evenimente online"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/website_snippet_filter.py:0
#, python-format
msgid "OpenWood Collection Online Reveal"
msgstr "Colecția OpenWood Reveal Online"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Organizer"
msgstr "Organizator"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
msgid "Our Trainings"
msgstr "Instructajele Noastre"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_2_answer_0
msgid "Our website"
msgstr "Site-ul nostru web"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Past Events"
msgstr "Evenimente din trecut"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_2
msgid "Pastafarian"
msgstr "Pastafarian"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_type.py:0
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__phone
#, python-format
msgid "Phone"
msgstr "Telefon"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_photos
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Photos"
msgstr "Fotografii"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_search
#: model_terms:ir.ui.view,arch_db:website_event.event_pages_kanban_view
msgid "Published"
msgstr "Publicat"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__question_id
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__question_id
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "Question"
msgstr "Întrebare"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__question_type
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__question_type
msgid "Question Type"
msgstr "Tip Întrebare"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
#, python-format
msgid "Question cannot be linked to both an Event and an Event Type."
msgstr ""
"Întrebarea nu poate fi legată atât de un eveniment, cât și de un tip de "
"eveniment."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__question_ids
#: model:ir.model.fields,field_description:website_event.field_event_type__question_ids
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Questions"
msgstr "Întrebări"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Quotes"
msgstr "Cotații"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_2
#: model:event.question.answer,name:website_event.event_5_question_0_answer_2
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_2
msgid "Radio Ad"
msgstr "Anunț radio"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Ref:"
msgstr "Ref:"

#. module: website_event
#. odoo-javascript
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: code:addons/website_event/static/src/js/register_toaster_widget.js:0
#: model:ir.model.fields.selection,name:website_event.selection__website_event_menu__menu_type__register
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.navbar
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
#, python-format
msgid "Register"
msgstr "Înregistrați"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Register Button"
msgstr "Buton înregistrare"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu
msgid "Register Menu"
msgstr "Meniu înregistrare"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__register_menu_ids
msgid "Register Menus"
msgstr "Meniuri înregistrare"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_visitor__event_registered_ids
msgid "Registered Events"
msgstr "Evenimente înregistrare"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__registration_id
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#, python-format
msgid "Registration"
msgstr "Înregistrare"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_complete
msgid "Registration confirmed!"
msgstr "Înregistrare confirmată"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registration failed! These tickets are not available anymore."
msgstr "Înregistrarea a eșuat! Aceste bilete nu mai sunt disponibile."

#. module: website_event
#: model:ir.actions.act_window,name:website_event.event_registration_action_from_visitor
#: model_terms:ir.ui.view,arch_db:website_event.website_visitor_view_form
msgid "Registrations"
msgstr "Înregistrări"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations Closed"
msgstr "Înregistrări Închise"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations are <b>closed</b>"
msgstr "Înregistrările sunt <b>închise</b>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Registrations not yet open"
msgstr "Înregistrările nu sunt încă deschise"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_remaining
msgid "Remaining before start"
msgstr "Rămas înainte de începere"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_remaining
msgid "Remaining time before event starts (minutes)"
msgstr "Timpul rămas până la începerea evenimentului (minute)"

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_2
msgid "Research"
msgstr "Cercetare "

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_id
#: model:ir.model.fields,help:website_event.field_event_tag__website_id
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_id
msgid "Restrict publishing to this website."
msgstr "Limitați publicarea pe acest site web."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Return to the event list."
msgstr "Revenire la lista de evenimente."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__is_seo_optimized
msgid "SEO optimized"
msgstr "Optimizat SEO"

#. module: website_event
#: model:event.question.answer,name:website_event.event_7_question_0_answer_1
msgid "Sales"
msgstr "Vânzări"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Sales end on"
msgstr "Vânzările se termină în"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "Sales start on"
msgstr "Vânzările încep în"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.dynamic_filter_template_event_event_card
#: model_terms:ir.ui.view,arch_db:website_event.dynamic_filter_template_event_event_picture
msgid "Sample"
msgstr "Mostră"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_search_box_input
msgid "Search an event..."
msgstr "Căutați un eveniment..."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_search
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_tree
msgid "Selected Answers"
msgstr "Răspunsuri selectate"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_answer_view_tree
msgid "Selected answer"
msgstr "Răspuns selectat"

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__simple_choice
msgid "Selection"
msgstr "Selecție"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__seo_name
msgid "Seo name"
msgstr "Nume SEO"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__sequence
#: model:ir.model.fields,field_description:website_event.field_event_question_answer__sequence
msgid "Sequence"
msgstr "Secvență"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Share"
msgstr "Partajează"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_tree
msgid "Show on Website"
msgstr "Afișați pe site"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Sidebar"
msgstr "Bară laterală"

#. module: website_event
#: model:event.question.answer,name:website_event.event_1_question_0_answer_0
#: model:event.question.answer,name:website_event.event_5_question_0_answer_0
#: model:event.question.answer,name:website_event.event_type_data_sports_question_0_answer_0
msgid "Social Media"
msgstr "Social media"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Sold Out"
msgstr "Epuizat"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.404
msgid "Sorry, the requested event is not available anymore."
msgstr "Ne pare rău, evenimentul solicitat nu mai este disponibil."

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__specific_question_ids
msgid "Specific Questions"
msgstr "Întrebări specifice"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "Start -"
msgstr "Start -"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__start_today
msgid "Start Today"
msgstr "Începeți azi"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "Start → End"
msgstr "Început → Sfârșit"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.display_timer_alert_widget
msgid "Starts <span/>"
msgstr "Începe <span/>"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
msgid "Stats"
msgstr "Status"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Sub-menu (Specific)"
msgstr "Sub-meniu (Specific)"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__value_answer_id
msgid "Suggested answer"
msgstr "Răspuns sugerat"

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "Suspicious activity detected by Google reCaptcha."
msgstr "Activitate suspectă detectată de Google reCaptcha."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Template badge"
msgstr "Insignă șablon"

#. module: website_event
#: model:ir.model.fields.selection,name:website_event.selection__event_question__question_type__text_box
msgid "Text Input"
msgstr "Introducere text"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration_answer__value_text_box
msgid "Text answer"
msgstr "Răspuns text"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__website_url
#: model:ir.model.fields,help:website_event.field_event_tag__website_url
#: model:ir.model.fields,help:website_event.field_event_tag_category__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"URL-ul complet pentru accesarea documentului prin intermediul site-ului web."

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "The website must be from the same company as the event."
msgstr "Site-ul trebuie să fie al aceleiași companii ca și evenimentul."

#. module: website_event
#: model:ir.model.constraint,message:website_event.constraint_event_registration_answer_value_check
msgid "There must be a suggested value or a text value."
msgstr "Trebuie să existe o valoare sugerată sau o valoare text."

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "This month"
msgstr "Luna aceasta"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "This shortcut will bring you right back to the event form."
msgstr "Această comandă rapidă vă va aduce înapoi la formularul de eveniment."

#. module: website_event
#: model_terms:ir.actions.act_window,help:website_event.website_event_menu_action
msgid "This technical menu displays all event sub-menu items."
msgstr ""
"Acest meniu tehnic afișează toate elementele submeniului evenimentului."

#. module: website_event
#. odoo-python
#: code:addons/website_event/controllers/main.py:0
#, python-format
msgid "This ticket is not available for sale for this event"
msgstr "Acest bilet nu este disponibil pentru vânzare pentru acest eveniment"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_attendee_details
msgid "Ticket #"
msgstr "Bilet #"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Ticket Sales starting on"
msgstr "Vânzările de bilete încep în     "

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Tickets"
msgstr "Tichete"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.registration_template
msgid "Tickets for this Event are <b>Sold Out</b>"
msgstr "Biletele pentru acest eveniment sunt<b>Epuizate</b>"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_question__title
msgid "Title"
msgstr "Titlu"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#, python-format
msgid "Today"
msgstr "Astăzi"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.navbar
msgid "Toggle navigation"
msgstr "Comutare navigare"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.snippet_options
msgid "Top Bar Filter"
msgstr "Filtru pentru bara de sus"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_registration_view_form
msgid "Type"
msgstr "Tip"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_event.py:0
#: model:website.snippet.filter,name:website_event.website_snippet_filter_event_list
#: model_terms:ir.ui.view,arch_db:website_event.event_time
#, python-format
msgid "Upcoming Events"
msgstr "Evenimente Viitoare"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid "Use this <b>shortcut</b> to easily access your event web page."
msgstr ""
"Utilizați această <b>comandă rapidă </b> pentru a accesa cu ușurință pagina "
"web a evenimentului."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.index_sidebar_about_us
msgid "Use this paragraph to write a short text about your events or company."
msgstr ""
"Utilizați acest paragraf pentru a scrie un text scurt despre evenimentele "
"sau compania dumneavoastră."

#. module: website_event
#: model:ir.model.fields,help:website_event.field_website_event_menu__view_id
msgid "Used when not being an url based menu"
msgstr "Utilizat atunci când nu este un meniu bazat pe url"

#. module: website_event
#: model:event.question.answer,name:website_event.event_0_question_0_answer_1
msgid "Vegetarian"
msgstr "Vegetarian"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_website_event_menu__view_id
msgid "View"
msgstr "Afișare"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag__website_published
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_published
msgid "Visible on current website"
msgstr "Vizibil pe site-ul curent"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_registration__visitor_id
msgid "Visitor"
msgstr "Vizitator"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "We couldn't find any event matching your search for:"
msgstr ""
"Nu am putut găsi niciun eveniment care să corespundă căutării dvs. pentru:"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.events_list
msgid "We couldn't find any event scheduled at this moment."
msgstr "Nu am putut găsi niciun eveniment programat în acest moment."

#. module: website_event
#: model:ir.model,name:website_event.model_website
#: model:ir.model.fields,field_description:website_event.field_event_event__website_id
#: model:ir.model.fields,field_description:website_event.field_event_tag__website_id
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_id
#: model_terms:ir.ui.view,arch_db:website_event.event_tag_category_view_form
msgid "Website"
msgstr "Site web"

#. module: website_event
#: model:ir.model,name:website_event.model_website_event_menu
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_form
msgid "Website Event Menu"
msgstr "Meniul evenimentului site-ului web"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_search
#: model_terms:ir.ui.view,arch_db:website_event.website_event_menu_view_tree
msgid "Website Event Menus"
msgstr "Meniuri ale evenimentului de pe site"

#. module: website_event
#: model:ir.actions.act_url,name:website_event.action_open_website
msgid "Website Home"
msgstr "Pagina de pornire a site-ului"

#. module: website_event
#: model:ir.model,name:website_event.model_website_menu
#: model:ir.model.fields,field_description:website_event.field_event_event__website_menu
msgid "Website Menu"
msgstr "Meniu website"

#. module: website_event
#: model:ir.ui.menu,name:website_event.menu_website_event_menu
msgid "Website Menus"
msgstr "Meniuri site"

#. module: website_event
#: model:ir.model,name:website_event.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Filtru fragment de site"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_type_view_form
msgid "Website Submenu"
msgstr "Submeniu site"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag__website_url
#: model:ir.model.fields,field_description:website_event.field_event_tag_category__website_url
msgid "Website URL"
msgstr "URL website"

#. module: website_event
#: model:ir.model,name:website_event.model_website_visitor
msgid "Website Visitor"
msgstr "Vizitator al sitului"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_description
msgid "Website meta description"
msgstr "Website meta descriere"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_keywords
msgid "Website meta keywords"
msgstr "Website meta cuvinte cheie"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_title
msgid "Website meta title"
msgstr "Metatitlu website"

#. module: website_event
#: model:ir.model.fields,field_description:website_event.field_event_event__website_meta_og_img
msgid "Website opengraph image"
msgstr "Imagine grafică deschisă a site-ului web"

#. module: website_event
#: model:event.question,title:website_event.event_5_question_1
msgid "What's your Hockey level?"
msgstr "Care este nivelul dvs. de hochei?"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__is_ongoing
msgid "Whether event has begun"
msgstr "Dacă evenimentul a început"

#. module: website_event
#: model:ir.model.fields,help:website_event.field_event_event__start_today
msgid "Whether event is going to start today if still not ongoing"
msgstr "Dacă evenimentul va începe astăzi, dacă nu este încă în desfășurare"

#. module: website_event
#: model:event.question,title:website_event.event_7_question_0
msgid "Which field are you working in"
msgstr "În ce domeniu lucrați"

#. module: website_event
#. odoo-javascript
#: code:addons/website_event/static/src/js/tours/event_tour.js:0
#, python-format
msgid ""
"With the Edit button, you can <b>customize</b> the web page visitors will "
"see when registering."
msgstr ""
"Cu ajutorul butonului Editare, puteți <b>personaliza </b> pagina web pe care"
" vizitatorii o vor vedea la înregistrare."

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
#, python-format
msgid ""
"You cannot change the question type of a question that already has answers!"
msgstr ""
"Nu puteți schimba tipul de întrebare a unei întrebări care are deja "
"răspunsuri!"

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question.py:0
#, python-format
msgid ""
"You cannot delete a question that has already been answered by attendees."
msgstr "Nu puteți șterge o întrebare la care au răspuns deja participanții."

#. module: website_event
#. odoo-python
#: code:addons/website_event/models/event_question_answer.py:0
#, python-format
msgid ""
"You cannot delete an answer that has already been selected by attendees."
msgstr ""
"Nu puteți șterge un răspuns care a fost deja selectat de participanți."

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.modal_ticket_registration
msgid "available)"
msgstr "disponibil)"

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form_add
msgid "e.g. \"Conference for Architects\""
msgstr "de exemplu, \"Conferință pentru arhitecți\""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event.event_question_view_form
msgid "e.g. \"Do you have any diet restrictions?\""
msgstr "de exemplu, \"Aveți restricții alimentare?\""

#. module: website_event
#: model_terms:ir.ui.view,arch_db:website_event.event_description_full
msgid "iCal/Outlook"
msgstr "iCal/Outlook"
