# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_timesheet
#
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:48+0000\n"
"PO-Revision-Date: 2018-09-21 13:17+0000\n"
"Last-Translator: Bole <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"Language: bs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "%s Spent"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid "(%(sign)s%(hours)s:%(minutes)s remaining)"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid "(%s days remaining)"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "(incl."
msgstr ""

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid "<b class=\"tip_title\">Tip: Record your Timesheets faster</b>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<i class=\"fa fa-calendar me-1\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "<i class=\"fa fa-download\"/> Download"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "<i class=\"fa fa-print\"/> Print"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid ""
"<span attrs=\"{'invisible': [('has_timesheet', '=', False)]}\">\n"
"                        You cannot delete employees who have timesheets.\n"
"                        <span attrs=\"{'invisible': [('has_active_employee', '=', False)]}\">\n"
"                            You can either archive these employees or first delete all of their timesheets.\n"
"                        </span>\n"
"                        <span attrs=\"{'invisible': [('has_active_employee', '=', True)]}\" groups=\"hr_timesheet.group_hr_timesheet_approver\">\n"
"                            Please first delete all of their timesheets.\n"
"                        </span>\n"
"                    </span>\n"
"                    <span attrs=\"{'invisible': [('has_timesheet', '=', True)]}\">\n"
"                        Are you sure you want to delete these employees?\n"
"                    </span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "<span class=\"o_stat_text\">Timesheets</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"<span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', True)]}\">Hours Spent on Sub-tasks:</span>\n"
"                                <span class=\"text-nowrap\" attrs=\"{'invisible' : [('encode_uom_in_days', '=', False)]}\">Days Spent on Sub-tasks:</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Days)</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Hours)</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Date</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Description</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Employee</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Project</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Task</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Days Spent:</strong>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<strong>Duration: </strong>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Hours Spent:</strong>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "<strong>Progress:</strong>"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__analytic_account_active
msgid "Active Analytic Account"
msgstr ""

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_timesheet_manager
msgid "Administrator"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "All"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_all
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_all
msgid "All Timesheets"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__allocated_hours
msgid "Allocated Hours"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__planned_hours
msgid "Allocated Time"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__allow_timesheets
msgid "Allow timesheets"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__amount
msgid "Amount"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/__init__.py:0
#, python-format
msgid "Analysis"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__analytic_account_id
msgid "Analytic Account"
msgstr "Analitički konto"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
msgid "Analytic Entry"
msgstr "Stavka analitike"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analitička stavka"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__analytic_account_id
msgid ""
"Analytic account to which this project, its tasks and its timesheets are linked. \n"
"Track the costs and revenues of your project by setting this analytic account on your related documents (e.g. sales orders, invoices, purchase orders, vendor bills, expenses etc.).\n"
"This analytic account can be changed on each task individually if necessary.\n"
"An analytic account is required in order to use timesheets."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid ""
"Analyze the projects and tasks on which your employees spend their time.<br>\n"
"                Evaluate which part is billable and what costs it represents."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_allow
msgid "Approver Reminder"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Archive Employees"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_ids
msgid "Associated Timesheets"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_activity_analysis
msgid "By Employee"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_project
msgid "By Project"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_task
msgid "By Task"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_collaborator
msgid "Collaborators in project shared"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__company_id
msgid "Company"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.hr_timesheet_menu_configuration
msgid "Configuration"
msgstr "Konfiguracija"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_employee.py:0
#, python-format
msgid "Confirmation"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__create_uid
msgid "Created by"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__create_date
msgid "Created on"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__date
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search_base
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Date"
msgstr "Datum"

#. module: hr_timesheet
#: model:ir.model.fields.selection,name:hr_timesheet.selection__res_config_settings__timesheet_encode_method__days
msgid "Days / Half-Days"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Days Spent"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Days recorded on sub-tasks:"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__internal_project_id
msgid "Default project value for timesheet generated from time off type."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_account_analytic_line__employee_id
msgid "Define an 'hourly cost' on the employee to track the cost of their time."
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.server,name:hr_timesheet.unlink_employee_action
msgid "Delete"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Delete Employee"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__department_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search_base
msgid "Department"
msgstr "Odjeljenje"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
msgid "Describe your activity"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__name
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Description"
msgstr "Opis"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Discard"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__display_name
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__display_name
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__display_name
msgid "Display Name"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "Download"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Duration"
msgstr "Trajanje"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Effective Hours"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__employee_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search_base
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#, python-format
msgid "Employee"
msgstr "Zaposleni"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_hr_employee_delete_wizard
msgid "Employee Delete Wizard"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_user_allow
msgid "Employee Reminder"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/wizard/hr_employee_delete_wizard.py:0
#, python-format
msgid "Employee Termination"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__employee_ids
msgid "Employees"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/wizard/hr_employee_delete_wizard.py:0
#, python-format
msgid "Employees' Timesheets"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__encode_uom_in_days
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__encode_uom_in_days
msgid "Encode Uom In Days"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__timesheet_encode_method
msgid "Encoding Method"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__encoding_uom_id
msgid "Encoding Uom"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Generate timesheets for validated time off requests and public holidays"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search_base
msgid "Group By"
msgstr "Grupiši po"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__has_active_employee
msgid "Has Active Employee"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee__has_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__has_timesheet
msgid "Has Timesheet"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Hours"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields.selection,name:hr_timesheet.selection__res_config_settings__timesheet_encode_method__hours
msgid "Hours / Minutes"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__effective_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__effective_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__unit_amount
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Hours Spent"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Hours Spent on Sub-Tasks"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Hours recorded on sub-tasks:"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__id
msgid "ID"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
msgid "Initially Planned Hours"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#: model:project.task.type,name:hr_timesheet.internal_project_default_stage
#, python-format
msgid "Internal"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__internal_project_id
msgid "Internal Project"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "Invalid operator: %s"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "Invalid value: %s"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__is_encode_uom_days
msgid "Is Encode Uom Days"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__is_internal_project
msgid "Is Internal Project"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__job_title
msgid "Job Title"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__write_uid
msgid "Last Updated by"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__write_date
msgid "Last Updated on"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last month"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last week"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Last year"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_project_view_form_simplified_inherit_timesheet
msgid "Log time on tasks"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__manager_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__manager_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search_base
msgid "Manager"
msgstr "Upravitelj"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "Meeting"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_ui_menu
msgid "Menu"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.rating_rating_view_search_project_inherited
msgid "My Department's Ratings"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_update_view_search_inherit
msgid "My Department's Updates"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.rating_rating_view_search_project_inherited
msgid "My Team's Ratings"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_update_view_search_inherit
msgid "My Team's Updates"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_mine
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_user
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search_base
msgid "My Timesheets"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Newest"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
msgid "No activities found. Let's start a new one!"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid "No data yet!"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "None"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__remaining_hours
msgid "Number of allocated hours minus the number of hours spent."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Ok"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__overtime
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__overtime
msgid "Overtime"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__parent_task_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__parent_task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search_base
msgid "Parent Task"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__partner_id
msgid "Partner"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "Print"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Jedinica mjere proizvoda"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__progress
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__progress
#, python-format
msgid "Progress"
msgstr "Napredak"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_project
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__project_id
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__project_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__project_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search_base
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#, python-format
msgid "Project"
msgstr "Projekat"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid "Project Time Unit"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__is_project_overtime
msgid "Project in Overtime"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_project
msgid "Project's Timesheets"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__readonly_timesheet
msgid "Readonly Timesheet"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
msgid "Record a new activity"
msgstr ""

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid "Record your timesheets in an instant by pressing Shift + the corresponding hotkey to add 15min to your projects."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Remaining Days"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Remaining Days:"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__remaining_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__remaining_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Remaining Hours"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__remaining_hours_percentage
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__remaining_hours_percentage
msgid "Remaining Hours Percentage"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Remaining Hours:"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__remaining_hours
msgid "Remaining Invoiced Time"
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports
msgid "Reporting"
msgstr "Izvještavanje"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in All"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Description"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Employee"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Project"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Search in Task"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "See Timesheets"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid "See timesheet entries"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Send a periodical email reminder to timesheets approvers that still have timesheets to validate"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Send a periodical email reminder to timesheets users that still have timesheets to encode"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.hr_timesheet_config_settings_action
msgid "Settings"
msgstr "Postavke"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Sub-tasks Hours Spent"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_task
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__task_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search_base
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#, python-format
msgid "Task"
msgstr "Zadatak"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_task
msgid "Task's Timesheets"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "The Internal Project of a company should be in that company."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "There are no timesheets."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "These projects have some timesheet entries referencing them. Before removing these projects, you have to remove these timesheet entries."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid "These tasks have some timesheet entries referencing them. Before removing these tasks, you have to remove these timesheet entries."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This Quarter"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This month"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid "This operator %s is not supported in this search method."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "This project has some timesheet entries referencing it. Before removing this project, you have to remove these timesheet entries."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid "This task has some timesheet entries referencing it. Before removing this task, you have to remove these timesheet entries."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
#, python-format
msgid "This task must be part of a project because there are some timesheets linked to it."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This week"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid ""
"This will set the unit of measure used in projects and tasks.\n"
"If you use the timesheet linked to projects, don't forget to setup the right unit of measure in your employees."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "This year"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Encoding"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Off"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Time spent on the sub-tasks (and their own sub-tasks) of this task."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__total_hours_spent
msgid "Time spent on this task and its sub-tasks (and their own sub-tasks)."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_report_project_task_user__total_hours_spent
msgid "Time spent on this task, including its sub-tasks."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time unit used to record your timesheets"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search_base
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheet Activities"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet Costs"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__timesheet_encode_uom_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_report_search
msgid "Timesheet Report"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search_base
msgid "Timesheet by Date"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#: code:addons/hr_timesheet/models/project_task.py:0
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line_by_project
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_from_employee
#: model:ir.actions.report,name:hr_timesheet.timesheet_report
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_project
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_task
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_task_timesheets
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__allow_timesheets
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__timesheet_ids
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_time_tracking
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_root
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_layout
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_home_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_project_view_form_simplified_inherit_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_project_task_page
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_kanban_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#, python-format
msgid "Timesheets"
msgstr "Vremenski listovi"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Timesheets - %s"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
msgid "Timesheets 80%"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_project_filter_inherit_timesheet
msgid "Timesheets >100%"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_task
msgid "Timesheets Analysis"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Timesheets Control"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_report
msgid "Timesheets by Employee"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_project
msgid "Timesheets by Project"
msgstr ""

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_task
msgid "Timesheets by Task"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__allow_timesheets
msgid "Timesheets can be logged on this task."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Timesheets cannot be created on a private task."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Timesheets must be created on a project or a task with an active analytic account."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "Timesheets must be created with an active employee in the selected companies."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/wizard/hr_employee_delete_wizard.py:0
#, python-format
msgid "Timesheets of %(name)s"
msgstr ""

#. module: hr_timesheet
#: model:digest.tip,name:hr_timesheet.digest_tip_hr_timesheet_0
msgid "Tip: Record your Timesheets faster"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#, python-format
msgid "Today"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
msgid "Total"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Total Allocated Time"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Total Days"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__total_hours_spent
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__total_hours_spent
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Total Hours"
msgstr "Ukupni sati"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__total_timesheet_time
msgid "Total Timesheet Time"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__total_timesheet_time
msgid "Total number of time (in the proper UoM) recorded in the project, rounded to the unit."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "Total:"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
msgid "Track your working hours by projects every day and invoice this time to your customers."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "Training"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__display_name
msgid ""
"Use these keywords in the title to set new tasks:\n"
"\n"
"        30h Allocate 30 hours to the task\n"
"        #tags Set tags on the task\n"
"        @user Assign the task to a user\n"
"        ! Set the task a high priority\n"
"\n"
"        Make sure to use the right format and order e.g. Improve the configuration screen 5h #feature #v16 @Mitchell !"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__user_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__user_id
msgid "User"
msgstr "Korisnik"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_approver
msgid "User: all timesheets"
msgstr ""

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_user
msgid "User: own timesheets only"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_uom_uom__timesheet_widget
msgid "Widget"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "You cannot access timesheets that are not yours."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_employee.py:0
#, python-format
msgid "You cannot delete employees who have timesheets."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "You cannot log timesheets on this project since it is linked to an inactive analytic account. Please change this account, or reactivate the current one to timesheet on the project."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#, python-format
msgid "You cannot set an archived employee to the existing timesheets."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#, python-format
msgid "You cannot use timesheets without an analytic account."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "days"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_task
msgid "for"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_project_task_page
msgid "for the"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#, python-format
msgid "hours"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"on\n"
"                            <span class=\"fw-bold text-dark\"> Sub-tasks</span>)"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
msgid ""
"on\n"
"                        <span class=\"fw-bold text-dark\"> Sub-tasks</span>)"
msgstr ""
