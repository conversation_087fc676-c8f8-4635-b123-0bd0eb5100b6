# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:27+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr " ดึงข้อมูล"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
#, python-format
msgid ""
"<h3>Please make a payment to: </h3><ul><li>Bank: %s</li><li>Account Number: "
"%s</li><li>Account Holder: %s</li></ul>"
msgstr ""
"<h3>กรุณาชำระเงินที่: </h3><ul><li>ธนาคาร: %s</li><li>หมายเลขบัญชี: "
"%s</li><li>เจ้าของบัญชี: %s</li></ul>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> These properties are set to\n"
"                                match the behavior of providers and that of their integration with\n"
"                                Odoo regarding this payment method. Any change may result in errors\n"
"                                and should be tested on a test database first."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> คุณสมบัติเหล่านี้ได้รับการตั้งค่า\n"
"                              ให้ตรงกับพฤติกรรมของผู้ให้บริการและการบูรณาการกับ Odoo \n"
"                              เกี่ยวกับวิธีการชำระเงินนี้ การเปลี่ยนแปลงใดๆ อาจส่งผลให้เกิดข้อผิดพลาด \n"
"                              และควรได้รับการทดสอบกับฐานข้อมูลทดสอบก่อน"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_breadcrumb
msgid "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"
msgstr "<i class=\"fa fa-home\" role=\"รูปหน้า\" title=\"หน้าแรก\" aria-label=\"หน้าแรก\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<i class=\"fa fa-info-circle oe_inline\" invisible=\"support_partial_capture"
" != 'full_only'\" title=\"Some of the transactions you intend to capture can"
" only be captured in full. Handle the transactions individually to capture a"
" partial amount.\"/>"
msgstr ""
"<i class=\"fa fa-info-circle oe_inline\" invisible=\"support_partial_capture"
" != 'full_only'\" "
"title=\"ธุรกรรมบางรายการที่คุณตั้งใจจะบันทึกสามารถบันทึกได้ทั้งหมดเท่านั้น "
"จัดการธุรกรรมทีละรายการเพื่อบันทึกจำนวนเงินบางส่วน\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.token_form
msgid ""
"<i class=\"fa fa-trash\" title=\"Delete payment method\" data-bs-"
"toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-delay=\"0\"/>"
msgstr ""
"<i class=\"fa fa-trash\" title=\"ลบวิธีการชำระเงิน\" data-bs-"
"toggle=\"เคล็ดลับเครื่องมือ\" data-bs-placement=\"บน\" data-bs-delay=\"0\"/>"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_method
msgid "<i class=\"oi oi-arrow-right me-1\"></i> Configure a payment provider"
msgstr "<i class=\"oi oi-arrow-right me-1\"></i> กำหนดค่าผู้ให้บริการชำระเงิน"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                                            Enable Payment Methods"
msgstr ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                                            เปิดใช้งานวิธีการชำระเงิน"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.method_form
msgid "<small class=\"text-600\">Save my payment details</small>"
msgstr "<small class=\"text-600\">บันทึกรายละเอียดการชำระเงินของฉัน</small>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "<span class=\"o_stat_text text-danger\">Unpublished</span>"
msgstr "<span class=\"o_stat_text text-danger\">ไม่ได้เผยแพร่</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "<span class=\"o_stat_text text-success\">Published</span>"
msgstr "<span class=\"o_stat_text text-success\">เผยแพร่แล้ว</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Saved Payment Methods</span>"
msgstr "<span class=\"o_stat_text\">วิธีการชำระเงินที่บันทึกไว้</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_country_ids\">\n"
"                                All countries are supported.\n"
"                            </span>"
msgstr ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_country_ids\">\n"
"                                รองรับทุกประเทศ\n"
"                            </span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_currency_ids\">\n"
"                                All currencies are supported.\n"
"                            </span>"
msgstr ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_currency_ids\">\n"
"                                รองรับทุกสกุลเงิน\n"
"                            </span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.method_form
#: model_terms:ir.ui.view,arch_db:payment.token_form
msgid "<span><i class=\"fa fa-lock\"/> Secured by</span>"
msgstr "<span><i class=\"fa fa-lock\"/> รับความปลอดภัยจาก</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid ""
"<span><i class=\"oi oi-arrow-right\"/> How to configure your PayPal "
"account</span>"
msgstr ""
"<span><i class=\"oi oi-arrow-right\"/> วิธีกำหนดค่าบัญชี PayPal "
"ของคุณ</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
#: model_terms:ir.ui.view,arch_db:payment.payment_methods
msgid ""
"<strong>No suitable payment method could be found.</strong><br/>\n"
"                                If you believe that it is an error, please contact the website\n"
"                                administrator."
msgstr ""
"<strong>ไม่พบวิธีการชำระเงินที่เหมาะสม</strong><br/>\n"
"                                หากคุณเชื่อว่าเป็นข้อผิดพลาด โปรดติดต่อผู้ดูแลเว็บไซต์\n"
"                                "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a partial capture pending. Please wait a\n"
"                    moment for it to be processed. Check your payment provider configuration if\n"
"                    the capture is still pending after a few minutes."
msgstr ""
"<strong>คำเตือน!</strong> มีการจับภาพบางส่วนที่รอดำเนินการ กรุณารอสักครู่\n"
"                    เพื่อดำเนินการ ตรวจสอบการกำหนดค่าผู้ให้บริการชำระเงินของคุณว่าการจับภาพยังคง\n"
"                    ค้างอยู่หลังจากผ่านไปไม่กี่นาทีหรือไม่"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<strong>Warning!</strong> You can not capture a negative amount nor more\n"
"                    than"
msgstr ""
"<strong>คำเตือน!</strong> คุณไม่สามารถจับยอดติดลบได้ไม่เกิน\n"
"                    "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid ""
"<strong>Warning</strong> Creating a payment provider from the <em>CREATE</em> button is not supported.\n"
"                        Please use the <em>Duplicate</em> action instead."
msgstr ""
"<strong>คำเตือน</strong> ไม่รองรับการสร้างผู้ให้บริการชำระเงินจากปุ่ม<em>สร้าง</em> \n"
"                        โปรดใช้การดำเนินการ<em>ซ้ำ</em>แทน"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>Warning</strong> Make sure you are logged in as the\n"
"                                    correct partner before making this payment."
msgstr ""
"<strong>คำเตือน</strong> ตรวจสอบให้แน่ใจว่าคุณเข้าสู่ระบบใน\n"
"                                   ฐานะพาร์ทเนอร์ที่ถูกต้องก่อนที่จะทำการชำระเงินนี้"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>คำเตือน</strong>สกุลเงินหายไปหรือไม่ถูกต้อง"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> You must be logged in to pay."
msgstr "<strong>คำเตือน</strong> คุณต้องเข้าสู่ระบบเพื่อชำระเงิน"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A refund request of %(amount)s has been sent. The payment will be created "
"soon. Refund transaction reference: %(ref)s (%(provider_name)s)."
msgstr ""
"ส่งคำขอคืนเงินจำนวน %(amount)s แล้ว การชำระเงินจะถูกสร้างขึ้นเร็วๆ นี้ "
"การอ้างอิงธุรกรรมการคืนเงิน: %(ref)s (%(provider_name)s)"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
#, python-format
msgid "A token cannot be unarchived once it has been archived."
msgstr "โทเค็นไม่สามารถยกเลิกการเก็บถาวรได้เมื่อเก็บถาวรแล้ว"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated (%(provider_name)s)."
msgstr ""
"การทำธุรกรรมที่มีอ้างอิง %(ref)s ได้ถูกเริ่มต้นแล้ว (%(provider_name)s)"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated to save a new "
"payment method (%(provider_name)s)"
msgstr ""
"การทำธุรกรรมที่มีอ้างอิง %(ref)s "
"ได้ถูกเริ่มต้นขึ้นเพื่อบันทึกวิธีการชำระเงินใหม่ (%(provider_name)s)"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"A transaction with reference %(ref)s has been initiated using the payment "
"method %(token)s (%(provider_name)s)."
msgstr ""
"การทำธุรกรรมด้วยอ้างอิง %(ref)s ได้ถูกเริ่มต้นขึ้นโดยใช้วิธีการชำระเงิน "
"%(token)s (%(provider_name)s)"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
#, python-format
msgid "Account"
msgstr "บัญชี"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "หมายเลขบัญชี"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Activate"
msgstr "เปิดใช้งาน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__active
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Address"
msgstr "ที่อยู่"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__allow_express_checkout
msgid "Allow Express Checkout"
msgstr "อนุญาตให้ชำระเงินด่วน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__allow_tokenization
msgid "Allow Saving Payment Methods"
msgstr "อนุญาตให้บันทึกวิธีการชำระเงิน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__captured_amount
msgid "Already Captured"
msgstr "จับภาพแล้ว"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__voided_amount
msgid "Already Voided"
msgstr "ยกเลิกแล้ว"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_aps
msgid "Amazon Payment Services"
msgstr "บริการชำระเงินของ Amazon"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
#, python-format
msgid "Amount"
msgstr "จำนวน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount_max
msgid "Amount Max"
msgstr "จำนวนเงินสูงสุด"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__amount_to_capture
msgid "Amount To Capture"
msgstr "จำนวนเงินที่จะรับ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_status
msgid "An error occurred during the processing of your payment."
msgstr "เกิดข้อผิดพลาดระหว่างการประมวลผลการชำระเงินของคุณ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Apply"
msgstr "นำไปใช้"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_form_templates.xml:0
#, python-format
msgid "Are you sure you want to delete this payment method?"
msgstr "คุณแน่ใจหรือไม่ว่าต้องการลบวิธีการชำระเงินนี้"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"คุณแน่ใจหรือไม่ว่าต้องการยกเลิกธุรกรรมที่ได้รับอนุญาต? "
"การดำเนินการนี้ไม่สามารถยกเลิกได้"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_asiapay
msgid "Asiapay"
msgstr "Asiapay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__auth_msg
msgid "Authorize Message"
msgstr "อนุญาตข้อความ"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_authorize
msgid "Authorize.net"
msgstr "Authorize.net"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__authorized
msgid "Authorized"
msgstr "ได้รับอนุญาต"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__authorized_amount
msgid "Authorized Amount"
msgstr "จำนวนเงินที่ได้รับอนุญาต"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Availability"
msgstr "ความพร้อม"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_search
msgid "Available methods"
msgstr "วิธีการที่มีอยู่"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
#, python-format
msgid "Bank"
msgstr "ธนาคาร"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "ชื่อธนาคาร"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__brand_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Brands"
msgstr "แบรนด์"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_model_id
msgid "Callback Document Model"
msgstr "โมเดลเอกสารโทรกลับ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_is_done
msgid "Callback Done"
msgstr "โทรกลับเรียบร้อยแล้ว"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_hash
msgid "Callback Hash"
msgstr "แฮชโทรกลับ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_method
msgid "Callback Method"
msgstr "วิธีการโทรกลับ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__callback_res_id
msgid "Callback Record ID"
msgstr "ID บันทึกการโทรกลับ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Cancel"
msgstr "ยกเลิก"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__cancel
msgid "Canceled"
msgstr "ถูกยกเลิก"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__cancel_msg
msgid "Canceled Message"
msgstr "ข้อความที่ยกเลิก"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Cannot delete payment method"
msgstr "ไม่สามารถลบวิธีการชำระเงินได้"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Cannot save payment method"
msgstr "ไม่สามารถบันทึกวิธีการชำระเงินได้"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
#, python-format
msgid "Capture"
msgstr "การตัดวงเงิน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__capture_manually
msgid "Capture Amount Manually"
msgstr "การตัดจำนวนเงินด้วยตนเอง"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Capture Transaction"
msgstr "จับการทำธุรกรรม"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__capture_manually
msgid ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."
msgstr ""
"เก็บเงินจาก Odoo เมื่อการส่งมอบเสร็จสิ้น\n"
"ใช้สิ่งนี้หากคุณต้องการเรียกเก็บเงินจากบัตรของลูกค้าของคุณเฉพาะเมื่อ\n"
"คุณแน่ใจว่าคุณสามารถจัดส่งสินค้าให้พวกเขาได้เท่านั้น"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__child_transaction_ids
msgid "Child Transactions"
msgstr "ธุรกรรมย่อย"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Child transactions"
msgstr "ธุรกรรมย่อย"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Choose a payment method"
msgstr "เลือกวิธีการชำระเงิน"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Choose another method <i class=\"oi oi-arrow-down\"/>"
msgstr "เลือกวิธีอื่น <i class=\"oi oi-arrow-down\"/>"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "City"
msgstr "เมือง"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Close"
msgstr "ปิด"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__code
#: model:ir.model.fields,field_description:payment.field_payment_provider__code
msgid "Code"
msgstr "โค้ด"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__color
msgid "Color"
msgstr "สี"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__company_id
#: model:ir.model.fields,field_description:payment.field_payment_provider__company_id
#: model:ir.model.fields,field_description:payment.field_payment_token__company_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__company_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Company"
msgstr "บริษัท"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Confirm Deletion"
msgstr "ยืนยันการลบ"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__done
msgid "Confirmed"
msgstr "ยืนยันแล้ว"

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "ติดต่อ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_id
msgid "Corresponding Module"
msgstr "โมดูลที่สอดคล้องกัน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__available_country_ids
msgid "Countries"
msgstr "ประเทศ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Country"
msgstr "ประเทศ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__tokenize
msgid "Create Token"
msgstr "สร้างโทเค็น"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_provider
msgid "Create a new payment provider"
msgstr "สร้างผู้ให้บริการชำระเงินใหม่"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_method__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_method__create_date
#: model:ir.model.fields,field_description:payment.field_payment_provider__create_date
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Creating a transaction from an archived token is forbidden."
msgstr "การสร้างธุรกรรมจากโทเค็นที่เก็บถาวรเป็นสิ่งต้องห้าม"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Credentials"
msgstr "ข้อมูลประจำตัว"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__stripe
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr "บัตรเครดิตและเดบิต (ผ่าน Stripe)"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__available_currency_ids
msgid "Currencies"
msgstr "สกุลเงิน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_provider__main_currency_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "สกุลเงิน"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__manual
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "คำแนะนำการชำระเงินที่กำหนดเอง"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "ลูกค้า"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__sequence
msgid "Define the display order"
msgstr "กำหนดลำดับการแสดง"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_demo
msgid "Demo"
msgstr "สาธิต"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__disabled
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Disabled"
msgstr "ปิดใช้งาน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_method__display_name
#: model:ir.model.fields,field_description:payment.field_payment_provider__display_name
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__done_msg
msgid "Done Message"
msgstr "ข้อความเสร็จสิ้น"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__draft
msgid "Draft"
msgstr "ร่าง"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_email
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__paypal_email_account
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Email"
msgstr "อีเมล"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__enabled
msgid "Enabled"
msgstr "เปิดใช้งานแล้ว"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Enterprise"
msgstr "องค์กร"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__error
msgid "Error"
msgstr "ผิดพลาด"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Error: %s"
msgstr "ข้อผิดพลาด: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__express_checkout_form_view_id
msgid "Express Checkout Form Template"
msgstr "เทมเพลตแบบฟอร์มการชำระเงินด่วน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_express_checkout
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_express_checkout
msgid "Express Checkout Supported"
msgstr "รองรับการชำระเงินด่วน"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_express_checkout
msgid ""
"Express checkout allows customers to pay faster by using a payment method "
"that provides all required billing and shipping information, thus allowing "
"to skip the checkout process."
msgstr ""
"การชำระเงินด่วนช่วยให้ลูกค้าชำระเงินได้เร็วขึ้นโดยใช้วิธีการชำระเงินที่ให้ข้อมูลการเรียกเก็บเงินและการจัดส่งที่จำเป็นทั้งหมด"
" จึงสามารถข้ามขั้นตอนการชำระเงินได้"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_flutterwave
msgid "Flutterwave"
msgstr "Flutterwave"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__full_only
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_manual_capture__full_only
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__full_only
msgid "Full Only"
msgstr "เต็มรูปแบบเท่านั้น"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate Payment Link"
msgstr "สร้างลิงค์การชำระเงิน"

#. module: payment
#: model:ir.model,name:payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "สร้างลิงก์การชำระเงินสำหรับการขาย"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate and Copy Payment Link"
msgstr "สร้างและคัดลอกลิงค์การชำระเงิน"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Go to my Account <i class=\"oi oi-arrow-right ms-2\"/>"
msgstr "ไปที่บัญชีของฉัน <i class=\"oi oi-arrow-right ms-2\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: payment
#: model:ir.model,name:payment.model_ir_http
msgid "HTTP Routing"
msgstr "การกำหนด HTTP"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__has_draft_children
msgid "Has Draft Children"
msgstr "มีการร่างย่อย"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__has_remaining_amount
msgid "Has Remaining Amount"
msgstr "มีจำนวนคงเหลือ"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__is_post_processed
msgid "Has the payment been post-processed"
msgstr "มีการชำระเงินภายหลังการประมวลผลหรือไม่"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__pre_msg
msgid "Help Message"
msgstr "ข้อความช่วยเหลือ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_method__id
#: model:ir.model.fields,field_description:payment.field_payment_provider__id
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
msgid "ID"
msgstr "ไอดี"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "If the payment hasn't been confirmed you can contact us."
msgstr "หากการชำระเงินไม่ได้รับการยืนยัน คุณสามารถติดต่อเราได้"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__image
#: model:ir.model.fields,field_description:payment.field_payment_provider__image_128
msgid "Image"
msgstr "รูปภาพ"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""
"ในโหมดทดสอบ การชำระเงินปลอมจะถูกประมวลผลผ่านอินเทอร์เฟซการชำระเงินทดสอบ\n"
"แนะนำให้ใช้โหมดนี้เมื่อตั้งค่าผู้ให้บริการ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__inline_form_view_id
msgid "Inline Form Template"
msgstr "เทมเพลตฟอร์มอินไลน์"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Install"
msgstr "ติดตั้ง"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_state
msgid "Installation State"
msgstr "สถานะการติดตั้ง"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "Installed"
msgstr "ติดตั้งแล้ว"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Internal server error"
msgstr "เกิดข้อผิดพลาดจากเซิร์ฟเวอร์ภายใน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__is_amount_to_capture_valid
msgid "Is Amount To Capture Valid"
msgstr "เป็นจำนวนเงินที่จะตัดได้"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_post_processed
msgid "Is Post-processed"
msgstr "เป็นขั้นตอนหลังการประมวลผล"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__is_primary
msgid "Is Primary Payment Method"
msgstr "เป็นวิธีการชำระเงินหลัก"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_form_templates.xml:0
#, python-format
msgid "It is currently linked to the following documents:"
msgstr "ขณะนี้มีการเชื่อมโยงกับเอกสารดังต่อไปนี้:"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__landing_route
msgid "Landing Route"
msgstr "เส้นทางการลง"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "ภาษา"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__last_state_change
msgid "Last State Change Date"
msgstr "วันที่เปลี่ยนสถานะล่าสุด"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_method__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_method__write_date
#: model:ir.model.fields,field_description:payment.field_payment_provider__write_date
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: payment
#: model:onboarding.onboarding.step,button_text:payment.onboarding_onboarding_step_payment_provider
msgid "Let's do it"
msgstr "ลุยกันเลย"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"Making a request to the provider is not possible because the provider is "
"disabled."
msgstr "ไม่สามารถส่งคำขอไปยังผู้ให้บริการได้เนื่องจากผู้ให้บริการถูกปิดใช้งาน"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_my_home_payment
msgid "Manage your payment methods"
msgstr "จัดการวิธีการชำระเงินของคุณ"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__manual
msgid "Manual"
msgstr "ด้วยตัวเอง"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_manual_capture
msgid "Manual Capture Supported"
msgstr "รองรับการตัดวงเงินด้วยตนเอง"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__maximum_amount
msgid "Maximum Amount"
msgstr "จำนวนสูงสุด"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__available_amount
msgid "Maximum Capture Allowed"
msgstr "อนุญาตให้ตัดวงเงินสูงสุด"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_mercado_pago
msgid "Mercado Pago"
msgstr "Mercado Pago"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Message"
msgstr "ข้อความ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Messages"
msgstr "ข้อความ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__manual_name
msgid "Method"
msgstr "วิธีการ"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__name
#: model:ir.model.fields,field_description:payment.field_payment_provider__name
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
#: model_terms:ir.ui.view,arch_db:payment.payment_method_search
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Name"
msgstr "ชื่อ"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__code__none
msgid "No Provider Set"
msgstr "ไม่มีการตั้งค่าผู้ให้บริการ"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
#, python-format
msgid ""
"No manual payment method could be found for this company. Please create one "
"from the Payment Provider menu."
msgstr ""
"ไม่พบวิธีการชำระเงินด้วยตนเองสำหรับบริษัทนี้ "
"โปรดสร้างหนึ่งรายการจากเมนูผู้ให้บริการชำระเงิน"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_method
msgid "No payment methods found for your payment providers."
msgstr "ไม่พบวิธีการชำระเงินสำหรับผู้ให้บริการชำระเงินของคุณ"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
#, python-format
msgid "No token can be assigned to the public partner."
msgstr "ไม่สามารถกำหนดโทเค็นให้กับพาร์ทเนอร์สาธารณะได้"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_to_buy
msgid "Odoo Enterprise Module"
msgstr "Odoo สำหรับองค์กร"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__offline
msgid "Offline payment by token"
msgstr "ชำระเงินออฟไลน์ด้วยโทเค็น"

#. module: payment
#: model:ir.model,name:payment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "ขั้นตอนการเริ่มใช้งาน"

#. module: payment
#: model:onboarding.onboarding.step,step_image_alt:payment.onboarding_onboarding_step_payment_provider
msgid "Onboarding Step Image"
msgstr "รูปภาพขั้นตอนการเริ่มใช้งาน"

#. module: payment
#: model:onboarding.onboarding.step,title:payment.onboarding_onboarding_step_payment_provider
msgid "Online Payments"
msgstr "การชำระเงินออนไลน์"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_direct
msgid "Online direct payment"
msgstr "ชำระเงินโดยตรงออนไลน์"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_token
msgid "Online payment by token"
msgstr "ชำระเงินออนไลน์ด้วยโทเค็น"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_redirect
msgid "Online payment with redirection"
msgstr "การชำระเงินออนไลน์พร้อมการเปลี่ยนเส้นทาง"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
#, python-format
msgid "Only administrators can access this data."
msgstr "มีเพียงผู้ดูแลระบบเท่านั้นที่สามารถเข้าถึงข้อมูลนี้ได้"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only authorized transactions can be voided."
msgstr "เฉพาะธุรกรรมที่ได้รับอนุญาตเท่านั้นที่สามารถถือเป็นโมฆะได้"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Only confirmed transactions can be refunded."
msgstr "เฉพาะธุรกรรมที่ยืนยันแล้วเท่านั้นที่สามารถขอคืนเงินได้"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__operation
msgid "Operation"
msgstr "ปฏิบัติการ"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#, python-format
msgid "Operation not supported."
msgstr "ไม่รองรับการดำเนินการ"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__other
msgid "Other"
msgstr "อื่น ๆ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Other payment methods"
msgstr "วิธีการชำระเงินอื่น"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "โทเค็น PDT Identity"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__partial
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_manual_capture__partial
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__partial
msgid "Partial"
msgstr "บางส่วน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_id
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Partner"
msgstr "พาร์ทเนอร์"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr "ชื่อคู่ค้า"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Pay"
msgstr "จ่าย"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__paypal
#: model:payment.provider,name:payment.payment_provider_paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment
#: model:ir.model,name:payment.model_payment_capture_wizard
msgid "Payment Capture Wizard"
msgstr "ตัวช่วยสร้างการตัดวงเงินการชำระเงิน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_details
msgid "Payment Details"
msgstr "รายละเอียดการชำระเงิน"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Followup"
msgstr "ติดตามการชำระเงิน"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Form"
msgstr "แบบฟอร์มการชำระเงิน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "คำแนะนำการชำระเงิน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__link
msgid "Payment Link"
msgstr "ลิงค์การชำระเงิน"

#. module: payment
#: model:ir.model,name:payment.model_payment_method
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__payment_method
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_method_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_method_id
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Payment Method"
msgstr "วิธีการชำระเงิน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_method_code
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_method_code
msgid "Payment Method Code"
msgstr "รหัสวิธีการชำระเงิน"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model:ir.actions.act_window,name:payment.action_payment_method
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#, python-format
msgid "Payment Methods"
msgstr "วิธีการชำระเงิน"

#. module: payment
#: model:ir.model,name:payment.model_payment_provider
msgid "Payment Provider"
msgstr "ผู้ให้บริการชำระเงิน"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_provider
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_list
msgid "Payment Providers"
msgstr "ผู้ให้บริการชำระเงิน"

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__token_id
msgid "Payment Token"
msgstr "โทเค็นการชำระเงิน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Payment Token Count"
msgstr "จำนวนโทเค็นการชำระเงิน"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_token
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_list
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Payment Tokens"
msgstr "โทเค็นการชำระเงิน"

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "ธุรกรรมสำหรับการชำระเงิน"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.model.fields,field_description:payment.field_payment_token__transaction_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_list
msgid "Payment Transactions"
msgstr "ธุรกรรมการชำระเงิน"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction_linked_to_token
msgid "Payment Transactions Linked To Token"
msgstr "ธุรกรรมการชำระเงินที่เชื่อมโยงกับโทเค็น"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
#, python-format
msgid "Payment details saved on %(date)s"
msgstr "รายละเอียดการชำระเงินบันทึกไว้เมื่อ %(date)s"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_my_home_payment
msgid "Payment methods"
msgstr "วิธีการชำระเงิน"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Payment processing failed"
msgstr "การประมวลผลการชำระเงินล้มเหลว"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment provider"
msgstr "ผู้ให้บริการชำระเงิน"

#. module: payment
#: model:ir.model,name:payment.model_payment_provider_onboarding_wizard
msgid "Payment provider onboarding wizard"
msgstr "ตัวช่วยสร้างการเริ่มต้นใช้งานผู้ให้บริการการชำระเงิน"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
msgid "Payments"
msgstr "การชำระเงิน"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__pending
msgid "Pending"
msgstr "รอดำเนินการ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__pending_msg
msgid "Pending Message"
msgstr "ข้อความอยู่ระหว่างดำเนินการ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "โทรศัพท์"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#, python-format
msgid "Please make sure that %(payment_method)s is supported by %(provider)s."
msgstr ""
"โปรดตรวจสอบให้แน่ใจว่า %(payment_method)s ได้รับการรับรองโดย%(provider)s."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "Please set a positive amount."
msgstr "โปรดกำหนดจำนวนที่เป็นบวก"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "Please set an amount lower than %s."
msgstr "กรุณากำหนดจำนวนเงินให้ต่ำกว่า %s"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.company_mismatch_warning
msgid "Please switch to company"
msgstr "กรุณาเปลี่ยนบริษัท"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__primary_payment_method_id
msgid "Primary Payment Method"
msgstr "วิธีการชำระเงินหลัก"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr "ดำเนินการโดย"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Provider"
msgstr "ผู้ให้บริการ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_code
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_code
msgid "Provider Code"
msgstr "รหัสผู้ให้บริการ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_ref
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_reference
msgid "Provider Reference"
msgstr "การอ้างอิงผู้ให้บริการ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__provider_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Providers"
msgstr "ผู้ให้บริการ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__is_published
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Published"
msgstr "เผยแพร่แล้ว"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_razorpay
msgid "Razorpay"
msgstr "Razorpay"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid "Reason: %s"
msgstr "เหตุผล: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__redirect_form_view_id
msgid "Redirect Form Template"
msgstr "เทมเพลตแบบฟอร์มการเปลี่ยนเส้นทาง"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
#, python-format
msgid "Reference"
msgstr "การอ้างอิง"

#. module: payment
#: model:ir.model.constraint,message:payment.constraint_payment_transaction_reference_uniq
msgid "Reference must be unique!"
msgstr "การอ้างอิงต้องไม่ซ้ำกัน!"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__refund
#, python-format
msgid "Refund"
msgstr "คืนเงิน"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_refund
msgid ""
"Refund is a feature allowing to refund customers directly from the payment "
"in Odoo."
msgstr ""
"การคืนเงินเป็นฟีเจอร์ที่ช่วยให้คืนเงินให้กับลูกค้าได้โดยตรงจากการชำระเงินใน "
"Odoo"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Refunds"
msgstr "การคืนเงิน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__refunds_count
msgid "Refunds Count"
msgstr "จำนวนการคืนเงิน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_id
msgid "Related Document ID"
msgstr "รหัสเอกสารที่เกี่ยวข้อง"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_model
msgid "Related Document Model"
msgstr "รูปแบบเอกสารที่เกี่ยวข้อง"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__require_currency
msgid "Require Currency"
msgstr "ต้องใช้สกุลเงิน"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr "เดบิต"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Save"
msgstr "บันทึก"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Select countries. Leave empty to allow any."
msgstr "เลือกประเทศ เว้นว่างไว้เพื่ออนุญาตรายการใดก็ได้"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Select countries. Leave empty to make available everywhere."
msgstr "เลือกประเทศ เว้นว่างไว้เพื่อให้สามารถใช้ได้ทุกที่"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Select currencies. Leave empty not to restrict any."
msgstr "เลือกสกุลเงิน เว้นว่างไว้เพื่อไม่จำกัดสกุลเงินใดๆ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Select currencies. Leave empty to allow any."
msgstr "เลือกสกุลเงิน เว้นว่างไว้เพื่ออนุญาตรายการใดก็ได้"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr "วิธีการชำระเงินการเริ่มต้นใช้งานที่เลือก"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__sequence
#: model:ir.model.fields,field_description:payment.field_payment_provider__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_allow_express_checkout
msgid "Show Allow Express Checkout"
msgstr "แสดงอนุญาตการชำระเงินด่วน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_allow_tokenization
msgid "Show Allow Tokenization"
msgstr "แสดงการอนุญาตโทเค็น"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_auth_msg
msgid "Show Auth Msg"
msgstr "แสดงข้อความการยืนยันตัวตน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_cancel_msg
msgid "Show Cancel Msg"
msgstr "แสดงข้อความการยกเลิก"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_credentials_page
msgid "Show Credentials Page"
msgstr "แสดงหน้าข้อมูลประจำตัว"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_done_msg
msgid "Show Done Msg"
msgstr "แสดงข้อความเสร็จสิ้น"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_pending_msg
msgid "Show Pending Msg"
msgstr "แสดงข้อความที่รอดำเนินการ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__show_pre_msg
msgid "Show Pre Msg"
msgstr "แสดงข้อความก่อนหน้า"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_sips
msgid "Sips"
msgstr "Sips"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Skip"
msgstr "ข้าม"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_capture_wizard.py:0
#, python-format
msgid ""
"Some of the transactions you intend to capture can only be captured in full."
" Handle the transactions individually to capture a partial amount."
msgstr ""
"ธุรกรรมบางรายการที่คุณตั้งใจจะบันทึกสามารถบันทึกได้ทั้งหมดเท่านั้น "
"จัดการธุรกรรมทีละรายการเพื่อบันทึกจำนวนเงินบางส่วน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__source_transaction_id
msgid "Source Transaction"
msgstr "ธุรกรรมแหล่งที่มา"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__state
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_state_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "State"
msgstr "รัฐ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Status"
msgstr "สถานะ"

#. module: payment
#: model:onboarding.onboarding.step,done_text:payment.onboarding_onboarding_step_payment_provider
msgid "Step Completed!"
msgstr "ขั้นตอนเสร็จสมบูรณ์!"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__stripe
#: model:payment.provider,name:payment.payment_provider_stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__support_partial_capture
msgid "Support Partial Capture"
msgstr "รองรับการตัดวงเงินบางส่วน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__supported_country_ids
msgid "Supported Countries"
msgstr "ประเทศที่รองรับ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__supported_currency_ids
msgid "Supported Currencies"
msgstr "สกุลเงินที่รองรับ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__payment_method_ids
msgid "Supported Payment Methods"
msgstr "วิธีการชำระเงินที่รองรับ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Supported by"
msgstr "สนับสนุนโดย"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__test
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Test Mode"
msgstr "ทดสอบโหมด"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_status
msgid "Thank you!"
msgstr "ขอบคุณ"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "The access token is invalid."
msgstr "โทเค็นการเข้าถึงไม่ถูกต้อง"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_capture_wizard.py:0
#, python-format
msgid "The amount to capture must be positive and cannot be superior to %s."
msgstr "จำนวนที่จะตัดวงเงินต้องเป็นจำนวนบวกและไม่สามารถมากกว่า %s ได้"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__image
#: model:ir.model.fields,help:payment.field_payment_method__image_payment_form
msgid "The base image used for this payment method; in a 64x64 px format."
msgstr "รูปภาพพื้นฐานที่ใช้สำหรับวิธีการชำระเงินนี้ ในรูปแบบ 64x64 พิกเซล"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__brand_ids
msgid ""
"The brands of the payment methods that will be displayed on the payment "
"form."
msgstr "แบรนด์ของวิธีการชำระเงินที่จะแสดงในแบบฟอร์มการชำระเงิน"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__child_transaction_ids
msgid "The child transactions of the transaction."
msgstr "ธุรกรรมย่อยของธุรกรรม"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__payment_details
msgid "The clear part of the payment method's payment details."
msgstr "ส่วนที่ชัดเจนของรายละเอียดการชำระเงินของวิธีการชำระเงิน"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__color
msgid "The color of the card in kanban view"
msgstr "สีของการ์ดในมุมมองคัมบัง"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "The complementary information message about the state"
msgstr "ข้อความข้อมูลเสริมเกี่ยวกับสถานะ"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__available_country_ids
msgid ""
"The countries in which this payment provider is available. Leave blank to "
"make it available in all countries."
msgstr ""
"ประเทศที่ผู้ให้บริการชำระเงินรายนี้ให้บริการ "
"เว้นว่างไว้เพื่อให้สามารถใช้ได้ในทุกประเทศ"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__available_currency_ids
msgid ""
"The currencies available with this payment provider. Leave empty not to "
"restrict any."
msgstr ""
"สกุลเงินที่สามารถใช้ได้กับผู้ให้บริการชำระเงินรายนี้ "
"เว้นว่างไว้เพื่อไม่จำกัด"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#, python-format
msgid "The following fields must be filled: %s"
msgstr "ต้องกรอกข้อมูลในฟิลด์ต่อไปนี้: %s"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "The following kwargs are not whitelisted: %s"
msgstr "kwargs ต่อไปนี้ไม่อยู่ในรายการที่อนุญาตพิเศษ: %s"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "The internal reference of the transaction"
msgstr "การอ้างอิงภายในของธุรกรรม"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__supported_country_ids
msgid ""
"The list of countries in which this payment method can be used (if the "
"provider allows it). In other countries, this payment method is not "
"available to customers."
msgstr ""
"รายชื่อประเทศที่ใช้วิธีการชำระเงินนี้ได้ (หากผู้ให้บริการอนุญาต) "
"ในประเทศอื่น วิธีการชำระเงินนี้ไม่พร้อมให้บริการแก่ลูกค้า"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__supported_currency_ids
msgid ""
"The list of currencies for that are supported by this payment method (if the"
" provider allows it). When paying with another currency, this payment method"
" is not available to customers."
msgstr ""
"รายการสกุลเงินที่รองรับวิธีการชำระเงินนี้ (หากผู้ให้บริการอนุญาต) "
"เมื่อชำระเงินด้วยสกุลเงินอื่น ลูกค้าจะไม่สามารถชำระเงินด้วยวิธีดังกล่าวได้"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__provider_ids
msgid "The list of providers supporting this payment method."
msgstr "รายชื่อผู้ให้บริการที่รองรับวิธีการชำระเงินนี้"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__main_currency_id
msgid "The main currency of the company, used to display monetary fields."
msgstr "สกุลเงินหลักของบริษัท ใช้เพื่อแสดงฟิลด์การเงิน"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__maximum_amount
msgid ""
"The maximum payment amount that this payment provider is available for. "
"Leave blank to make it available for any payment amount."
msgstr ""
"จำนวนเงินการชำระเงินสูงสุดที่ผู้ให้บริการการชำระเงินรายนี้สามารถใช้ได้ "
"เว้นว่างไว้เพื่อให้ใช้ได้กับจำนวนเงินที่ชำระ"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__auth_msg
msgid "The message displayed if payment is authorized"
msgstr "ข้อความปรากฏขึ้นหากการชำระเงินได้รับการอนุมัติ"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__cancel_msg
msgid ""
"The message displayed if the order is canceled during the payment process"
msgstr "ข้อความที่แสดงขึ้นหากคำสั่งซื้อถูกยกเลิกในระหว่างขั้นตอนการชำระเงิน"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__done_msg
msgid ""
"The message displayed if the order is successfully done after the payment "
"process"
msgstr "ข้อความที่แสดงขึ้นหากคำสั่งซื้อเสร็จสิ้นหลังจากขั้นตอนการชำระเงิน"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__pending_msg
msgid "The message displayed if the order pending after the payment process"
msgstr ""
"ข้อความที่แสดงขึ้นหากคำสั่งซื้ออยู่ระหว่างดำเนินการหลังจากขั้นตอนการชำระเงิน"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__pre_msg
msgid "The message displayed to explain and help the payment process"
msgstr "ข้อความที่แสดงขึ้นเพื่ออธิบายและช่วยเหลือขั้นตอนการชำระเงิน"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid ""
"The payment should either be direct, with redirection, or made by a token."
msgstr ""
"การชำระเงินควรเป็นการชำระเงินโดยตรง หรือมีการเปลี่ยนเส้นทาง "
"หรือชำระด้วยโทเค็น"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__primary_payment_method_id
msgid ""
"The primary payment method of the current payment method, if the latter is a brand.\n"
"For example, \"Card\" is the primary payment method of the card brand \"VISA\"."
msgstr ""
"วิธีการชำระเงินหลักของวิธีการชำระเงินปัจจุบัน หากวิธีหลังเป็นแบรนด์\n"
"ตัวอย่างเช่น \"บัตร\" เป็นวิธีการชำระเงินหลักของแบรนด์บัตร \"VISA\""

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__provider_ref
msgid "The provider reference of the token of the transaction."
msgstr "การอ้างอิงผู้ให้บริการของโทเค็นของธุรกรรม"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__provider_reference
msgid "The provider reference of the transaction"
msgstr "การอ้างอิงผู้ให้บริการของธุรกรรม"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__image_payment_form
msgid "The resized image displayed on the payment form."
msgstr "รูปภาพปรับขนาดที่แสดงในแบบฟอร์มการชำระเงิน"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__landing_route
msgid "The route the user is redirected to after the transaction"
msgstr "เส้นทางที่ผู้ใช้ถูกเปลี่ยนเส้นทางไปหลังการทำธุรกรรม"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__source_transaction_id
msgid "The source transaction of the related child transactions"
msgstr "ธุรกรรมแหล่งที่มาของธุรกรรมรองที่เกี่ยวข้อง"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__code
#: model:ir.model.fields,help:payment.field_payment_token__payment_method_code
#: model:ir.model.fields,help:payment.field_payment_transaction__payment_method_code
msgid "The technical code of this payment method."
msgstr "รหัสทางเทคนิคของวิธีการชำระเงินนี้"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__code
#: model:ir.model.fields,help:payment.field_payment_token__provider_code
#: model:ir.model.fields,help:payment.field_payment_transaction__provider_code
msgid "The technical code of this payment provider."
msgstr "รหัสทางเทคนิคของผู้ให้บริการชำระเงินรายนี้"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__redirect_form_view_id
msgid ""
"The template rendering a form submitted to redirect the user when making a "
"payment"
msgstr "เทมเพลตแสดงแบบฟอร์มที่ส่งเพื่อเปลี่ยนเส้นทางผู้ใช้เมื่อชำระเงิน"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__express_checkout_form_view_id
msgid "The template rendering the express payment methods' form."
msgstr "เทมเพลตที่แสดงแบบฟอร์มวิธีการชำระเงินด่วน"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a direct payment"
msgstr "เทมเพลตที่แสดงแบบฟอร์มการชำระเงินแบบอินไลน์เมื่อชำระเงินโดยตรง"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__token_inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a payment by "
"token."
msgstr "เทมเพลตที่แสดงแบบฟอร์มการชำระเงินแบบอินไลน์เมื่อชำระเงินด้วยโทเค็น"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s encountered an error "
"(%(provider_name)s)."
msgstr ""
"ธุรกรรมที่มีการอ้างอิง %(ref)s สำหรับ %(amount)s พบข้อผิดพลาด "
"(%(provider_name)s)"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been authorized "
"(%(provider_name)s)."
msgstr ""
"ธุรกรรมที่มีการอ้างอิง %(ref)s สำหรับ %(amount)s ได้รับการอนุมัติแล้ว "
"(%(provider_name)s)"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been confirmed "
"(%(provider_name)s)."
msgstr ""
"ธุรกรรมที่มีการอ้างอิง %(ref)s สำหรับ %(amount)s ได้รับการยืนยันแล้ว "
"(%(provider_name)s)"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "There are no transactions to show"
msgstr "ไม่มีธุรกรรมที่จะแสดง"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_token
msgid "There is no token created yet."
msgstr "ยังไม่มีการสร้างโทเค็น"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
#, python-format
msgid "There is nothing to be paid."
msgstr "ไม่มีอะไรจะต้องชำระ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "There is nothing to pay."
msgstr "ไม่มีอะไรต้องชำระ"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#, python-format
msgid ""
"This action will also archive %s tokens that are registered with this "
"payment method. Archiving tokens is irreversible."
msgstr ""
"การดำเนินการนี้จะเก็บถาวรโทเค็น %s ที่ลงทะเบียนด้วยวิธีการชำระเงินนี้ด้วย "
"โทเค็นการเก็บถาวรไม่สามารถย้อนกลับได้"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#, python-format
msgid ""
"This action will also archive %s tokens that are registered with this "
"provider. Archiving tokens is irreversible."
msgstr ""
"การดำเนินการนี้จะเก็บถาวรโทเค็น %s ที่ลงทะเบียนกับผู้ให้บริการรายนี้ด้วย "
"โทเค็นการเก็บถาวรไม่สามารถย้อนกลับได้"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__allow_tokenization
msgid ""
"This controls whether customers can save their payment methods as payment tokens.\n"
"A payment token is an anonymous link to the payment method details saved in the\n"
"provider's database, allowing the customer to reuse it for a next purchase."
msgstr ""
"วิธีนี้จะควบคุมว่าลูกค้าสามารถบันทึกวิธีการชำระเงินเป็นโทเค็นการชำระเงินได้หรือไม่\n"
"โทเค็นการชำระเงินคือลิงก์ที่ไม่ระบุตัวตนไปยังรายละเอียดวิธีการชำระเงินที่บันทึกไว้ใน\n"
"ฐานข้อมูลของผู้ให้บริการทำให้ลูกค้าสามารถนำกลับมาใช้ซ้ำได้ในการซื้อครั้งถัดไป"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__allow_express_checkout
msgid ""
"This controls whether customers can use express payment methods. Express "
"checkout enables customers to pay with Google Pay and Apple Pay from which "
"address information is collected at payment."
msgstr ""
"วิธีนี้จะควบคุมว่าลูกค้าสามารถใช้วิธีการชำระเงินแบบด่วนได้หรือไม่ "
"การชำระเงินแบบด่วนช่วยให้ลูกค้าสามารถชำระเงินด้วย Google Pay และ Apple Pay "
"ซึ่งจะมีการรวบรวมข้อมูลที่อยู่เมื่อชำระเงิน"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid ""
"This partner has no email, which may cause issues with some payment providers.\n"
"                     Setting an email for this partner is advised."
msgstr ""
"พาร์ทเนอร์รายนี้ไม่มีอีเมล ซึ่งอาจทำให้เกิดปัญหากับผู้ให้บริการชำระเงินบางราย\n"
"                     แนะนำให้ตั้งค่าอีเมลสำหรับพาร์ทเนอร์รายนี้"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#, python-format
msgid ""
"This payment method needs a partner in crime; you should enable a payment "
"provider supporting this method first."
msgstr ""
"วิธีการชำระเงินนี้จำเป็นต้องมีพาร์ทเนอร์ "
"คุณควรเปิดใช้งานผู้ให้บริการการชำระเงินที่รองรับวิธีนี้ก่อน"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"This transaction has been confirmed following the processing of its partial "
"capture and partial void transactions (%(provider)s)."
msgstr ""
"ธุรกรรมนี้ได้รับการยืนยันหลังจากการประมวลผลการตัดวงเงินบางส่วนและธุรกรรมที่เป็นโมฆะบางส่วน"
" (%(provider)s)."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__token_inline_form_view_id
msgid "Token Inline Form Template"
msgstr "เทมเพลตฟอร์มโทเค็นอินไลน์"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_tokenization
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_tokenization
msgid "Tokenization Supported"
msgstr "โทเค็นที่รองรับ"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_tokenization
msgid ""
"Tokenization is the process of saving the payment details as a token that "
"can later be reused without having to enter the payment details again."
msgstr ""
"Tokenization "
"คือกระบวนการบันทึกรายละเอียดการชำระเงินเป็นโทเค็นที่สามารถนำมาใช้ซ้ำได้ในภายหลังโดยไม่ต้องป้อนรายละเอียดการชำระเงินอีกครั้ง"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__transaction_ids
msgid "Transaction"
msgstr "ธุรกรรม"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#, python-format
msgid ""
"Transaction authorization is not supported by the following payment "
"providers: %s"
msgstr ""
"การอนุมัติธุรกรรมไม่ได้รับการรองรับโดยผู้ให้บริการชำระเงินต่อไปนี้: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_refund
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_refund
msgid "Type of Refund Supported"
msgstr "ประเภทของการคืนเงินที่รองรับ"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "Unable to contact the server. Please wait."
msgstr "ไม่สามารถติดต่อเซิร์ฟเวอร์ได้ กรุณารอสักครู่"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Unpublished"
msgstr "ยกเลิกการเผยแพร่"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Upgrade"
msgstr "อัพเกรด"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__validation
msgid "Validation of the payment method"
msgstr "การตรวจสอบความถูกต้องของวิธีการชำระเงิน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__void_remaining_amount
msgid "Void Remaining Amount"
msgstr "จำนวนเงินคงเหลือเป็นโมฆะ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Void Transaction"
msgstr "ธุรกรรมที่เป็นโมฆะ"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#: code:addons/payment/models/payment_method.py:0
#: code:addons/payment/models/payment_provider.py:0
#, python-format
msgid "Warning"
msgstr "คำเตือน"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__warning_message
msgid "Warning Message"
msgstr "ข้อความเตือน"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
#, python-format
msgid "Warning!"
msgstr "คำเตือน!"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "We are not able to find your payment, but don't worry."
msgstr "เราไม่พบการชำระเงินของคุณ แต่ไม่ต้องกังวล"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/post_processing.js:0
#, python-format
msgid "We are processing your payment. Please wait."
msgstr "เรากำลังประมวลผลการชำระเงินของคุณ กรุณารอสักครู่"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__tokenize
msgid ""
"Whether a payment token should be created when post-processing the "
"transaction"
msgstr "เมื่อมีการประมวลผลธุรกรรมภายหลัง ควรสร้างโทเค็นการชำระเงินหรือไม่"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_capture_wizard__support_partial_capture
msgid ""
"Whether each of the transactions' provider supports the partial capture."
msgstr "ไม่ว่าผู้ให้บริการธุรกรรมแต่ละรายจะรองรับการตัดวงเงินบางส่วนหรือไม่"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__callback_is_done
msgid "Whether the callback has already been executed"
msgstr "ไม่ว่าการโทรกลับจะถูกดำเนินการแล้วก็ตาม"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__is_published
msgid ""
"Whether the provider is visible on the website or not. Tokens remain "
"functional but are only visible on manage forms."
msgstr ""
"ไม่ว่าผู้ให้บริการจะมองเห็นได้บนเว็บไซต์หรือไม่ก็ตาม โทเค็นยังคงใช้งานได้ "
"แต่จะมองเห็นได้เฉพาะในแบบฟอร์มการจัดการเท่านั้น"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_transfer
msgid "Wire Transfer"
msgstr "โอนเงิน"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_xendit
msgid "Xendit"
msgstr "Xendit"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot change the company of a payment provider with existing "
"transactions."
msgstr ""
"คุณไม่สามารถเปลี่ยนบริษัทของผู้ให้บริการชำระเงินด้วยธุรกรรมที่มีอยู่ได้"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#, python-format
msgid ""
"You cannot delete the payment provider %s; disable it or uninstall it "
"instead."
msgstr ""
"คุณไม่สามารถลบผู้ให้บริการชำระเงิน %s ได้ ปิดการใช้งานหรือถอนการติดตั้งแทน"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#, python-format
msgid "You cannot publish a disabled provider."
msgstr "คุณไม่สามารถเผยแพร่ผู้ให้บริการที่ถูกปิดใช้งานได้"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
#, python-format
msgid "You do not have access to this payment token."
msgstr "คุณไม่มีสิทธิ์เข้าถึงโทเค็นการชำระเงินนี้"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_post_processing.xml:0
#, python-format
msgid "You should receive an email confirming your payment in a few minutes."
msgstr "คุณควรได้รับอีเมลยืนยันการชำระเงินของคุณในอีกไม่กี่นาที"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,auth_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,auth_msg:payment.payment_provider_aps
#: model_terms:payment.provider,auth_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,auth_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,auth_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,auth_msg:payment.payment_provider_demo
#: model_terms:payment.provider,auth_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,auth_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,auth_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,auth_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,auth_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,auth_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,auth_msg:payment.payment_provider_sips
#: model_terms:payment.provider,auth_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,auth_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,auth_msg:payment.payment_provider_xendit
#, python-format
msgid "Your payment has been authorized."
msgstr "การชำระเงินของคุณได้รับการอนุมัติแล้ว"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_aps
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_demo
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_sips
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_xendit
#, python-format
msgid "Your payment has been cancelled."
msgstr "การชำระเงินของคุณถูกยกเลิก"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,pending_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,pending_msg:payment.payment_provider_aps
#: model_terms:payment.provider,pending_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,pending_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,pending_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,pending_msg:payment.payment_provider_demo
#: model_terms:payment.provider,pending_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,pending_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,pending_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,pending_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,pending_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,pending_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,pending_msg:payment.payment_provider_sips
#: model_terms:payment.provider,pending_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,pending_msg:payment.payment_provider_xendit
#, python-format
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "การชำระเงินของคุณได้รับการประมวลผลเรียบร้อยแล้ว แต่กำลังรอการอนุมัติ"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,done_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,done_msg:payment.payment_provider_aps
#: model_terms:payment.provider,done_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,done_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,done_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,done_msg:payment.payment_provider_demo
#: model_terms:payment.provider,done_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,done_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,done_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,done_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,done_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,done_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,done_msg:payment.payment_provider_sips
#: model_terms:payment.provider,done_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,done_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,done_msg:payment.payment_provider_xendit
#, python-format
msgid "Your payment has been successfully processed."
msgstr "การชำระเงินของคุณได้รับการประมวลผลเรียบร้อยแล้ว"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.transaction_status
msgid "Your payment has not been processed yet."
msgstr "การชำระเงินของคุณยังไม่ได้รับการประมวลผล"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Your payment methods"
msgstr ""

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "ZIP"
msgstr "รหัสไปรษณีย์"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
msgid "Zip"
msgstr "รหัสไปรษณีย์"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "payment method"
msgstr "วิธีการชำระเงิน"

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
msgid "payment: post-process transactions"
msgstr "การชำระเงิน: การทำธุรกรรมหลังการประมวลผล"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "provider"
msgstr "ผู้ให้บริการ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.company_mismatch_warning
msgid ""
"to make this\n"
"                    payment."
msgstr ""
"เพื่อทำการ\n"
"                    ชำระเงินนี้"
