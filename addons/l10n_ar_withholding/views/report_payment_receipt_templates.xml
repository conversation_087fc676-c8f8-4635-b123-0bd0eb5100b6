<odoo>
    <template inherit_id="account.report_payment_receipt_document" id="report_payment_receipt_document">
        <xpath expr="//table" position="before">
            <t t-if="o.l10n_ar_withholding_ids">
                <table id="l10n_ar_withholding"  class="table table-sm">
                        <thead>
                            <tr>
                                <th><span>Tax</span></th>
                                <th><span>Withholding number</span></th>
                                <th><span>Base</span></th>
                                <th><span>Amount</span></th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-foreach="o.l10n_ar_withholding_ids" t-as="line">
                                <t t-set="withholding_base" t-value="o.line_ids.filtered(lambda x: line.tax_line_id.id in x.tax_ids.ids)"/>
                                <tr>
                                    <td>
                                        <span t-field='line.tax_line_id.name'/>
                                    </td>
                                    <td>
                                        <span t-field='line.name'/>
                                    </td>
                                    <td class="text-end">
                                        <span t-out="abs(withholding_base.amount_currency)" t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                                    </td>
                                    <td class="text-end">
                                        <span t-out="abs(line.amount_currency)" t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                                    </td>
                                </tr>
                            </t>
                        </tbody>
                </table>
            </t>
        </xpath>
    </template>
</odoo>
