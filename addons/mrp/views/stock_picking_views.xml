<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_production_type_kanban" model="ir.ui.view">
        <field name="name">stock.picking.type.kanban</field>
        <field name="model">stock.picking.type</field>
        <field name="inherit_id" ref="stock.stock_picking_type_kanban"/>
        <field name="arch" type="xml">
            <field name="code" position="after">
                <field name="count_mo_todo"/>
                <field name="count_mo_waiting"/>
                <field name="count_mo_late"/>
            </field>

            <xpath expr="//t[@t-name='kanban-menu']" position="inside">
                <div class="container" t-if="record.code.raw_value == 'mrp_operation'">
                    <div class="row">
                        <div class="col-6 o_kanban_card_manage_section o_kanban_manage_view" name="picking_left_manage_pane">
                            <h5 role="menuitem" class="o_kanban_card_manage_title">
                                <span>Orders</span>
                            </h5>
                            <div role="menuitem">
                                <a name="%(mrp_production_action_picking_deshboard)d" type="action">All</a>
                            </div>
                            <div role="menuitem">
                                <a name="%(mrp_production_action_picking_deshboard)d" type="action" context="{'search_default_inprogress': 1}">In Progress</a>
                            </div>
                            <div role="menuitem">
                                <a name="%(mrp_production_action_picking_deshboard)d" type="action" context="{'search_default_planned': 1}">Planned</a>
                            </div>
                        </div>
                        <div class="col-6 o_kanban_card_manage_section o_kanban_manage_new">
                            <h5 role="menuitem" class="o_kanban_card_manage_title">
                                <span>New</span>
                            </h5>
                            <div role="menuitem">
                                <a name="%(action_mrp_production_form)d" context="{'default_picking_type_id': id}" type="action">Manufacturing Order</a>
                            </div>
                        </div>
                    </div>

                    <div t-if="widget.editable" class="o_kanban_card_manage_settings row">
                        <div role="menuitem" aria-haspopup="true" class="col-8">
                            <ul role="menu" class="oe_kanban_colorpicker" data-field="color"/>
                        </div>
                        <div class="col-4">
                            <a class="dropdown-item" role="menuitem" type="edit">Configuration</a>
                        </div>
                    </div>
                </div>
            </xpath>
            <xpath expr='//div[@name="stock_picking"]' position="after">
                <div t-if="record.code.raw_value == 'mrp_operation'" t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''}">
                    <div>
                        <div t-attf-class="o_kanban_card_header">
                            <div class="o_kanban_card_header_title">
                                <div class="o_primary" t-if="!selection_mode">
                                    <a type="object" name="get_mrp_stock_picking_action_picking_type">
                                        <field name="name"/>
                                    </a>
                                </div>
                                <span class="o_primary" t-if="selection_mode"><field name="name"/></span>
                                <div class="o_secondary"><field class="o_secondary"  name="warehouse_id" readonly="1" groups="stock.group_stock_multi_warehouses"/></div>
                            </div>
                        </div>
                        <div class="container o_kanban_card_content" t-if="!selection_mode">
                            <div class="row">
                                <div class="col-6 o_kanban_primary_left">
                                    <button class="btn btn-primary" name="%(mrp_production_action_picking_deshboard)d" type="action" context="{'search_default_todo': 1, 'default_picking_type_id': id}">
                                        <span t-if="record.code.raw_value =='mrp_operation'"><t t-esc="record.count_mo_todo.value"/> To Process</span>
                                    </button>
                                </div>
                                <div class="col-6 o_kanban_primary_right">
                                    <div t-if="record.count_mo_waiting.raw_value > 0" class="row">
                                        <div class="col-9">
                                            <a name="%(mrp_production_action_picking_deshboard)d" type="action" context="{'search_default_waiting': 1}">
                                                Waiting
                                            </a>
                                        </div>
                                        <div class="col-3">
                                            <field name="count_mo_waiting"/>
                                        </div>
                                    </div>
                                    <div t-if="record.count_mo_late.raw_value > 0" class="row">
                                        <div class="col-9">
                                            <a class="oe_kanban_stock_picking_type_list" name="%(mrp_production_action_picking_deshboard)d" type="action" context="{'search_default_planning_issues': 1, 'default_picking_type_id': id}">
                                                Late
                                            </a>
                                        </div>
                                        <div class="col-3">
                                            <field name="count_mo_late"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

    <record id="view_picking_form_inherit_mrp" model="ir.ui.view">
        <field name="name">view.picking.form.inherit.mrp</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='use_create_lots']" position="after">
                <field name="has_kits" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='description_picking']" position="after">
                <field name="description_bom_line" optional="show" column_invisible="not parent.has_kits"/>
            </xpath>
        </field>
    </record>

    <record id="view_stock_move_line_detailed_operation_tree_mrp" model="ir.ui.view">
        <field name="name">stock.move.line.operations.tree.mrp</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.view_stock_move_line_detailed_operation_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_id']" position="after">
                <field name="description_bom_line" optional="show" column_invisible="not context.get('has_kits')"/>
            </xpath>
        </field>
    </record>

    <record id="view_picking_type_form_inherit_mrp" model="ir.ui.view">
        <field name="name">Operation Types</field>
        <field name="model">stock.picking.type</field>
        <field name="inherit_id" ref="stock.view_picking_type_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='stock_picking_type_lot']" position="after">
                <group invisible="code != 'mrp_operation'" string="Traceability" groups="stock.group_production_lot">
                    <field name="use_create_components_lots"/>
                    <field name="use_auto_consume_components_lots"/>
                </group>
            </xpath>
            <field name="auto_show_reception_report" position="after">
                <field name="auto_show_reception_report" invisible="code != 'mrp_operation'" groups="mrp.group_mrp_reception_report"/>
            </field>
            <xpath expr="//page[@name='hardware']" position="after">
                <page name="mrp_hardware" string="Hardware" invisible="code != 'mrp_operation'">
                    <group>
                        <group string="Print when done">
                            <field name="auto_print_done_production_order" string="Production Order"/>
                            <label for="auto_print_done_mrp_product_labels" string="Product Labels"/>
                            <div class="o_row">
                                <field name="auto_print_done_mrp_product_labels" string="Product Labels"/>
                                <label for="mrp_product_label_to_print" string="Print labels as:" invisible="not auto_print_done_mrp_product_labels" class="fw-bold"/>
                                <field name="mrp_product_label_to_print" invisible="not auto_print_done_mrp_product_labels"/>
                            </div>
                            <label for="auto_print_done_mrp_lot" string="Lot/SN Labels" groups="stock.group_production_lot"/>
                            <div class="o_row" groups="stock.group_production_lot">
                                <field name="auto_print_done_mrp_lot" string="Lot/SN Labels"/>
                                <label for="done_mrp_lot_label_to_print" string="Print labels as:" invisible="not auto_print_done_mrp_lot" class="fw-bold"/>
                                <field name="done_mrp_lot_label_to_print" invisible="not auto_print_done_mrp_lot"/>
                            </div>
                            <field name="auto_print_mrp_reception_report" string="Allocation Report" groups="mrp.group_mrp_reception_report"/>
                            <field name="auto_print_mrp_reception_report_labels" string="Allocation Report Labels" groups="mrp.group_mrp_reception_report"/>
                        </group>
                        <group string="">
                            <div colspan="2">
                                Odoo opens a PDF preview by default. If you want to print instantly,
                                install the IoT App on a computer that is on the same local network as the
                                barcode operator and configure the routing of the reports.
                                <a href="https://www.odoo.com/documentation/17.0/applications/productivity/iot/devices/printer.html"
                                    target="_blank" class="fa fa-arrow-right">
                                    Documentation
                                </a>
                            </div>
                        </group>
                        <group string='Print when "Create new Lot/SN"' groups="stock.group_production_lot">
                            <label for="auto_print_generated_mrp_lot" string="Lot/SN Label"/>
                            <div class="o_row">
                                <field name="auto_print_generated_mrp_lot" string="Lot/SN Label"/>
                                <label for="generated_mrp_lot_label_to_print" string="Print labels as:" invisible="not auto_print_generated_mrp_lot" class="fw-bold"/>
                                <field name="generated_mrp_lot_label_to_print" invisible="not auto_print_generated_mrp_lot"/>
                            </div>
                        </group>
                    </group>
                </page>
            </xpath>
        </field>
    </record>

    <record id="action_picking_tree_mrp_operation" model="ir.actions.act_window">
        <field name="name">Manufacturings</field>
        <field name="res_model">mrp.production</field>
        <field name="view_mode">tree,kanban,form,calendar,activity</field>
        <field name="domain"></field>
        <field name="context">{'default_company_id': allowed_company_ids[0]}</field>
        <field name="search_view_id" ref="mrp.view_mrp_production_filter"/>
    </record>

    <menuitem id="mrp_operation_picking" name="Manufacturings" parent="stock.menu_stock_transfers"
        action="action_picking_tree_mrp_operation" sequence="25"
        groups="stock.group_stock_manager,stock.group_stock_user"/>
</odoo>
