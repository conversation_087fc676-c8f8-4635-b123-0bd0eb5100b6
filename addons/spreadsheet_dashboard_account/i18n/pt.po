# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_account
# 
# Translators:
# Wil Odoo, 2023
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid " days"
msgstr "dias"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "# days"
msgstr "# de dias"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Amount"
msgstr "Valor"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Average Invoice"
msgstr "Média de Faturas"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "COGS"
msgstr "CMV"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Country"
msgstr "País"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Current year"
msgstr "Ano corrente"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Customer"
msgstr "Cliente"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "DSO"
msgstr "DSO"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Date"
msgstr "Data"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Income"
msgstr "Rendimento"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Invoiced"
msgstr "Faturado"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Invoiced by Month"
msgstr "Faturado por Mês"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Invoices"
msgstr "Faturas"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Invoices by Total Signed"
msgstr "Faturas por Total c/ Sinal"

#. module: spreadsheet_dashboard_account
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_account.dashboard_invoicing
msgid "Invoicing"
msgstr "Faturação"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "KPI - Average Invoice"
msgstr "KPI - Média de Faturas"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "KPI - DSO"
msgstr "KPI - DSO"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "KPI - Income"
msgstr "KPI - Rendimento"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "KPI - Invoice Count"
msgstr "KPI - Total de Faturas"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "KPI - Unpaid Invoices"
msgstr "KPI - Faturas por Pagar"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Period"
msgstr "Período"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Product"
msgstr "Produto"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Product Category"
msgstr "Categoria do Artigo"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Ratio"
msgstr "Rácio"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Receivable"
msgstr "A Receber"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Reference"
msgstr "Referência"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Revenue"
msgstr "Receita"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Salesperson"
msgstr "Vendedor"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Status"
msgstr "Estado"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Top Categories"
msgstr "Categorias Principais"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Top Countries"
msgstr "Países Principais"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Top Invoices"
msgstr "Faturas Principais"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Top Products"
msgstr "Artigos Principais"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "Top Salespeople"
msgstr "Vendedores Principais"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "in current year"
msgstr "no ano corrente"

#. module: spreadsheet_dashboard_account
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account/data/files/invoicing_dashboard.json:0
#, python-format
msgid "unpaid"
msgstr "por pagar"
