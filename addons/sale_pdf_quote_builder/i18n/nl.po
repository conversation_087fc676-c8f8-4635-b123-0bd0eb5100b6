# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_pdf_quote_builder
# 
# Translators:
# <PERSON><PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 10:34+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Man<PERSON> Ron<PERSON>u, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_pdf_quote_builder
#: model:ir.actions.report,print_report_name:sale_pdf_quote_builder.action_report_saleorder_raw
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in ('draft', 'sent') and 'Offerte - %s' % (object.name)) or "
"'Order - %s' % (object.name)"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid "<i class=\"fa fa-arrow-right\"/> Download examples"
msgstr "<i class=\"fa fa-arrow-right\"/> Voorbeelden downloaden"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,help:sale_pdf_quote_builder.field_product_document__attached_on
msgid ""
"Allows you to share the document with your customers within a sale.\n"
"Leave it empty if you don't want to share this document with sales customer.\n"
"Quotation: the document will be sent to and accessible by customers at any time.\n"
"e.g. this option can be useful to share Product description files.\n"
"Confirmed order: the document will be sent to and accessible by customers.\n"
"e.g. this option can be useful to share User Manual or digital content bought on ecommerce. \n"
"Inside quote: The document will be included in the pdf of the quotation \n"
"and sale order between the header pages and the quote table. "
msgstr ""
"Hiermee kan je het document delen met je klanten binnen een verkoop. \n"
"Laat het leeg als je dit document niet wilt delen met klanten. \n"
"Offerte: het document wordt verzonden naar de klanten en is op elk moment toegankelijk voor hen.\n"
"bijv. deze optie kan handig zijn om bestanden met productbeschrijvingen te delen. \n"
"Bevestigde order: het document wordt verzonden naar de klanten en is toegankelijk voor hen.\n"
"bijv. deze optie kan handig zijn voor het delen van gebuikershandleidingen of digitale inhoud die via e-commerce is gekocht. \n"
"Binnen offerte: het document wordt opgenomen in de pdf-file van de offerte\n"
"en in de verkooporder tussen de voorbladen en de offertetabel."

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_res_company__sale_footer
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_res_config_settings__sale_footer
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_template__sale_footer
msgid "Footer pages"
msgstr "Voettekst pagina's"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_res_company__sale_header
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_res_config_settings__sale_header
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_template__sale_header
msgid "Header pages"
msgstr "Koptekst pagina's"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields.selection,name:sale_pdf_quote_builder.selection__product_document__attached_on__inside
msgid "Inside quote"
msgstr "Binnen offerte"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/utils.py:0
#, python-format
msgid ""
"It seems that we're not able to process this pdf inside a quotation. It is "
"either encrypted, or encoded in a format we do not support."
msgstr ""
"Het lijkt erop dat we deze pdf niet in een offerte kunnen verwerken. Het is "
"gecodeerd of gecodeerd in een formaat dat we niet ondersteunen."

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid "Learn more from the documentation."
msgstr "Vind meer informatie in de documentatie."

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.res_config_settings_view_form
msgid ""
"Make your quote attractive by adding header pages, product descriptions and "
"footer pages to your quote."
msgstr ""
"Maak je offerte aantrekkelijk door voorbladen, productbeschrijvingen en "
"voetteksten aan je offerte toe te voegen."

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
#, python-format
msgid "Only PDF documents can be attached inside a quote."
msgstr "Enkel PDF-documenten kunnen worden toegevoegd aan een offerte."

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid "PDF Quote Builder"
msgstr "PDF offertebouwer"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.res_config_settings_view_form
msgid "PDF Quote builder"
msgstr "PDF offertebouwer"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_product_document
msgid "Product Document"
msgstr "Document product"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid ""
"Products descriptions are pdf documents you can add directly on products.\n"
"                            To do so, go on a product, find the \"product documents\" button, then add a\n"
"                            new pdf document with a visibility set as \"Inside Quotes\". For each product\n"
"                            in the quote, if the product has an \"inside quotes\" document, this document\n"
"                            will be added after header pages and before the quotation details."
msgstr ""
"Productbeschrijvingen zijn PDF-documenten die je rechtstreeks op producten kan toevoegen. \n"
"Ga hiervoor naar een product, vind de knop \"Documenten\" en voeg een\n"
"nieuw PDF-document toe met de zichtbaarheid ingesteld op \"Binnen offertes\". Voor elk product\n"
"in de offerte, als het product een \"binnen offerte\" document heeft, wordt dit document\n"
"toegevoegd na de voorbladen en voor de offertedetails."

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid ""
"Provide header pages and footer pages to compose an attractive quotation\n"
"                            with more information about your company, your products and your services.\n"
"                            The pdf of your quotes will be built by putting together header pages,\n"
"                            product descriptions, details of the quote and then the footer pages.\n"
"                            If empty, it will use those define in the company settings.<br/>"
msgstr ""
"Maak je offerte aantrekkelijk door kopteksten en voetteksten toe te voegen\n"
"met meer informatie over je bedrijf, je producten en je dienstverlening.\n"
"De PDF van je offerte wordt opgebouwd door de voorbladen,\n"
"productbeschrijvingen, details van de offerte en de voetteksten samen te stellen.\n"
"Als dit leeg blijft, worden de gegevens die in de bedrijfsinstellingen zijn bepaald gebruikt.<br/>"

#. module: sale_pdf_quote_builder
#: model:ir.actions.report,name:sale_pdf_quote_builder.action_report_saleorder_raw
msgid "Quotation / Order"
msgstr "Offerte / Order"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order_template
msgid "Quotation Template"
msgstr "Offertesjabloon"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_ir_actions_report
msgid "Report Action"
msgstr "Rapport actie"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_res_company__sale_footer_name
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_res_config_settings__sale_footer_name
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_template__sale_footer_name
msgid "Sale Footer Name"
msgstr "Naam voettekst verkoop"

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_res_company__sale_header_name
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_res_config_settings__sale_header_name
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_sale_order_template__sale_header_name
msgid "Sale Header Name"
msgstr "Naam koptekst verkoop"

#. module: sale_pdf_quote_builder
#: model:ir.model,name:sale_pdf_quote_builder.model_sale_order
msgid "Sales Order"
msgstr "Verkooporder"

#. module: sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale_pdf_quote_builder.sale_order_template_form
msgid ""
"Some information specific to the quote (customer name, quotation reference, ... )\n"
"                            can be injected in these documents using pdf forms.\n"
"                            Refer to the documentation to know more about this feature."
msgstr ""
"Bepaalde informatie die specifiek is voor de offerte (naam klant, referentie offerte,...)\n"
"kan in deze documenten worden geïnjecteerd met behulp van PDF-formulieren. \n"
"Raadpleeg de documentatie voor meer informatie over deze functie."

#. module: sale_pdf_quote_builder
#: model:ir.model.fields,field_description:sale_pdf_quote_builder.field_product_document__attached_on
msgid "Visible at"
msgstr "Zichtbaar op"

#. module: sale_pdf_quote_builder
#. odoo-python
#: code:addons/sale_pdf_quote_builder/models/product_document.py:0
#, python-format
msgid "When attached inside a quote, the document must be a file, not a URL."
msgstr ""
"Als het document in een offerte is bijgevoegd, moet het een bestand zijn en "
"geen URL."
