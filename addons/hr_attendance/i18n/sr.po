# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_attendance
# 
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> <mbo<PERSON><PERSON>@outlook.com>, 2024
# <PERSON><PERSON><PERSON>, 2024
# コフスタジオ, 2024
# <PERSON><PERSON> <dragan.v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 12:31+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Serbian (https://app.transifex.com/odoo/teams/41243/sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "\"Check Out\" time cannot be earlier than \"Check In\" time."
msgstr "Vreme odjave ne moze biti ranije nego vreme prijave"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "%s : (%s-%s)"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<br/>\n"
"                            <span>\n"
"                                If your address is compromised, you can refresh it to generate a new one.\n"
"                            </span>\n"
"                            <br/>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_hr_attendance_kanban
msgid "<i class=\"fa fa-calendar\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"
msgstr "<i class=\"fa fa-calendar\" aria-label=\"Period\" role=\"img\" title=\"Period\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid ""
"<span class=\"o_stat_text\">\n"
"                            This Month\n"
"                        </span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "<span class=\"o_stat_text\">Extra Hours</span>"
msgstr "<span class=\"o_stat_text\">Extra Hours</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-success "
"me-1\" role=\"img\" aria-label=\"Available\" title=\"Available\"/>"
msgstr ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-success "
"me-1\" role=\"img\" aria-label=\"Available\" title=\"Available\"/>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
msgid ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-warning me-1\" role=\"img\" aria-label=\"Not available\" title=\"Not available\">\n"
"                                    </span>"
msgstr ""
"<span id=\"oe_hr_attendance_status\" class=\"fa fa-circle text-warning me-1\" role=\"img\" aria-label=\"Not available\" title=\"Not available\">\n"
"                                    </span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span> Minutes</span>"
msgstr "<span> Minutes</span>"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"<span> Minutes</span>\n"
"                                <br/>\n"
"                                <br/>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span> seconds</span>"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "<span>Time Period </span>"
msgstr "<span>Time Period </span>"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Activate the count of employees' extra hours."
msgstr "Activate the count of employees' extra hours."

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid ""
"Add a few employees to be able to select an employee here and perform his check in / check out.\n"
"                To create employees go to the Employees menu."
msgstr ""
"Add a few employees to be able to select an employee here and perform his check in / check out.\n"
"                To create employees go to the Employees menu."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__adjustment
msgid "Adjustment"
msgstr "Podešavanje"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_manager
msgid "Administrator"
msgstr "Administrator"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#, python-format
msgid "All"
msgstr "Sve"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Allow Users to Check in/out from Odoo."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"counted, in benefit of the company"
msgstr ""
"Allow a period of time (around working hours) where extra time will not be "
"counted, in benefit of the company"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Allow a period of time (around working hours) where extra time will not be "
"deducted, in benefit of the employee"
msgstr ""
"Allow a period of time (around working hours) where extra time will not be "
"deducted, in benefit of the employee"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
msgid "Amount of extra hours"
msgstr "Amount of extra hours"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "At Work"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_attachment_count
msgid "Attachment Count"
msgstr "Broj priloga"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#: model:ir.model,name:hr_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_ids
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_tree_inherit_leave
#, python-format
msgid "Attendance"
msgstr "Prisustvo"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_from_systray
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_from_systray
msgid "Attendance From Systray"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_delay
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_delay
msgid "Attendance Kiosk Delay"
msgstr "Attendance Kiosk Delay"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_key
msgid "Attendance Kiosk Key"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_url
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_url
msgid "Attendance Kiosk Url"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_manager_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_manager_id
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__attendance_manager_id
msgid "Attendance Manager"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_mode
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_mode
msgid "Attendance Mode"
msgstr "Attendance Mode"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_attendance_overtime
msgid "Attendance Overtime"
msgstr "Attendance Overtime"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__attendance_state
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__attendance_state
msgid "Attendance Status"
msgstr "Attendance Status"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_action
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_reporting
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_root
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances"
msgstr "Prisustva"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#: code:addons/hr_attendance/models/res_users.py:0
#, python-format
msgid "Attendances This Month"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Attendances from Backend"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__back
msgid "Back Camera"
msgstr "Back Camera"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__barcode
msgid "Barcode / RFID"
msgstr "Barcode / RFID"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__barcode_manual
msgid "Barcode / RFID and Manual Selection"
msgstr "Barcode / RFID and Manual Selection"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_barcode_source
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_barcode_source
msgid "Barcode Source"
msgstr "Barcode Source"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__in_country_name
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__out_country_name
msgid "Based on IP Address"
msgstr "Na osnovu IP adrese"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee_base
msgid "Basic Employee"
msgstr "Osnovni zaposleni"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#, python-format
msgid "Before"
msgstr "Pre"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_browser
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Browser"
msgstr "Pretraživač"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee hasn't "
"checked out since %(datetime)s"
msgstr ""
"Cannot create new attendance record for %(empl_name)s, the employee hasn't "
"checked out since %(datetime)s"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Cannot create new attendance record for %(empl_name)s, the employee was "
"already checked in on %(datetime)s"
msgstr ""
"Cannot create new attendance record for %(empl_name)s, the employee was "
"already checked in on %(datetime)s"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#, python-format
msgid ""
"Cannot perform check out on %(empl_name)s, could not find corresponding "
"check in. Your attendances have probably been modified manually by human "
"resources."
msgstr ""
"Cannot perform check out on %(empl_name)s, could not find corresponding "
"check in. Your attendances have probably been modified manually by human "
"resources."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/check_in_out/check_in_out.xml:0
#, python-format
msgid "Check IN"
msgstr "Check IN"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_check_in
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_in
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Check In"
msgstr "Prijava"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/check_in_out/check_in_out.xml:0
#, python-format
msgid "Check OUT"
msgstr "Check OUT"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_check_out
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__last_check_out
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Check Out"
msgstr "Odjava"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#, python-format
msgid "Check in"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#, python-format
msgid "Check out"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_in
msgid "Checked in"
msgstr "Prijavljen/a"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Checked in at"
msgstr "Checked in at"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_employee__attendance_state__checked_out
msgid "Checked out"
msgstr "Checked out"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Checked out at"
msgstr "Checked out at"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Choose how long the greeting message will be displayed."
msgstr "Choose how long the greeting message will be displayed."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_city
msgid "City"
msgstr "Grad"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__color
msgid "Color"
msgstr "Boja"

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_company
msgid "Companies"
msgstr "Preduzeća"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__company_id
msgid "Company"
msgstr "Kompanija"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
#, python-format
msgid "Company Logo"
msgstr "Logo preduzeća"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Compare attendance with working hours set on employee."
msgstr "Compare attendance with working hours set on employee."

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_config_settings
msgid "Config Settings"
msgstr "Podešavanje konfiguracije"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_settings
msgid "Configuration"
msgstr "Konfiguracija"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__hr_attendance_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__hr_attendance_overtime
msgid "Count Extra Hours"
msgstr "Računajte dodatne sate"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Count of Extra Hours"
msgstr "Count of Extra Hours"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Count of extra hours is considered from this date. Potential extra hours "
"prior to this date are not considered."
msgstr ""
"Count of extra hours is considered from this date. Potential extra hours "
"prior to this date are not considered."

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_country_name
msgid "Country"
msgstr "Zemlja"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_employee_attendance_action_kanban
msgid "Create a new employee"
msgstr "Kreirajte novog zaposlenog"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__create_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__date
msgid "Day"
msgstr "Dan"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Define the camera used for the barcode scan."
msgstr "Define the camera used for the barcode scan."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Define the way the user will be identified by the application."
msgstr "Define the way the user will be identified by the application."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__department_id
#, python-format
msgid "Department"
msgstr "Odeljenje"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__hr_attendance_display_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__hr_attendance_display_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__display_extra_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Extra Hours"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Extra Hours in Kiosk mode and on User profile."
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Extra Hours."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__display_name
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Display Time"
msgstr "Display Time"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid ""
"Do not have access, user cannot edit the attendances that are not his own."
msgstr ""
"Do not have access, user cannot edit the attendances that are not his own."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: model:ir.model,name:hr_attendance.model_hr_employee
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__employee_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__employee_id
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_employees_view_kanban
#, python-format
msgid "Employee"
msgstr "Zaposleni"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__attendance_kiosk_use_pin
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__attendance_kiosk_use_pin
msgid "Employee PIN Identification"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Employee attendances"
msgstr "Employee attendances"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_employee_attendance_action_kanban
msgid "Employees"
msgstr "Zaposleni"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Error: could not find corresponding employee."
msgstr "Error: could not find corresponding employee."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Errors"
msgstr "Greške"

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.hr_attendance_overtime_action
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Extra Hours"
msgstr "Dodatni sati"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra Hours (Real)"
msgstr "Extra Hours (Real)"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_start_date
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_start_date
msgid "Extra Hours Starting Date"
msgstr "Extra Hours Starting Date"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Extra hours today:"
msgstr "Dodatni sati danas."

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance_overtime__duration_real
msgid "Extra-hours including the threshold duration"
msgstr "Extra-hours including the threshold duration"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "From %s"
msgstr "Od %s"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__front
msgid "Front Camera"
msgstr "Front Camera"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "GPS Coordinates"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Generate a new Kiosk Mode URL"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Go back"
msgstr "Nazad"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Goodbye"
msgstr "Doviđenja"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__has_message
msgid "Has Message"
msgstr "Ima poruku"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Hours"
msgstr "Sati"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month
msgid "Hours Last Month"
msgstr "Sati proteklog meseca"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_last_month_display
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__hours_last_month_display
msgid "Hours Last Month Display"
msgstr "Prikaz sati proteklog meseca"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_previously_today
msgid "Hours Previously Today"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Hours Previously Today:"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__hours_today
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__hours_today
msgid "Hours Today"
msgstr "Sati danas"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Hours Today :"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Hr Attendance Search"
msgstr "Hr Attendance Search"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__id
msgid "ID"
msgstr "ID"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_ip_address
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "IP Address"
msgstr "IP adresa"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
#, python-format
msgid "Identify Manually"
msgstr "Identify Manually"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je označeno, nove poruke zahtevaju vašu pažnju."

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_has_error
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ako je označeno, neke poruke imaju grešku u isporuci."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Invalid request"
msgstr "Invalid request"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__kiosk
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__kiosk
msgid "Kiosk"
msgstr "Kiosk"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_kiosk_no_user_mode
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Kiosk Mode"
msgstr "Kiosk Mode"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Kiosk Mode Adress"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Kiosk Settings"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Last 3 Months"
msgstr "Poslednja 3 meseca"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "Last 7 days"
msgstr "Poslednjih 7 dana"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_id
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__last_attendance_id
msgid "Last Attendance"
msgstr "Last Attendance"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__last_attendance_worked_hours
msgid "Last Attendance Worked Hours"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_uid
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_uid
msgid "Last Updated by"
msgstr "Poslednji put ažurirao"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__write_date
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance_overtime__write_date
msgid "Last Updated on"
msgstr "Poslednji put ažurirano"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_latitude
msgid "Latitude"
msgstr "Geografska širina"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Localisation"
msgstr "Lokalizacija"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_longitude
msgid "Longitude"
msgstr "Geografska dužina"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__manual
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__manual
msgid "Manual"
msgstr "Ručno"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_kiosk_mode__manual
msgid "Manual Selection"
msgstr "Manual Selection"

#. module: hr_attendance
#: model:ir.actions.client,name:hr_attendance.hr_attendance_action_greeting_message
msgid "Message"
msgstr "Poruka"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_has_error
msgid "Message Delivery error"
msgstr "Greška pri isporuci poruke"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__in_mode
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "Mode"
msgstr "Model"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Modes"
msgstr "Režimi"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Attendances"
msgstr "My Attendances"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_filter
msgid "My Team"
msgstr "Moj tim"

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_reporting
msgid "No attendance records found"
msgstr "No attendance records found"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.js:0
#, python-format
msgid "No employee corresponding to Badge ID '%(barcode)s.'"
msgstr "No employee corresponding to Badge ID '%(barcode)s.'"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_has_error_counter
msgid "Number of errors"
msgstr "Broj grešaka"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Broj poruka koje zahtevaju akciju"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Broj poruka sa greškom u isporuci"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "OK"
msgstr "U redu"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_officer
msgid "Officer: Manage attendances"
msgstr ""

#. module: hr_attendance
#: model:ir.actions.server,name:hr_attendance.open_kiosk_url
msgid "Open Kiosk Url"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_browser
msgid "Out Browser"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_city
msgid "Out City"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_country_name
msgid "Out Country Name"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_ip_address
msgid "Out Ip Address"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_latitude
msgid "Out Latitude"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_longitude
msgid "Out Longitude"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__out_mode
msgid "Out Mode"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__overtime_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Over Time"
msgstr ""

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_employee.py:0
#: code:addons/hr_attendance/models/res_users.py:0
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__overtime_ids
#, python-format
msgid "Overtime"
msgstr "Prekovremeno"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_view_attendances
msgid "Overview"
msgstr "Pregled"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Please enter your PIN to"
msgstr "Please enter your PIN to"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Please return to the main menu."
msgstr "Please return to the main menu."

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_hr_employee_public
msgid "Public Employee"
msgstr "Public Employee"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__rating_ids
msgid "Ratings"
msgstr "Ocene"

#. module: hr_attendance
#: model:ir.ui.menu,name:hr_attendance.menu_hr_attendance_reporting
msgid "Reporting"
msgstr "Izveštavanje"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Greška u dostavi SMS-a"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
#, python-format
msgid "Scan your badge"
msgstr "Scan your badge"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__res_company__attendance_barcode_source__scanner
msgid "Scanner"
msgstr "Skener"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#: code:addons/hr_attendance/static/src/components/manual_selection/manual_selection.xml:0
#, python-format
msgid "Search..."
msgstr "Pretraga..."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Set PIN codes in the employee detail form (in HR Settings tab)."
msgstr "Set PIN codes in the employee detail form (in HR Settings tab)."

#. module: hr_attendance
#: model:ir.actions.act_window,name:hr_attendance.action_hr_attendance_settings
msgid "Settings"
msgstr "Podešavanje"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Sign out"
msgstr "Sign out"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#, python-format
msgid "Since"
msgstr "Od"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Start from"
msgstr "Početi od"

#. module: hr_attendance
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__in_mode__systray
#: model:ir.model.fields.selection,name:hr_attendance.selection__hr_attendance__out_mode__systray
msgid "Systray"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_action
msgid "The attendance records of your employees will be displayed here."
msgstr "The attendance records of your employees will be displayed here."

#. module: hr_attendance
#: model_terms:ir.actions.act_window,help:hr_attendance.hr_attendance_reporting
msgid "The attendance reporting of your employees will be displayed here."
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_employee__attendance_manager_id
#: model:ir.model.fields,help:hr_attendance.field_hr_employee_public__attendance_manager_id
#: model:ir.model.fields,help:hr_attendance.field_res_users__attendance_manager_id
msgid ""
"The user set in Attendance will access the attendance of the employee "
"through the dedicated app and will be able to edit them."
msgstr ""

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_own_reader
msgid ""
"The user will have access to his own attendances on his user / employee "
"profile"
msgstr ""

#. module: hr_attendance
#: model:res.groups,comment:hr_attendance.group_hr_attendance_officer
msgid ""
"The user will have access to the attendance records and reporting of "
"employees where he's set as an attendance manager"
msgstr ""

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_company_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_company_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Company"
msgstr "Tolerance Time In Favor Of Company"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_res_company__overtime_employee_threshold
#: model:ir.model.fields,field_description:hr_attendance.field_res_config_settings__overtime_employee_threshold
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Tolerance Time In Favor Of Employee"
msgstr "Tolerance Time In Favor Of Employee"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_hr_employee_public__total_overtime
#: model:ir.model.fields,field_description:hr_attendance.field_res_users__total_overtime
msgid "Total Overtime"
msgstr "Ukupno prekovremeni rad"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Total extra hours:"
msgstr "Total extra hours:"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/attendance_menu/attendance_menu.xml:0
#, python-format
msgid "Total today"
msgstr ""

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/controllers/main.py:0
#: code:addons/hr_attendance/controllers/main.py:0
#, python-format
msgid "Unknown"
msgstr "Nepoznato"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid "Use PIN codes (defined on the Employee's profile) to check-in."
msgstr "Use PIN codes (defined on the Employee's profile) to check-in."

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.res_config_settings_view_form
msgid ""
"Use this url to access your kiosk mode from any device. Warning, anybody "
"with the link can access your kiosk."
msgstr ""

#. module: hr_attendance
#: model:ir.model,name:hr_attendance.model_res_users
msgid "User"
msgstr "Korisnik"

#. module: hr_attendance
#: model:res.groups,name:hr_attendance.group_hr_attendance_own_reader
msgid "User: Read his own attendances"
msgstr ""

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_form
msgid "View on Maps"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Want to check out?"
msgstr "Want to check out?"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__website_message_ids
msgid "Website Messages"
msgstr "Website poruke"

#. module: hr_attendance
#: model:ir.model.fields,help:hr_attendance.field_hr_attendance__website_message_ids
msgid "Website communication history"
msgstr "Istorija website komunikacije"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/greetings/greetings.xml:0
#, python-format
msgid "Welcome"
msgstr "Dobrodošli"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.xml:0
#, python-format
msgid "Welcome to"
msgstr "Dobro došli na"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "Welcome!"
msgstr "Dobrodošli!"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_employee_simple_tree_view
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_attendance_tree
msgid "Work Hours"
msgstr "Radno vreme"

#. module: hr_attendance
#: model:ir.model.fields,field_description:hr_attendance.field_hr_attendance__worked_hours
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_graph
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_attendance_view_pivot
msgid "Worked Hours"
msgstr "Worked Hours"

#. module: hr_attendance
#: model_terms:ir.ui.view,arch_db:hr_attendance.hr_user_view_form
#: model_terms:ir.ui.view,arch_db:hr_attendance.view_employee_form_inherit_hr_attendance
msgid "Worked hours this month"
msgstr ""

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/public_kiosk/public_kiosk_app.js:0
#, python-format
msgid "Wrong Pin"
msgstr ""

#. module: hr_attendance
#. odoo-python
#: code:addons/hr_attendance/models/hr_attendance.py:0
#, python-format
msgid "You cannot duplicate an attendance."
msgstr "You cannot duplicate an attendance."

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "check in"
msgstr "prijava"

#. module: hr_attendance
#. odoo-javascript
#: code:addons/hr_attendance/static/src/components/pin_code/pin_code.xml:0
#, python-format
msgid "check out"
msgstr "odjava"
