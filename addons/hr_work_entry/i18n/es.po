# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_employee.py:0
#, python-format
msgid "%s work entries"
msgstr "%s entradas de trabajo"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_employee_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Work Entries\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Entradas de trabajo\n"
"                            </span>"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "<span>Hours</span>"
msgstr "<span>Horas</span>"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__active
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__active
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__active
msgid "Active"
msgstr "Activo"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Archived"
msgstr "Archivado"

#. module: hr_work_entry
#: model:hr.work.entry.type,name:hr_work_entry.work_entry_type_attendance
msgid "Attendance"
msgstr "Asistencia"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_tree
msgid "Beginning"
msgstr "Comenzando"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__cancelled
msgid "Cancelled"
msgstr "Cancelado"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry__code
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__code
msgid ""
"Careful, the Code is used in many references, changing it could lead to "
"unwanted changes."
msgstr ""
"Cuidado, el código se utiliza en muchas referencias, cambiarlo podría "
"resultar en cambios no deseados."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__color
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__color
msgid "Color"
msgstr "Color"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__company_id
msgid "Company"
msgstr "Compañía"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__conflict
msgid "Conflict"
msgstr "Conflicto"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Conflicting"
msgstr "Contradictorio"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__conflict
msgid "Conflicts"
msgstr "Conflictos"

#. module: hr_work_entry
#: model_terms:ir.actions.act_window,help:hr_work_entry.hr_work_entry_type_action
msgid "Create a new work entry type"
msgstr "Crear un nuevo tipo de entrada de trabajo"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__create_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__create_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__create_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__create_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__create_date
msgid "Created on"
msgstr "Creado el"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Current Month"
msgstr "Mes actual"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Date"
msgstr "Fecha"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__department_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Department"
msgstr "Departamento"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Description"
msgstr "Descripción"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__display_name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__display_name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__draft
msgid "Draft"
msgstr "Borrador"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__duration
msgid "Duration"
msgstr "Duración"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_employee
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__employee_id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__employee_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Employee"
msgstr "Empleado"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_tree
msgid "End"
msgstr "Fin"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__external_code
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__external_code
msgid "External Code"
msgstr "Código externo"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__date_start
msgid "From"
msgstr "Desde"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_work_entry
msgid "HR Work Entry"
msgstr "Entrada de trabajo de RR. HH."

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "Tipo de entrada de trabajo de RR. HH."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_employee__has_work_entries
msgid "Has Work Entries"
msgstr "Tiene entradas de trabajo"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__id
msgid "ID"
msgstr "ID"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__active
msgid ""
"If the active field is set to false, it will allow you to hide the work "
"entry type without removing it."
msgstr ""
"Si el campo activo se establece en \"False\", podrá ocultar el tipo de "
"entrada de trabajo sin eliminarlo."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__write_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__write_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__write_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__write_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__user_id
msgid "Me"
msgstr "Yo"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__name
msgid "Name"
msgstr "Nombre"

#. module: hr_work_entry
#: model_terms:ir.actions.act_window,help:hr_work_entry.hr_work_entry_action
msgid "No data to display"
msgstr "Sin datos que mostrar"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Note: Validated work entries cannot be modified."
msgstr "Nota: las entradas de trabajo validadas no se pueden modificar."

#. module: hr_work_entry
#: model:hr.work.entry.type,name:hr_work_entry.overtime_work_entry_type
msgid "Overtime Hours"
msgstr "Horas extraordinarias"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__code
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__code
msgid "Payroll Code"
msgstr "Código de nómina"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Detalle de la ausencia del recurso"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Search Work Entry"
msgstr "Buscar entrada de trabajo"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_search
msgid "Search Work Entry Type"
msgstr "Buscar tipo de entrada de trabajo"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: hr_work_entry
#. odoo-javascript
#: code:addons/hr_work_entry/static/src/xml/work_entry_templates.xml:0
#, python-format
msgid "Solve conflicts first"
msgstr "Resolver conflictos primero"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entry_start_before_end
msgid "Starting time should be before end time."
msgstr "La hora de inicio debe ser antes de la hora de finalización."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__state
msgid "State"
msgstr "Estado"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry_type_unique_work_entry_code
msgid "The same code cannot be associated to multiple work entry types."
msgstr ""
"El mismo código no puede asociarse a múltiples tipos de entradas de trabajo."

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
msgid "Time Off Options"
msgstr "Opciones de ausencia"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__date_stop
msgid "To"
msgstr "A"

#. module: hr_work_entry
#: model_terms:ir.actions.act_window,help:hr_work_entry.hr_work_entry_action
msgid ""
"Try to add some records, or make sure that there is no active filter in the "
"search bar."
msgstr ""
"Intente añadir algunos registros o asegúrese de que no haya ningún filtro "
"activo en la barra de búsqueda."

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Type"
msgstr "Tipo"

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
#, python-format
msgid "Undefined"
msgstr "Indefinido"

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
#, python-format
msgid "Undefined Type"
msgstr "Tipo indefinido"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry__external_code
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__external_code
msgid "Use this code to export your data to a third party"
msgstr "Utilice este código para exportar sus datos a terceros."

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__validated
msgid "Validated"
msgstr "Validado"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entries_no_validated_conflict
msgid "Validated work entries cannot overlap"
msgstr "Las entradas de trabajo validadas no se pueden sobreponer"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Detalle del trabajo"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_pivot
msgid "Work Entries"
msgstr "Entradas de trabajo"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_user_work_entry_employee
msgid "Work Entries Employees"
msgstr "Empleados de entradas de trabajo"

#. module: hr_work_entry
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_action
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_action_conflict
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Work Entry"
msgstr "Entrada de trabajo"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Work Entry Name"
msgstr "Nombre de la entrada de trabajo"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__work_entry_type_id
#: model:ir.model.fields,field_description:hr_work_entry.field_resource_calendar_attendance__work_entry_type_id
#: model:ir.model.fields,field_description:hr_work_entry.field_resource_calendar_leaves__work_entry_type_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_work_entry.resource_calendar_leaves_view_search_inherit
msgid "Work Entry Type"
msgstr "Tipo de entrada de trabajo"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
msgid "Work Entry Type Name"
msgstr "Nombre del tipo de entrada de trabajo"

#. module: hr_work_entry
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_type_action
msgid "Work Entry Types"
msgstr "Tipos de entradas de trabajo"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entry_has_end
msgid "Work entry must end. Please define an end date or a duration."
msgstr ""
"La entrada de trabajo debe tener un fin. Defina una fecha de finalización o "
"una duración."

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_user_work_entry_employee_user_id_employee_id_unique
msgid "You cannot have the same employee twice."
msgstr "No puede tener el mismo empleado dos veces."
