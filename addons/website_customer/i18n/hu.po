# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_customer
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# Szabolcs Rádi, 2023
# <PERSON><PERSON>é<PERSON> <<EMAIL>>, 2023
# krnkris, 2023
# <PERSON>, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2023\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
msgid "<i class=\"fa fa-chevron-left me-2\"/>Back to references"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid ""
"<i class=\"fa fa-map-marker\" role=\"img\" aria-label=\"Open map\" "
"title=\"Open map\"/>"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "<span class=\"fa fa-1x fa-tags\"/> All"
msgstr "<span class=\"fa fa-1x fa-tags\"/> Összes"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "<span class=\"small text-muted\">Implemented by</span>"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__active
msgid "Active"
msgstr "Aktív"

#. module: website_customer
#. odoo-python
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "Összes ország"

#. module: website_customer
#. odoo-python
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Industries"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Archived"
msgstr "Archivált"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
msgid "Back to references list"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__classname
msgid "Bootstrap class to customize the color"
msgstr "Rendszerbetöltő osztály a színek testreszabásához"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__can_publish
msgid "Can Publish"
msgstr "Publikálhat"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__name
msgid "Category Name"
msgstr "Kategórianév"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__classname
msgid "Class"
msgstr "Osztály"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "Close"
msgstr "Bezárás"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner
msgid "Contact"
msgstr "Kapcsolat"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "Countries Filter"
msgstr ""

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid "Create a new contact tag"
msgstr "Új kapcsolat címke létrehozása"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__display_name
msgid "Display Name"
msgstr "Megjelenített név"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Enter a short description"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Filter by Country"
msgstr "Ország szerinti szűrés"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Filter by Industry"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Filter by Tags"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Filters"
msgstr "Szűrők"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Go to customer"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__id
msgid "ID"
msgstr "Azonosító"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "Industry Filter"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__is_published
msgid "Is Published"
msgstr "Közzétett"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_date
msgid "Last Updated on"
msgstr "Frissítve"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid ""
"Manage contact tags to better classify them for tracking and analysis "
"purposes."
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "No results found for \""
msgstr "Nincs eredmény a következőre: \""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country_list
msgid "Open countries dropdown"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_industry_list
msgid "Open industries dropdown"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Our references"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_form
msgid "Partner Tag"
msgstr "Ügyfél címke"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner_tag
msgid ""
"Partner Tags - These tags can be used on website to find customers by "
"sector, or ..."
msgstr ""

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__partner_ids
msgid "Partners"
msgstr "Partnerek"

#. module: website_customer
#. odoo-python
#: code:addons/website_customer/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_customer.references_block
#, python-format
msgid "References"
msgstr "Hivatkozások"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "References Page"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Search"
msgstr "Keresés"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Search Partner Tag"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "See all customers"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "See countries filters"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "See industries filters"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "Show Map"
msgstr ""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "Tags Filter"
msgstr ""

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"A teljes elérési út/URL a dokumentum weboldalon keresztüli eléréséhez."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_published
msgid "Visible on current website"
msgstr "Látható ezen a weboldalon"

#. module: website_customer
#: model:ir.model,name:website_customer.model_website
msgid "Website"
msgstr "Honlap"

#. module: website_customer
#: model:ir.actions.act_window,name:website_customer.action_partner_tag_form
#: model:ir.ui.menu,name:website_customer.menu_partner_tag_form
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_list
#: model_terms:ir.ui.view,arch_db:website_customer.view_partners_form_website
msgid "Website Tags"
msgstr "Weboldal címkék"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_url
msgid "Website URL"
msgstr "Honlap címe"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner__website_tag_ids
#: model:ir.model.fields,field_description:website_customer.field_res_users__website_tag_ids
msgid "Website tags"
msgstr "Weboldal címkék"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "World Map"
msgstr "Világtérkép"
