# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_customer
# 
# Translators:
# Wil Odoo, 2023
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Wil Odoo, 2023\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
msgid "<i class=\"fa fa-chevron-left me-2\"/>Back to references"
msgstr "<i class=\"fa fa-chevron-left me-2\"/>Voltar às referências"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid ""
"<i class=\"fa fa-map-marker\" role=\"img\" aria-label=\"Open map\" "
"title=\"Open map\"/>"
msgstr ""
"<i class=\"fa fa-map-marker\" role=\"img\" aria-label=\"Open map\" "
"title=\"Open map\"/>"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
#: model_terms:ir.ui.view,arch_db:website_customer.opt_tag_list
msgid "<span class=\"fa fa-1x fa-tags\"/> All"
msgstr "<span class=\"fa fa-1x fa-tags\"/> Tudo"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.implemented_by_block
msgid "<span class=\"small text-muted\">Implemented by</span>"
msgstr "<span class=\"small text-muted\">Implementado por</span>"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__active
msgid "Active"
msgstr "Ativo"

#. module: website_customer
#. odoo-python
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Countries"
msgstr "Todos os países"

#. module: website_customer
#. odoo-python
#: code:addons/website_customer/controllers/main.py:0
#, python-format
msgid "All Industries"
msgstr "Todos os segmentos"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Archived"
msgstr "Arquivado"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.details
msgid "Back to references list"
msgstr "Voltar à lista de referências"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__classname
msgid "Bootstrap class to customize the color"
msgstr "Classe de Bootstrap para personalizar a cor"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__can_publish
msgid "Can Publish"
msgstr "Pode publicar"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__name
msgid "Category Name"
msgstr "Nome da categoria"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__classname
msgid "Class"
msgstr "Classe"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "Close"
msgstr "Fechar"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner
msgid "Contact"
msgstr "Contato"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "Countries Filter"
msgstr "Filtro de países"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid "Create a new contact tag"
msgstr "Criar um novo marcador de contato"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__create_date
msgid "Created on"
msgstr "Criado em"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Enter a short description"
msgstr "Insira uma descrição curta"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Filter by Country"
msgstr "Filtrar por país"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Filter by Industry"
msgstr "Filtrar por segmento"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Filter by Tags"
msgstr "Filtrar por marcadores"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Filters"
msgstr "Filtros"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Go to customer"
msgstr "Ir ao cliente"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__id
msgid "ID"
msgstr "ID"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "Industry Filter"
msgstr "Filtro de segmentos"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__is_published
msgid "Is Published"
msgstr "Está publicado"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: website_customer
#: model_terms:ir.actions.act_window,help:website_customer.action_partner_tag_form
msgid ""
"Manage contact tags to better classify them for tracking and analysis "
"purposes."
msgstr ""
"Gerencie marcadores de contatos para uma melhor classificação, para fins de "
"análise e rastreamento."

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "No results found for \""
msgstr "Sem resultados encontrados para \""

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country_list
msgid "Open countries dropdown"
msgstr "Abrir menu de opções de países"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_industry_list
msgid "Open industries dropdown"
msgstr "Abrir menu de opções de segmentos"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Our references"
msgstr "Nossas referências"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_form
msgid "Partner Tag"
msgstr "Marcador de usuário"

#. module: website_customer
#: model:ir.model,name:website_customer.model_res_partner_tag
msgid ""
"Partner Tags - These tags can be used on website to find customers by "
"sector, or ..."
msgstr ""
"Marcadores de usuário - Estes marcadores podem ser utilizados no site para "
"encontrar clientes por setor ou..."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__partner_ids
msgid "Partners"
msgstr "Parceiros"

#. module: website_customer
#. odoo-python
#: code:addons/website_customer/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_customer.references_block
#, python-format
msgid "References"
msgstr "Referências"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "References Page"
msgstr "Página de referências"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "Search"
msgstr "Pesquisar"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.res_partner_tag_view_search
msgid "Search Partner Tag"
msgstr "Pesquisar marcador de usuário"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "See all customers"
msgstr "Ver todos os clientes"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "See countries filters"
msgstr "Ver filtros de países"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.index
msgid "See industries filters"
msgstr "Ver filtros de segmentos"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "Show Map"
msgstr "Mostrar mapa"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.snippet_options
msgid "Tags Filter"
msgstr "Filtro de marcadores"

#. module: website_customer
#: model:ir.model.fields,help:website_customer.field_res_partner_tag__website_url
msgid "The full URL to access the document through the website."
msgstr "A URL completa para acessar o documento através do site."

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_published
msgid "Visible on current website"
msgstr "Visível neste site"

#. module: website_customer
#: model:ir.model,name:website_customer.model_website
msgid "Website"
msgstr "Site"

#. module: website_customer
#: model:ir.actions.act_window,name:website_customer.action_partner_tag_form
#: model:ir.ui.menu,name:website_customer.menu_partner_tag_form
#: model_terms:ir.ui.view,arch_db:website_customer.view_partner_tag_list
#: model_terms:ir.ui.view,arch_db:website_customer.view_partners_form_website
msgid "Website Tags"
msgstr "Marcadores do site"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner_tag__website_url
msgid "Website URL"
msgstr "URL do site"

#. module: website_customer
#: model:ir.model.fields,field_description:website_customer.field_res_partner__website_tag_ids
#: model:ir.model.fields,field_description:website_customer.field_res_users__website_tag_ids
msgid "Website tags"
msgstr "Marcadores do site"

#. module: website_customer
#: model_terms:ir.ui.view,arch_db:website_customer.opt_country
msgid "World Map"
msgstr "Mapa Mundi"
