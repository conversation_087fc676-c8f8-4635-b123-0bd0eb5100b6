{\rtf1\ansi\deff3\adeflang1025
{\fonttbl{\f0\froman\fprq2\fcharset0 Times New Roman;}{\f1\froman\fprq2\fcharset2 Symbol;}{\f2\fswiss\fprq2\fcharset0 Arial;}{\f3\froman\fprq2\fcharset0 Liberation Serif{\*\falt Times New Roman};}{\f4\froman\fprq0\fcharset128 Arial;}{\f5\froman\fprq0\fcharset128 Liberation Serif{\*\falt Times New Roman};}{\f6\fswiss\fprq0\fcharset128 Liberation Sans{\*\falt Arial};}{\f7\fnil\fprq0\fcharset128 Liberation Serif{\*\falt Times New Roman};}{\f8\fnil\fprq0\fcharset128 FreeSans;}{\f9\fnil\fprq2\fcharset0 FreeSans;}{\f10\fswiss\fprq0\fcharset128 FreeSans;}{\f11\fnil\fprq2\fcharset0 Liberation Serif{\*\falt Times New Roman};}}
{\colortbl;\red0\green0\blue0;\red128\green128\blue128;}
{\stylesheet{\s0\snext0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033 Normal;}
{\s15\sbasedon0\snext16\ql\nowidctlpar\sb240\sa120\keepn\ltrpar\cf1\kerning1\dbch\af8\langfe2052\dbch\af9\afs28\alang1081\loch\f6\fs28\lang1033 Heading;}
{\s16\sbasedon0\snext16\sl288\slmult1\ql\nowidctlpar\sb0\sa140\ltrpar\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033 Text Body;}
{\s17\sbasedon16\snext17\sl288\slmult1\ql\nowidctlpar\sb0\sa140\ltrpar\cf1\kerning1\dbch\af8\langfe2052\dbch\af10\afs24\alang1081\loch\f5\fs24\lang1033 List;}
{\s18\sbasedon0\snext18\ql\nowidctlpar\sb120\sa120\noline\ltrpar\cf1\i\kerning1\dbch\af8\langfe2052\dbch\af10\afs24\alang1081\ai\loch\f5\fs24\lang1033 Caption;}
{\s19\sbasedon0\snext19\ql\nowidctlpar\noline\ltrpar\cf1\kerning1\dbch\af8\langfe2052\dbch\af10\afs24\alang1081\loch\f5\fs24\lang1033 Index;}
}{\info{\creatim\yr0\mo0\dy0\hr0\min0}{\revtim\yr0\mo0\dy0\hr0\min0}{\printim\yr0\mo0\dy0\hr0\min0}{\comment LibreOffice}{\vern67241986}}\deftab720
\viewscale150
{\*\pgdsctbl
{\pgdsc0\pgdscuse451\pgwsxn12240\pghsxn15840\marglsxn1800\margrsxn1800\margtsxn1440\margbsxn1440\pgdscnxt0 Default Style;}}
\formshade{\*\pgdscno0}\paperh15840\paperw12240\margl1800\margr1800\margt1440\margb1440\sectd\sbknone\sectunlocked1\pgndec\pgwsxn12240\pghsxn15840\marglsxn1800\margrsxn1800\margtsxn1440\margbsxn1440\ftnbj\ftnstart1\ftnrstcont\ftnnar\aenddoc\aftnrstcont\aftnstart1\aftnnrlc
\pgndec\pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
For copyright information, please see the COPYRIGHT file.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Odoo is published under the GNU LESSER GENERAL PUBLIC LICENSE, Version 3\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
(LGPLv3), as included below. Since the LGPL is a set of additional\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
permissions on top of the GPL, the text of the GPL is included at the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
bottom as well.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Some external libraries and contributions bundled with Odoo may be published\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
under other GPL-compatible licenses. For these, please refer to the relevant\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
source files and/or license files, in the source code tree.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
**************************************************************************\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
                   }{\rtlch \ltrch\loch
GNU LESSER GENERAL PUBLIC LICENSE\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
                       }{\rtlch \ltrch\loch
Version 3, 29 June 2007\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
 }{\rtlch \ltrch\loch
Copyright (C) 2007 Free Software Foundation, Inc. <http://fsf.org/>\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
 }{\rtlch \ltrch\loch
Everyone is permitted to copy and distribute verbatim copies\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
 }{\rtlch \ltrch\loch
of this license document, but changing it is not allowed.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
This version of the GNU Lesser General Public License incorporates\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the terms and conditions of version 3 of the GNU General Public\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
License, supplemented by the additional permissions listed below.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
0. Additional Definitions.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
As used herein, "this License" refers to version 3 of the GNU Lesser\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
General Public License, and the "GNU GPL" refers to version 3 of the GNU\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
General Public License.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
"The Library" refers to a covered work governed by this License,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
other than an Application or a Combined Work as defined below.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
An "Application" is any work that makes use of an interface provided\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
by the Library, but which is not otherwise based on the Library.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Defining a subclass of a class defined by the Library is deemed a mode\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
of using an interface provided by the Library.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
A "Combined Work" is a work produced by combining or linking an\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Application with the Library.  The particular version of the Library\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
with which the Combined Work was made is also called the "Linked\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Version".\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The "Minimal Corresponding Source" for a Combined Work means the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Corresponding Source for the Combined Work, excluding any source code\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
for portions of the Combined Work that, considered in isolation, are\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
based on the Application, and not on the Linked Version.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The "Corresponding Application Code" for a Combined Work means the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
object code and/or source code for the Application, including any data\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
and utility programs needed for reproducing the Combined Work from the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Application, but excluding the System Libraries of the Combined Work.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
1. Exception to Section 3 of the GNU GPL.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
You may convey a covered work under sections 3 and 4 of this License\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
without being bound by section 3 of the GNU GPL.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
2. Conveying Modified Versions.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
If you modify a copy of the Library, and, in your modifications, a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
facility refers to a function or data to be supplied by an Application\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
that uses the facility (other than as an argument passed when the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
facility is invoked), then you may convey a copy of the modified\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
version:\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
a) under this License, provided that you make a good faith effort to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
ensure that, in the event an Application does not supply the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
function or data, the facility still operates, and performs\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
whatever part of its purpose remains meaningful, or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
b) under the GNU GPL, with none of the additional permissions of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
this License applicable to that copy.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
3. Object Code Incorporating Material from Library Header Files.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The object code form of an Application may incorporate material from\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
a header file that is part of the Library.  You may convey such object\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
code under terms of your choice, provided that, if the incorporated\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
material is not limited to numerical parameters, data structure\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
layouts and accessors, or small macros, inline functions and templates\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
(ten or fewer lines in length), you do both of the following:\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
a) Give prominent notice with each copy of the object code that the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
Library is used in it and that the Library and its use are\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
covered by this License.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
b) Accompany the object code with a copy of the GNU GPL and this license\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
document.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
4. Combined Works.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
You may convey a Combined Work under terms of your choice that,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
taken together, effectively do not restrict modification of the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
portions of the Library contained in the Combined Work and reverse\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
engineering for debugging such modifications, if you also do each of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the following:\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
a) Give prominent notice with each copy of the Combined Work that\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
the Library is used in it and that the Library and its use are\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
covered by this License.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
b) Accompany the Combined Work with a copy of the GNU GPL and this license\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
document.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
c) For a Combined Work that displays copyright notices during\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
execution, include the copyright notice for the Library among\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
these notices, as well as a reference directing the user to the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
copies of the GNU GPL and this license document.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
d) Do one of the following:\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
       }{\rtlch \ltrch\loch
0) Convey the Minimal Corresponding Source under the terms of this\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
       }{\rtlch \ltrch\loch
License, and the Corresponding Application Code in a form\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
       }{\rtlch \ltrch\loch
suitable for, and under terms that permit, the user to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
       }{\rtlch \ltrch\loch
recombine or relink the Application with a modified version of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
       }{\rtlch \ltrch\loch
the Linked Version to produce a modified Combined Work, in the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
       }{\rtlch \ltrch\loch
manner specified by section 6 of the GNU GPL for conveying\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
       }{\rtlch \ltrch\loch
Corresponding Source.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
       }{\rtlch \ltrch\loch
1) Use a suitable shared library mechanism for linking with the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
       }{\rtlch \ltrch\loch
Library.  A suitable mechanism is one that (a) uses at run time\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
       }{\rtlch \ltrch\loch
a copy of the Library already present on the user's computer\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
       }{\rtlch \ltrch\loch
system, and (b) will operate properly with a modified version\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
       }{\rtlch \ltrch\loch
of the Library that is interface-compatible with the Linked\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
       }{\rtlch \ltrch\loch
Version.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
e) Provide Installation Information, but only if you would otherwise\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
be required to provide such information under section 6 of the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
GNU GPL, and only to the extent that such information is\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
necessary to install and execute a modified version of the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
Combined Work produced by recombining or relinking the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
Application with a modified version of the Linked Version. (If\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
you use option 4d0, the Installation Information must accompany\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
the Minimal Corresponding Source and Corresponding Application\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
Code. If you use option 4d1, you must provide the Installation\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
Information in the manner specified by section 6 of the GNU GPL\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
for conveying Corresponding Source.)\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
5. Combined Libraries.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
You may place library facilities that are a work based on the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Library side by side in a single library together with other library\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
facilities that are not Applications and are not covered by this\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
License, and convey such a combined library under terms of your\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
choice, if you do both of the following:\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
a) Accompany the combined library with a copy of the same work based\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
on the Library, uncombined with any other library facilities,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
conveyed under the terms of this License.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
b) Give prominent notice with the combined library that part of it\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
is a work based on the Library, and explaining where to find the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
   }{\rtlch \ltrch\loch
accompanying uncombined form of the same work.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
6. Revised Versions of the GNU Lesser General Public License.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The Free Software Foundation may publish revised and/or new versions\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
of the GNU Lesser General Public License from time to time. Such new\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
versions will be similar in spirit to the present version, but may\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
differ in detail to address new problems or concerns.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Each version is given a distinguishing version number. If the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Library as you received it specifies that a certain numbered version\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
of the GNU Lesser General Public License "or any later version"\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
applies to it, you have the option of following the terms and\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
conditions either of that published version or of any later version\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
published by the Free Software Foundation. If the Library as you\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
received it does not specify a version number of the GNU Lesser\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
General Public License, you may choose any version of the GNU Lesser\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
General Public License ever published by the Free Software Foundation.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
If the Library as you received it specifies that a proxy can decide\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
whether future versions of the GNU Lesser General Public License shall\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
apply, that proxy's public statement of acceptance of any version is\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
permanent authorization for you to choose that version for the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Library.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
**************************************************************************\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
                    }{\rtlch \ltrch\loch
GNU GENERAL PUBLIC LICENSE\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
                       }{\rtlch \ltrch\loch
Version 3, 29 June 2007\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
 }{\rtlch \ltrch\loch
Copyright (C) 2007 Free Software Foundation, Inc. <http://fsf.org/>\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
 }{\rtlch \ltrch\loch
Everyone is permitted to copy and distribute verbatim copies\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
 }{\rtlch \ltrch\loch
of this license document, but changing it is not allowed.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
                            }{\rtlch \ltrch\loch
Preamble\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The GNU General Public License is a free, copyleft license for\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
software and other kinds of works.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The licenses for most software and other practical works are designed\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
to take away your freedom to share and change the works.  By contrast,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the GNU General Public License is intended to guarantee your freedom to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
share and change all versions of a program--to make sure it remains free\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
software for all its users.  We, the Free Software Foundation, use the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
GNU General Public License for most of our software; it applies also to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
any other work released this way by its authors.  You can apply it to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
your programs, too.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
When we speak of free software, we are referring to freedom, not\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
price.  Our General Public Licenses are designed to make sure that you\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
have the freedom to distribute copies of free software (and charge for\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
them if you wish), that you receive source code or can get it if you\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
want it, that you can change the software or use pieces of it in new\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
free programs, and that you know you can do these things.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
To protect your rights, we need to prevent others from denying you\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
these rights or asking you to surrender the rights.  Therefore, you have\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
certain responsibilities if you distribute copies of the software, or if\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
you modify it: responsibilities to respect the freedom of others.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
For example, if you distribute copies of such a program, whether\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
gratis or for a fee, you must pass on to the recipients the same\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
freedoms that you received.  You must make sure that they, too, receive\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
or can get the source code.  And you must show them these terms so they\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
know their rights.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Developers that use the GNU GPL protect your rights with two steps:\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
(1) assert copyright on the software, and (2) offer you this License\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
giving you legal permission to copy, distribute and/or modify it.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
For the developers' and authors' protection, the GPL clearly explains\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
that there is no warranty for this free software.  For both users' and\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
authors' sake, the GPL requires that modified versions be marked as\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
changed, so that their problems will not be attributed erroneously to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
authors of previous versions.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Some devices are designed to deny users access to install or run\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
modified versions of the software inside them, although the manufacturer\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
can do so.  This is fundamentally incompatible with the aim of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
protecting users' freedom to change the software.  The systematic\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
pattern of such abuse occurs in the area of products for individuals to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
use, which is precisely where it is most unacceptable.  Therefore, we\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
have designed this version of the GPL to prohibit the practice for those\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
products.  If such problems arise substantially in other domains, we\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
stand ready to extend this provision to those domains in future versions\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
of the GPL, as needed to protect the freedom of users.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Finally, every program is threatened constantly by software patents.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
States should not allow patents to restrict development and use of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
software on general-purpose computers, but in those that do, we wish to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
avoid the special danger that patents applied to a free program could\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
make it effectively proprietary.  To prevent this, the GPL assures that\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
patents cannot be used to render the program non-free.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The precise terms and conditions for copying, distribution and\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
modification follow.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
                       }{\rtlch \ltrch\loch
TERMS AND CONDITIONS\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
0. Definitions.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
"This License" refers to version 3 of the GNU General Public License.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
"Copyright" also means copyright-like laws that apply to other kinds of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
works, such as semiconductor masks.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
"The Program" refers to any copyrightable work licensed under this\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
License.  Each licensee is addressed as "you".  "Licensees" and\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
"recipients" may be individuals or organizations.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
To "modify" a work means to copy from or adapt all or part of the work\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
in a fashion requiring copyright permission, other than the making of an\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
exact copy.  The resulting work is called a "modified version" of the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
earlier work or a work "based on" the earlier work.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
A "covered work" means either the unmodified Program or a work based\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
on the Program.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
To "propagate" a work means to do anything with it that, without\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
permission, would make you directly or secondarily liable for\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
infringement under applicable copyright law, except executing it on a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
computer or modifying a private copy.  Propagation includes copying,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
distribution (with or without modification), making available to the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
public, and in some countries other activities as well.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
To "convey" a work means any kind of propagation that enables other\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
parties to make or receive copies.  Mere interaction with a user through\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
a computer network, with no transfer of a copy, is not conveying.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
An interactive user interface displays "Appropriate Legal Notices"\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
to the extent that it includes a convenient and prominently visible\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
feature that (1) displays an appropriate copyright notice, and (2)\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
tells the user that there is no warranty for the work (except to the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
extent that warranties are provided), that licensees may convey the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
work under this License, and how to view a copy of this License.  If\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the interface presents a list of user commands or options, such as a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
menu, a prominent item in the list meets this criterion.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
1. Source Code.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The "source code" for a work means the preferred form of the work\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
for making modifications to it.  "Object code" means any non-source\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
form of a work.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
A "Standard Interface" means an interface that either is an official\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
standard defined by a recognized standards body, or, in the case of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
interfaces specified for a particular programming language, one that\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
is widely used among developers working in that language.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The "System Libraries" of an executable work include anything, other\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
than the work as a whole, that (a) is included in the normal form of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
packaging a Major Component, but which is not part of that Major\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Component, and (b) serves only to enable use of the work with that\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Major Component, or to implement a Standard Interface for which an\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
implementation is available to the public in source code form.  A\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
"Major Component", in this context, means a major essential component\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
(kernel, window system, and so on) of the specific operating system\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
(if any) on which the executable work runs, or a compiler used to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
produce the work, or an object code interpreter used to run it.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The "Corresponding Source" for a work in object code form means all\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the source code needed to generate, install, and (for an executable\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
work) run the object code and to modify the work, including scripts to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
control those activities.  However, it does not include the work's\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
System Libraries, or general-purpose tools or generally available free\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
programs which are used unmodified in performing those activities but\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
which are not part of the work.  For example, Corresponding Source\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
includes interface definition files associated with source files for\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the work, and the source code for shared libraries and dynamically\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
linked subprograms that the work is specifically designed to require,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
such as by intimate data communication or control flow between those\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
subprograms and other parts of the work.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The Corresponding Source need not include anything that users\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
can regenerate automatically from other parts of the Corresponding\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Source.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The Corresponding Source for a work in source code form is that\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
same work.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
2. Basic Permissions.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
All rights granted under this License are granted for the term of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
copyright on the Program, and are irrevocable provided the stated\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
conditions are met.  This License explicitly affirms your unlimited\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
permission to run the unmodified Program.  The output from running a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
covered work is covered by this License only if the output, given its\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
content, constitutes a covered work.  This License acknowledges your\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
rights of fair use or other equivalent, as provided by copyright law.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
You may make, run and propagate covered works that you do not\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
convey, without conditions so long as your license otherwise remains\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
in force.  You may convey covered works to others for the sole purpose\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
of having them make modifications exclusively for you, or provide you\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
with facilities for running those works, provided that you comply with\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the terms of this License in conveying all material for which you do\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
not control copyright.  Those thus making or running the covered works\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
for you must do so exclusively on your behalf, under your direction\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
and control, on terms that prohibit them from making any copies of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
your copyrighted material outside their relationship with you.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Conveying under any other circumstances is permitted solely under\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the conditions stated below.  Sublicensing is not allowed; section 10\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
makes it unnecessary.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
3. Protecting Users' Legal Rights From Anti-Circumvention Law.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
No covered work shall be deemed part of an effective technological\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
measure under any applicable law fulfilling obligations under article\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
11 of the WIPO copyright treaty adopted on 20 December 1996, or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
similar laws prohibiting or restricting circumvention of such\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
measures.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
When you convey a covered work, you waive any legal power to forbid\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
circumvention of technological measures to the extent such circumvention\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
is effected by exercising rights under this License with respect to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the covered work, and you disclaim any intention to limit operation or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
modification of the work as a means of enforcing, against the work's\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
users, your or third parties' legal rights to forbid circumvention of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
technological measures.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
4. Conveying Verbatim Copies.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
You may convey verbatim copies of the Program's source code as you\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
receive it, in any medium, provided that you conspicuously and\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
appropriately publish on each copy an appropriate copyright notice;\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
keep intact all notices stating that this License and any\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
non-permissive terms added in accord with section 7 apply to the code;\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
keep intact all notices of the absence of any warranty; and give all\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
recipients a copy of this License along with the Program.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
You may charge any price or no price for each copy that you convey,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
and you may offer support or warranty protection for a fee.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
5. Conveying Modified Source Versions.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
You may convey a work based on the Program, or the modifications to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
produce it from the Program, in the form of source code under the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
terms of section 4, provided that you also meet all of these conditions:\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
a) The work must carry prominent notices stating that you modified\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
it, and giving a relevant date.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
b) The work must carry prominent notices stating that it is\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
released under this License and any conditions added under section\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
7.  This requirement modifies the requirement in section 4 to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
"keep intact all notices".\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
c) You must license the entire work, as a whole, under this\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
License to anyone who comes into possession of a copy.  This\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
License will therefore apply, along with any applicable section 7\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
additional terms, to the whole of the work, and all its parts,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
regardless of how they are packaged.  This License gives no\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
permission to license the work in any other way, but it does not\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
invalidate such permission if you have separately received it.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
d) If the work has interactive user interfaces, each must display\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
Appropriate Legal Notices; however, if the Program has interactive\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
interfaces that do not display Appropriate Legal Notices, your\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
work need not make them do so.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
A compilation of a covered work with other separate and independent\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
works, which are not by their nature extensions of the covered work,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
and which are not combined with it such as to form a larger program,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
in or on a volume of a storage or distribution medium, is called an\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
"aggregate" if the compilation and its resulting copyright are not\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
used to limit the access or legal rights of the compilation's users\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
beyond what the individual works permit.  Inclusion of a covered work\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
in an aggregate does not cause this License to apply to the other\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
parts of the aggregate.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
6. Conveying Non-Source Forms.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
You may convey a covered work in object code form under the terms\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
of sections 4 and 5, provided that you also convey the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
machine-readable Corresponding Source under the terms of this License,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
in one of these ways:\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
a) Convey the object code in, or embodied in, a physical product\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
(including a physical distribution medium), accompanied by the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
Corresponding Source fixed on a durable physical medium\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
customarily used for software interchange.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
b) Convey the object code in, or embodied in, a physical product\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
(including a physical distribution medium), accompanied by a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
written offer, valid for at least three years and valid for as\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
long as you offer spare parts or customer support for that product\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
model, to give anyone who possesses the object code either (1) a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
copy of the Corresponding Source for all the software in the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
product that is covered by this License, on a durable physical\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
medium customarily used for software interchange, for a price no\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
more than your reasonable cost of physically performing this\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
conveying of source, or (2) access to copy the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
Corresponding Source from a network server at no charge.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
c) Convey individual copies of the object code with a copy of the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
written offer to provide the Corresponding Source.  This\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
alternative is allowed only occasionally and noncommercially, and\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
only if you received the object code with such an offer, in accord\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
with subsection 6b.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
d) Convey the object code by offering access from a designated\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
place (gratis or for a charge), and offer equivalent access to the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
Corresponding Source in the same way through the same place at no\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
further charge.  You need not require recipients to copy the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
Corresponding Source along with the object code.  If the place to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
copy the object code is a network server, the Corresponding Source\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
may be on a different server (operated by you or a third party)\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
that supports equivalent copying facilities, provided you maintain\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
clear directions next to the object code saying where to find the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
Corresponding Source.  Regardless of what server hosts the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
Corresponding Source, you remain obligated to ensure that it is\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
available for as long as needed to satisfy these requirements.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
e) Convey the object code using peer-to-peer transmission, provided\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
you inform other peers where the object code and Corresponding\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
Source of the work are being offered to the general public at no\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
charge under subsection 6d.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
A separable portion of the object code, whose source code is excluded\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
from the Corresponding Source as a System Library, need not be\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
included in conveying the object code work.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
A "User Product" is either (1) a "consumer product", which means any\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
tangible personal property which is normally used for personal, family,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
or household purposes, or (2) anything designed or sold for incorporation\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
into a dwelling.  In determining whether a product is a consumer product,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
doubtful cases shall be resolved in favor of coverage.  For a particular\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
product received by a particular user, "normally used" refers to a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
typical or common use of that class of product, regardless of the status\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
of the particular user or of the way in which the particular user\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
actually uses, or expects or is expected to use, the product.  A product\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
is a consumer product regardless of whether the product has substantial\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
commercial, industrial or non-consumer uses, unless such uses represent\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the only significant mode of use of the product.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
"Installation Information" for a User Product means any methods,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
procedures, authorization keys, or other information required to install\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
and execute modified versions of a covered work in that User Product from\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
a modified version of its Corresponding Source.  The information must\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
suffice to ensure that the continued functioning of the modified object\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
code is in no case prevented or interfered with solely because\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
modification has been made.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
If you convey an object code work under this section in, or with, or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
specifically for use in, a User Product, and the conveying occurs as\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
part of a transaction in which the right of possession and use of the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
User Product is transferred to the recipient in perpetuity or for a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
fixed term (regardless of how the transaction is characterized), the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Corresponding Source conveyed under this section must be accompanied\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
by the Installation Information.  But this requirement does not apply\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
if neither you nor any third party retains the ability to install\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
modified object code on the User Product (for example, the work has\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
been installed in ROM).\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The requirement to provide Installation Information does not include a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
requirement to continue to provide support service, warranty, or updates\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
for a work that has been modified or installed by the recipient, or for\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the User Product in which it has been modified or installed.  Access to a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
network may be denied when the modification itself materially and\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
adversely affects the operation of the network or violates the rules and\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
protocols for communication across the network.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Corresponding Source conveyed, and Installation Information provided,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
in accord with this section must be in a format that is publicly\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
documented (and with an implementation available to the public in\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
source code form), and must require no special password or key for\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
unpacking, reading or copying.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
7. Additional Terms.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
"Additional permissions" are terms that supplement the terms of this\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
License by making exceptions from one or more of its conditions.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Additional permissions that are applicable to the entire Program shall\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
be treated as though they were included in this License, to the extent\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
that they are valid under applicable law.  If additional permissions\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
apply only to part of the Program, that part may be used separately\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
under those permissions, but the entire Program remains governed by\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
this License without regard to the additional permissions.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
When you convey a copy of a covered work, you may at your option\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
remove any additional permissions from that copy, or from any part of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
it.  (Additional permissions may be written to require their own\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
removal in certain cases when you modify the work.)  You may place\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
additional permissions on material, added by you to a covered work,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
for which you have or can give appropriate copyright permission.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Notwithstanding any other provision of this License, for material you\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
add to a covered work, you may (if authorized by the copyright holders of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
that material) supplement the terms of this License with terms:\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
a) Disclaiming warranty or limiting liability differently from the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
terms of sections 15 and 16 of this License; or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
b) Requiring preservation of specified reasonable legal notices or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
author attributions in that material or in the Appropriate Legal\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
Notices displayed by works containing it; or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
c) Prohibiting misrepresentation of the origin of that material, or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
requiring that modified versions of such material be marked in\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
reasonable ways as different from the original version; or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
d) Limiting the use for publicity purposes of names of licensors or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
authors of the material; or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
e) Declining to grant rights under trademark law for use of some\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
trade names, trademarks, or service marks; or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
f) Requiring indemnification of licensors and authors of that\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
material by anyone who conveys the material (or modified versions of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
it) with contractual assumptions of liability to the recipient, for\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
any liability that these contractual assumptions directly impose on\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
those licensors and authors.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
All other non-permissive additional terms are considered "further\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
restrictions" within the meaning of section 10.  If the Program as you\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
received it, or any part of it, contains a notice stating that it is\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
governed by this License along with a term that is a further\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
restriction, you may remove that term.  If a license document contains\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
a further restriction but permits relicensing or conveying under this\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
License, you may add to a covered work material governed by the terms\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
of that license document, provided that the further restriction does\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
not survive such relicensing or conveying.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
If you add terms to a covered work in accord with this section, you\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
must place, in the relevant source files, a statement of the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
additional terms that apply to those files, or a notice indicating\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
where to find the applicable terms.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Additional terms, permissive or non-permissive, may be stated in the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
form of a separately written license, or stated as exceptions;\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the above requirements apply either way.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
8. Termination.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
You may not propagate or modify a covered work except as expressly\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
provided under this License.  Any attempt otherwise to propagate or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
modify it is void, and will automatically terminate your rights under\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
this License (including any patent licenses granted under the third\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
paragraph of section 11).\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
However, if you cease all violation of this License, then your\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
license from a particular copyright holder is reinstated (a)\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
provisionally, unless and until the copyright holder explicitly and\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
finally terminates your license, and (b) permanently, if the copyright\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
holder fails to notify you of the violation by some reasonable means\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
prior to 60 days after the cessation.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Moreover, your license from a particular copyright holder is\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
reinstated permanently if the copyright holder notifies you of the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
violation by some reasonable means, this is the first time you have\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
received notice of violation of this License (for any work) from that\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
copyright holder, and you cure the violation prior to 30 days after\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
your receipt of the notice.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Termination of your rights under this section does not terminate the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
licenses of parties who have received copies or rights from you under\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
this License.  If your rights have been terminated and not permanently\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
reinstated, you do not qualify to receive new licenses for the same\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
material under section 10.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
9. Acceptance Not Required for Having Copies.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
You are not required to accept this License in order to receive or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
run a copy of the Program.  Ancillary propagation of a covered work\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
occurring solely as a consequence of using peer-to-peer transmission\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
to receive a copy likewise does not require acceptance.  However,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
nothing other than this License grants you permission to propagate or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
modify any covered work.  These actions infringe copyright if you do\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
not accept this License.  Therefore, by modifying or propagating a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
covered work, you indicate your acceptance of this License to do so.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
10. Automatic Licensing of Downstream Recipients.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Each time you convey a covered work, the recipient automatically\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
receives a license from the original licensors, to run, modify and\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
propagate that work, subject to this License.  You are not responsible\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
for enforcing compliance by third parties with this License.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
An "entity transaction" is a transaction transferring control of an\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
organization, or substantially all assets of one, or subdividing an\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
organization, or merging organizations.  If propagation of a covered\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
work results from an entity transaction, each party to that\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
transaction who receives a copy of the work also receives whatever\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
licenses to the work the party's predecessor in interest had or could\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
give under the previous paragraph, plus a right to possession of the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Corresponding Source of the work from the predecessor in interest, if\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the predecessor has it or can get it with reasonable efforts.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
You may not impose any further restrictions on the exercise of the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
rights granted or affirmed under this License.  For example, you may\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
not impose a license fee, royalty, or other charge for exercise of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
rights granted under this License, and you may not initiate litigation\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
(including a cross-claim or counterclaim in a lawsuit) alleging that\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
any patent claim is infringed by making, using, selling, offering for\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
sale, or importing the Program or any portion of it.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
11. Patents.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
A "contributor" is a copyright holder who authorizes use under this\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
License of the Program or a work on which the Program is based.  The\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
work thus licensed is called the contributor's "contributor version".\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
A contributor's "essential patent claims" are all patent claims\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
owned or controlled by the contributor, whether already acquired or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
hereafter acquired, that would be infringed by some manner, permitted\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
by this License, of making, using, or selling its contributor version,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
but do not include claims that would be infringed only as a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
consequence of further modification of the contributor version.  For\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
purposes of this definition, "control" includes the right to grant\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
patent sublicenses in a manner consistent with the requirements of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
this License.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Each contributor grants you a non-exclusive, worldwide, royalty-free\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
patent license under the contributor's essential patent claims, to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
make, use, sell, offer for sale, import and otherwise run, modify and\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
propagate the contents of its contributor version.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
In the following three paragraphs, a "patent license" is any express\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
agreement or commitment, however denominated, not to enforce a patent\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
(such as an express permission to practice a patent or covenant not to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
sue for patent infringement).  To "grant" such a patent license to a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
party means to make such an agreement or commitment not to enforce a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
patent against the party.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
If you convey a covered work, knowingly relying on a patent license,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
and the Corresponding Source of the work is not available for anyone\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
to copy, free of charge and under the terms of this License, through a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
publicly available network server or other readily accessible means,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
then you must either (1) cause the Corresponding Source to be so\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
available, or (2) arrange to deprive yourself of the benefit of the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
patent license for this particular work, or (3) arrange, in a manner\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
consistent with the requirements of this License, to extend the patent\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
license to downstream recipients.  "Knowingly relying" means you have\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
actual knowledge that, but for the patent license, your conveying the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
covered work in a country, or your recipient's use of the covered work\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
in a country, would infringe one or more identifiable patents in that\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
country that you have reason to believe are valid.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
If, pursuant to or in connection with a single transaction or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
arrangement, you convey, or propagate by procuring conveyance of, a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
covered work, and grant a patent license to some of the parties\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
receiving the covered work authorizing them to use, propagate, modify\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
or convey a specific copy of the covered work, then the patent license\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
you grant is automatically extended to all recipients of the covered\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
work and works based on it.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
A patent license is "discriminatory" if it does not include within\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the scope of its coverage, prohibits the exercise of, or is\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
conditioned on the non-exercise of one or more of the rights that are\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
specifically granted under this License.  You may not convey a covered\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
work if you are a party to an arrangement with a third party that is\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
in the business of distributing software, under which you make payment\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
to the third party based on the extent of your activity of conveying\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the work, and under which the third party grants, to any of the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
parties who would receive the covered work from you, a discriminatory\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
patent license (a) in connection with copies of the covered work\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
conveyed by you (or copies made from those copies), or (b) primarily\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
for and in connection with specific products or compilations that\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
contain the covered work, unless you entered into that arrangement,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
or that patent license was granted, prior to 28 March 2007.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Nothing in this License shall be construed as excluding or limiting\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
any implied license or other defenses to infringement that may\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
otherwise be available to you under applicable patent law.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
12. No Surrender of Others' Freedom.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
If conditions are imposed on you (whether by court order, agreement or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
otherwise) that contradict the conditions of this License, they do not\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
excuse you from the conditions of this License.  If you cannot convey a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
covered work so as to satisfy simultaneously your obligations under this\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
License and any other pertinent obligations, then as a consequence you may\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
not convey it at all.  For example, if you agree to terms that obligate you\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
to collect a royalty for further conveying from those to whom you convey\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the Program, the only way you could satisfy both those terms and this\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
License would be to refrain entirely from conveying the Program.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
13. Use with the GNU Affero General Public License.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Notwithstanding any other provision of this License, you have\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
permission to link or combine any covered work with a work licensed\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
under version 3 of the GNU Affero General Public License into a single\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
combined work, and to convey the resulting work.  The terms of this\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
License will continue to apply to the part which is the covered work,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
but the special requirements of the GNU Affero General Public License,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
section 13, concerning interaction through a network will apply to the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
combination as such.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
14. Revised Versions of this License.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The Free Software Foundation may publish revised and/or new versions of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the GNU General Public License from time to time.  Such new versions will\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
be similar in spirit to the present version, but may differ in detail to\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
address new problems or concerns.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Each version is given a distinguishing version number.  If the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Program specifies that a certain numbered version of the GNU General\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Public License "or any later version" applies to it, you have the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
option of following the terms and conditions either of that numbered\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
version or of any later version published by the Free Software\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Foundation.  If the Program does not specify a version number of the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
GNU General Public License, you may choose any version ever published\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
by the Free Software Foundation.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
If the Program specifies that a proxy can decide which future\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
versions of the GNU General Public License can be used, that proxy's\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
public statement of acceptance of a version permanently authorizes you\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
to choose that version for the Program.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
Later license versions may give you additional or different\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
permissions.  However, no additional obligations are imposed on any\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
author or copyright holder as a result of your choosing to follow a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
later version.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
15. Disclaimer of Warranty.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
THERE IS NO WARRANTY FOR THE PROGRAM, TO THE EXTENT PERMITTED BY\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
APPLICABLE LAW.  EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
HOLDERS AND/OR OTHER PARTIES PROVIDE THE PROGRAM "AS IS" WITHOUT WARRANTY\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
OF ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
PURPOSE.  THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE PROGRAM\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
IS WITH YOU.  SHOULD THE PROGRAM PROVE DEFECTIVE, YOU ASSUME THE COST OF\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
ALL NECESSARY SERVICING, REPAIR OR CORRECTION.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
16. Limitation of Liability.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MODIFIES AND/OR CONVEYS\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
THE PROGRAM AS PERMITTED ABOVE, BE LIABLE TO YOU FOR DAMAGES, INCLUDING ANY\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF THE\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
USE OR INABILITY TO USE THE PROGRAM (INCLUDING BUT NOT LIMITED TO LOSS OF\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
DATA OR DATA BEING RENDERED INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
PARTIES OR A FAILURE OF THE PROGRAM TO OPERATE WITH ANY OTHER PROGRAMS),\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
SUCH DAMAGES.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
17. Interpretation of Sections 15 and 16.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
If the disclaimer of warranty and limitation of liability provided\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
above cannot be given local legal effect according to their terms,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
reviewing courts shall apply local law that most closely approximates\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
an absolute waiver of all civil liability in connection with the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Program, unless a warranty or assumption of liability accompanies a\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
copy of the Program in return for a fee.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
                     }{\rtlch \ltrch\loch
END OF TERMS AND CONDITIONS\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
            }{\rtlch \ltrch\loch
How to Apply These Terms to Your New Programs\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
If you develop a new program, and you want it to be of the greatest\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
possible use to the public, the best way to achieve this is to make it\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
free software which everyone can redistribute and change under these terms.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
To do so, attach the following notices to the program.  It is safest\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
to attach them to the start of each source file to most effectively\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
state the exclusion of warranty; and each file should have at least\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the "copyright" line and a pointer to where the full notice is found.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
<one line to give the program's name and a brief idea of what it does.>\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
Copyright (C) <year>  <name of author>\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
This program is free software: you can redistribute it and/or modify\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
it under the terms of the GNU General Public License as published by\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
the Free Software Foundation, either version 3 of the License, or\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
(at your option) any later version.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
This program is distributed in the hope that it will be useful,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
but WITHOUT ANY WARRANTY; without even the implied warranty of\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
GNU General Public License for more details.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
You should have received a copy of the GNU General Public License\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
along with this program.  If not, see <http://www.gnu.org/licenses/>.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Also add information on how to contact you by electronic and paper mail.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
If the program does terminal interaction, make it output a short\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
notice like this when it starts in an interactive mode:\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
<program>  Copyright (C) <year>  <name of author>\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
This program comes with ABSOLUTELY NO WARRANTY; for details type `show w'.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
This is free software, and you are welcome to redistribute it\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
    }{\rtlch \ltrch\loch
under certain conditions; type `show c' for details.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
The hypothetical commands `show w' and `show c' should show the appropriate\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
parts of the General Public License.  Of course, your program's commands\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
might be different; for a GUI interface, you would use an "about box".\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
You should also get your employer (if you work as a programmer) or school,\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
if any, to sign a "copyright disclaimer" for the program, if necessary.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
For more information on this, and how to apply and follow the GNU GPL, see\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
<http://www.gnu.org/licenses/>.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch
  }{\rtlch \ltrch\loch
The GNU General Public License does not permit incorporating your program\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
into proprietary programs.  If your program is a subroutine library, you\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
may consider it more useful to permit linking proprietary applications with\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
the library.  If this is what you want to do, use the GNU Lesser General\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
Public License instead of this License.  But first, please read\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
<http://www.gnu.org/philosophy/why-not-lgpl.html>.\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
\u13\'0d}
\par \pard\plain \s0\ql\nowidctlpar\ltrpar{\*\hyphen2\hyphlead2\hyphtrail2\hyphmax0}\cf1\kerning1\dbch\af7\langfe2052\dbch\af11\afs24\alang1081\loch\f5\fs24\lang1033{\rtlch \ltrch\loch
**************************************************************************}
\par }